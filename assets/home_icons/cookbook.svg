<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_165_1885)">
<rect x="6" y="6" width="48" height="48" rx="10" fill="url(#paint0_linear_165_1885)"/>
</g>
<g filter="url(#filter1_d_165_1885)">
<g filter="url(#filter2_i_165_1885)">
<path d="M21.9271 15.2729C19.71 15.2729 17.9126 17.0712 17.9126 19.2895V40.711C17.9126 42.9293 19.71 44.7275 21.9271 44.7275H37.9853C40.2026 44.7275 41.9999 42.9293 41.9999 40.711V19.2895C41.9999 17.0712 40.2026 15.2729 37.9853 15.2729H21.9271Z" fill="#FFFEE3"/>
</g>
<g filter="url(#filter3_i_165_1885)">
<rect x="15.8184" y="23.6885" width="5.23636" height="2.1039" rx="1.05195" fill="#F8E223"/>
<rect x="15.8184" y="28.9478" width="5.23636" height="2.1039" rx="1.05195" fill="#F8E223"/>
<rect x="15.8184" y="34.208" width="5.23636" height="2.1039" rx="1.05195" fill="#F8E223"/>
</g>
<g filter="url(#filter4_i_165_1885)">
<path d="M24.7658 30.5856C24.993 30.7915 25.1734 31.0442 25.2946 31.3264C25.4158 31.6086 25.475 31.9138 25.4682 32.2211V33.1551H34.4439V32.2211C34.437 31.9138 34.4961 31.6087 34.6172 31.3264C34.7383 31.0442 34.9186 30.7915 35.1458 30.5856C35.5672 30.2226 35.8851 29.7536 36.067 29.2268C36.2489 28.6999 36.2883 28.134 36.1812 27.5868C36.0741 27.0396 35.8243 26.5308 35.4573 26.1123C35.0903 25.6939 34.6193 25.3808 34.0925 25.205C33.6018 25.0375 33.0769 24.9972 32.5666 25.0878C32.2798 24.6572 31.8917 24.3043 31.4368 24.0602C30.9819 23.8162 30.474 23.6885 29.9583 23.6885C29.4425 23.6885 28.9347 23.8162 28.4797 24.0602C28.0248 24.3043 27.6368 24.6572 27.3499 25.0878C26.8396 24.9972 26.3148 25.0376 25.8241 25.205C25.2968 25.3802 24.8253 25.6929 24.4578 26.1111C24.0903 26.5293 23.8399 27.038 23.7323 27.5852C23.6247 28.1325 23.6637 28.6986 23.8452 29.2258C24.0268 29.7529 24.3445 30.2222 24.7658 30.5856ZM31.7601 29.0079C31.7869 28.8941 31.8565 28.7952 31.9544 28.7321C32.0523 28.669 32.1708 28.6465 32.2849 28.6694C32.399 28.6923 32.4998 28.7588 32.566 28.855C32.6322 28.9511 32.6586 29.0692 32.6398 29.1846L32.191 31.4385C32.171 31.541 32.1162 31.6333 32.036 31.6998C31.9558 31.7662 31.8551 31.8026 31.7512 31.8027C31.721 31.8029 31.6909 31.7999 31.6614 31.7937C31.6036 31.7819 31.5488 31.7588 31.4999 31.7256C31.4511 31.6925 31.4092 31.65 31.3767 31.6006C31.3442 31.5512 31.3217 31.4959 31.3105 31.4377C31.2992 31.3796 31.2996 31.3198 31.3114 31.2618L31.7601 29.0079ZM29.5072 28.1964C29.5072 28.0769 29.5545 27.9622 29.6387 27.8777C29.7228 27.7932 29.837 27.7457 29.956 27.7457C30.075 27.7457 30.1892 27.7932 30.2734 27.8777C30.3575 27.9622 30.4048 28.0769 30.4048 28.1964V31.352C30.4048 31.4715 30.3575 31.5862 30.2734 31.6707C30.1892 31.7553 30.075 31.8027 29.956 31.8027C29.837 31.8027 29.7228 31.7553 29.6387 31.6707C29.5545 31.5862 29.5072 31.4715 29.5072 31.352V28.1964ZM27.6223 28.6562C27.6801 28.6442 27.7397 28.6438 27.7977 28.6551C27.8556 28.6664 27.9108 28.6891 27.9599 28.722C28.009 28.7549 28.0511 28.7973 28.0838 28.8466C28.1164 28.896 28.139 28.9514 28.1501 29.0097L28.5989 31.2636C28.6222 31.3802 28.5987 31.5014 28.5335 31.6007C28.4683 31.7 28.3666 31.7694 28.2506 31.7937C28.2211 31.7999 28.191 31.8029 28.1609 31.8027C28.0575 31.8027 27.9573 31.7667 27.8773 31.701C27.7972 31.6353 27.7422 31.5439 27.7215 31.4421L27.2727 29.1882C27.2489 29.0711 27.2723 28.9493 27.3379 28.8495C27.4035 28.7498 27.5058 28.6802 27.6223 28.6562Z" fill="#E01010"/>
<path d="M25.4683 34.0566V34.9614C25.4691 35.3195 25.6113 35.6627 25.8637 35.9156C26.1161 36.1685 26.4581 36.3106 26.8146 36.3106H33.0976C33.4541 36.3106 33.7961 36.1685 34.0485 35.9156C34.3009 35.6627 34.4431 35.3195 34.4439 34.9614V34.0566H25.4683Z" fill="#E01010"/>
</g>
</g>
<defs>
<filter id="filter0_d_165_1885" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1885"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1885" result="shape"/>
</filter>
<filter id="filter1_d_165_1885" x="15.8184" y="15.2729" width="30.1816" height="33.4546" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1885"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1885" result="shape"/>
</filter>
<filter id="filter2_i_165_1885" x="17.9126" y="15.2729" width="25.0874" height="30.4546" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_165_1885"/>
</filter>
<filter id="filter3_i_165_1885" x="14.8184" y="23.6885" width="6.23633" height="13.6235" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_165_1885"/>
</filter>
<filter id="filter4_i_165_1885" x="23.6729" y="23.6885" width="14.5674" height="14.6221" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_165_1885"/>
</filter>
<linearGradient id="paint0_linear_165_1885" x1="30.2727" y1="9" x2="30.2727" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#B96AE8"/>
<stop offset="1" stop-color="#6C2FAD"/>
</linearGradient>
</defs>
</svg>
