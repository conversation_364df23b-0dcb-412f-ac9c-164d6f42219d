<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_165_1937)">
<rect x="6" y="6" width="48" height="48" rx="10" fill="url(#paint0_linear_165_1937)"/>
<rect x="6.5" y="6.5" width="47" height="47" rx="9.5" stroke="url(#paint1_linear_165_1937)" stroke-opacity="0.4"/>
</g>
<path opacity="0.5" d="M53.6324 33.7703L53.6648 41.7831C53.6648 48.007 48.6199 53.0519 42.396 53.0519H29.5053L19.7251 43.2718L29.5958 13.186L41.0944 24.6846L43.1372 23.2435L53.2511 33.389L53.6324 33.7703Z" fill="url(#paint2_linear_165_1937)"/>
<g filter="url(#filter1_i_165_1937)">
<path d="M37.6868 42.1565C37.5323 42.1565 37.3817 42.1185 37.2389 42.0435L29.5076 37.9789C29.3114 37.8757 29.0902 37.8211 28.8684 37.8211C28.6467 37.8211 28.4255 37.8757 28.2293 37.9789L20.498 42.0435C20.3553 42.1185 20.2046 42.1565 20.0501 42.1565C19.7709 42.1566 19.4944 42.0263 19.3104 41.808C19.1273 41.5908 19.0533 41.3143 19.1022 41.0293L20.5787 32.4205C20.6551 31.9749 20.5074 31.5203 20.1837 31.2047L13.929 25.1079C13.6608 24.8465 13.5698 24.4779 13.6855 24.1218C13.8012 23.7657 14.0915 23.5209 14.462 23.4671L23.1058 22.211C23.5533 22.146 23.94 21.8651 24.1401 21.4597L28.0057 13.6271C28.1714 13.2913 28.4939 13.0908 28.8683 13.0908C29.2428 13.0908 29.5654 13.2913 29.7309 13.6271L33.5965 21.4597C33.7967 21.8652 34.1834 22.1461 34.6309 22.211L43.2747 23.4671C43.6452 23.5209 43.9355 23.7657 44.0512 24.1218C44.1669 24.4779 44.0759 24.8465 43.8078 25.1079L37.553 31.2047C37.2293 31.5204 37.0816 31.9749 37.1581 32.4206L38.6345 41.0293C38.6835 41.3142 38.6094 41.5908 38.4263 41.8079C38.2425 42.0262 37.966 42.1565 37.6868 42.1565Z" fill="url(#paint3_linear_165_1937)"/>
</g>
<defs>
<filter id="filter0_d_165_1937" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1937"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1937" result="shape"/>
</filter>
<filter id="filter1_i_165_1937" x="13.6362" y="13.0908" width="32.4644" height="31.0659" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_165_1937"/>
</filter>
<linearGradient id="paint0_linear_165_1937" x1="30" y1="6" x2="30" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#6EAFFD"/>
<stop offset="1" stop-color="#1B3594"/>
</linearGradient>
<linearGradient id="paint1_linear_165_1937" x1="30" y1="6" x2="30" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D3896"/>
<stop offset="1" stop-color="#6CACFB"/>
</linearGradient>
<linearGradient id="paint2_linear_165_1937" x1="22.7227" y1="24.5492" x2="47.6642" y2="49.4907" gradientUnits="userSpaceOnUse">
<stop stop-color="#212385"/>
<stop offset="1" stop-color="#001E91" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_165_1937" x1="28.8683" y1="12.3647" x2="28.8683" y2="41.9032" gradientUnits="userSpaceOnUse">
<stop offset="0.005" stop-color="#FDE97E"/>
<stop offset="1" stop-color="#FBB728"/>
</linearGradient>
</defs>
</svg>
