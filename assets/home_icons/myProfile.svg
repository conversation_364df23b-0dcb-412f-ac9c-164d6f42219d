<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_311_5310)">
<rect x="6" y="6" width="48" height="48" rx="10" fill="url(#paint0_linear_311_5310)"/>
<rect x="6.5" y="6.5" width="47" height="47" rx="9.5" stroke="url(#paint1_linear_311_5310)" stroke-opacity="0.4"/>
</g>
<g clip-path="url(#clip0_311_5310)">
<g filter="url(#filter1_i_311_5310)">
<path d="M28.9752 14.1816H31.5084C31.5442 14.2781 31.6289 14.2379 31.6932 14.2441C32.8744 14.3541 34.0341 14.576 35.1585 14.9555C39.3569 16.3741 42.4642 19.0661 44.4627 23.0178C45.304 24.6818 45.8257 26.4502 45.9815 28.3176C45.8516 28.2848 45.7855 28.1717 45.7002 28.0864C43.0849 25.4742 40.472 22.8595 37.8566 20.2479C37.7645 20.1558 37.6947 20.0316 37.5519 19.9994C37.513 19.9537 37.4722 19.9092 37.4357 19.8616C36.587 18.744 35.4459 18.265 34.065 18.3509C33.8387 18.3651 33.6935 18.3342 33.526 18.1778C32.613 17.3242 31.5312 16.8853 30.2721 16.9113C29.0271 16.9119 27.9392 17.3223 27.0343 18.1822C26.8797 18.3293 26.7382 18.4084 26.5218 18.3732C25.9501 18.2798 25.3968 18.3861 24.8578 18.5722C23.4175 19.0686 22.4755 20.3815 22.5528 21.7908C22.6072 22.7878 23.0998 23.5524 23.849 24.1749C24.2953 24.7108 24.8046 25.1849 25.3152 25.6578C25.322 26.2425 25.3288 26.8273 25.335 27.412C25.2763 27.7483 25.2633 28.0833 25.3115 28.4245C25.3511 28.707 25.3535 28.995 25.3727 29.2806C25.0049 29.3931 24.6773 29.6107 24.6347 29.9915C24.5518 30.7419 24.482 31.5182 25.1527 32.0956C25.5297 32.5332 25.9377 32.9387 26.4081 33.278C26.5299 33.4796 26.6535 33.6805 26.7734 33.8832C27.1115 34.4568 27.5974 34.9019 28.0561 35.3716C28.0462 35.4829 28.0319 35.5942 28.027 35.7061C28.0103 36.1054 28.0579 36.5059 27.9992 36.9046C27.8131 36.8916 27.7297 36.9547 27.7402 37.1636C27.7569 37.5097 27.7402 37.8577 27.7371 38.2051C27.0163 38.2088 26.2956 38.2101 25.5748 38.2175C25.0742 38.2231 24.5932 38.3362 24.1482 38.5575C22.7493 39.2541 21.9865 40.3797 21.926 41.9535C21.9074 42.4288 21.9229 42.9054 21.9229 43.3962C21.8549 43.3745 21.8339 43.3714 21.8172 43.3616C21.7554 43.3251 21.6936 43.2874 21.6342 43.2472C17.7263 40.6084 15.3768 36.9714 14.6245 32.3082C14.5701 31.9707 14.5936 31.6202 14.4545 31.297V28.7021C14.5503 28.6674 14.5133 28.5821 14.5201 28.5179C14.5924 27.8639 14.6845 27.213 14.8291 26.5708C16.2737 20.141 21.5007 15.2912 27.9955 14.3473C28.3225 14.2997 28.6624 14.3176 28.9752 14.1816Z" fill="#EE233E"/>
<path d="M37.5519 19.9993C37.6947 20.0314 37.7645 20.1557 37.8566 20.2478C40.472 22.86 43.0855 25.474 45.7002 28.0862C45.7855 28.1715 45.8516 28.2847 45.9815 28.3174L45.9771 28.3168C45.9889 28.3415 46.0006 28.3662 46.013 28.3904C46.0705 28.5084 45.9604 28.6611 46.0909 28.7637V31.2968C46.0247 31.3147 46.0309 31.3697 46.029 31.4192C46.0105 31.9335 45.9301 32.4422 45.8269 32.9429C44.9053 37.4298 42.5291 40.926 38.672 43.4077C38.6652 43.0078 38.6571 42.6085 38.6522 42.2086C38.6324 40.5471 37.9209 39.3052 36.4201 38.5517C35.8415 38.2612 35.2135 38.1988 34.5749 38.1889C33.9828 38.1796 33.3893 38.2383 32.799 38.1543C32.8009 37.8353 32.783 37.5151 32.8126 37.1987C32.8336 36.973 32.7384 36.8921 32.5468 36.8599C32.5518 36.3481 32.5796 35.8363 32.5314 35.3257C33.4611 34.5376 34.0841 33.5381 34.5335 32.4199C34.5793 32.3062 34.6269 32.2536 34.7721 32.2506C35.4391 32.2363 35.831 31.8574 35.8959 31.1966C35.9169 30.9828 35.9392 30.7689 35.9633 30.555C36.0393 29.8868 35.7945 29.4925 35.1591 29.2625C35.211 28.6599 35.2734 28.0578 35.1467 27.4564L35.143 27.4533C35.1597 27.4452 35.1758 27.4378 35.1925 27.4298C35.2506 27.3346 35.224 27.2295 35.2246 27.1288C35.2271 26.4717 35.2339 25.814 35.2209 25.1576C35.2166 24.9437 35.2629 24.8231 35.4916 24.7706C35.9664 24.6612 36.3892 24.4257 36.7625 24.1191C37.5074 23.5078 37.9549 22.7394 38.0007 21.7511C38.0303 21.1138 37.843 20.5457 37.5507 19.998L37.5519 19.9993Z" fill="#9A1D1F"/>
<path d="M21.9235 43.3961C21.9235 42.9053 21.9081 42.4287 21.9266 41.9534C21.9872 40.3796 22.75 39.2547 24.1488 38.5574C24.5939 38.3355 25.0754 38.2224 25.5755 38.2174C26.2962 38.21 27.017 38.2088 27.7377 38.2051C28.4579 38.2088 29.1792 38.2273 29.8987 38.21C30.1979 38.2026 30.2808 38.2929 30.2789 38.5908C30.2672 40.9996 30.2721 43.4091 30.2721 45.8185C25.0816 45.8185 21.8549 43.3751 21.9229 43.3967L21.9235 43.3961Z" fill="#FFFEFE"/>
<path d="M30.2727 45.8187C30.2727 43.4093 30.2677 41.0004 30.2795 38.591C30.2807 38.293 30.1979 38.2028 29.8993 38.2102C29.1792 38.2275 28.4584 38.2096 27.7383 38.2053C27.7414 37.8579 27.7587 37.5099 27.742 37.1631C27.7315 36.9536 27.8149 36.8905 28.001 36.9041C28.757 36.9103 29.513 36.9165 30.2689 36.9233C30.2708 37.2608 30.2782 37.5983 30.2714 37.9351C30.2683 38.1033 30.3258 38.1744 30.5007 38.1719C31.2672 38.162 32.0337 38.1601 32.8002 38.1552C33.3905 38.2399 33.984 38.1812 34.5761 38.1898C35.2153 38.1997 35.8427 38.2621 36.4213 38.5527C37.9221 39.3068 38.6336 40.5486 38.6534 42.2095C38.6583 42.6094 38.6664 43.0087 38.6732 43.4087C38.573 43.5144 38.4457 43.5793 38.3208 43.651C37.5815 44.0738 36.8231 44.4564 36.0281 44.7642C34.8493 45.221 33.6359 45.5369 32.3811 45.7038C32.0912 45.7427 31.7883 45.6963 31.5095 45.8175H30.2739L30.2727 45.8187ZM31.6962 40.9831C32.0177 40.9813 32.276 40.7099 32.2674 40.3823C32.2593 40.0677 32.0059 39.821 31.6919 39.8223C31.3668 39.8235 31.1108 40.0893 31.1201 40.4175C31.1282 40.7291 31.389 40.985 31.6968 40.9831H31.6962ZM31.6808 43.4729C31.996 43.4797 32.2532 43.2387 32.2668 42.924C32.2804 42.5946 32.0313 42.3214 31.708 42.3121C31.4032 42.3028 31.135 42.5569 31.122 42.8672C31.1078 43.1979 31.355 43.4661 31.6814 43.4736L31.6808 43.4729Z" fill="#FEF5D6"/>
<path d="M37.5519 19.9988C37.8443 20.5464 38.0316 21.1145 38.0019 21.7518C37.9556 22.7396 37.508 23.5085 36.7638 24.1198C36.3904 24.4264 35.9682 24.6613 35.4929 24.7713C35.2642 24.8239 35.2178 24.9444 35.2221 25.1583C35.2351 25.8154 35.2283 26.4724 35.2259 27.1295C35.2259 27.2303 35.2518 27.3353 35.1943 27.4305C35.1783 27.4379 35.1616 27.4466 35.1449 27.4546L35.1486 27.4577C33.5247 27.4194 31.9015 27.4386 30.2777 27.4478C30.2795 27.4336 30.2783 27.4194 30.2739 27.4058C30.2739 23.9078 30.2733 20.4092 30.2727 16.9112C31.5319 16.8853 32.6136 17.3235 33.5266 18.1778C33.6941 18.3348 33.8394 18.3651 34.0656 18.3509C35.4465 18.2656 36.5882 18.744 37.4363 19.8616C37.4728 19.9092 37.5136 19.9537 37.5525 19.9994L37.5519 19.9988Z" fill="#FFF6D6"/>
<path d="M30.2721 16.9102C30.2721 20.4081 30.2727 23.9067 30.2733 27.4047C28.6272 27.4066 26.9817 27.409 25.3356 27.4109C25.3288 26.8261 25.322 26.2414 25.3152 25.6566C25.3189 25.4422 25.3196 25.2271 25.3276 25.0119C25.3325 24.892 25.2948 24.829 25.1675 24.7987C24.6853 24.6831 24.2576 24.447 23.849 24.1738C23.0992 23.5513 22.6072 22.7867 22.5528 21.7896C22.4755 20.3803 23.4175 19.0674 24.8578 18.5711C25.3968 18.385 25.9501 18.2787 26.5218 18.372C26.7382 18.4073 26.8797 18.3281 27.0343 18.181C27.9392 17.3212 29.0265 16.9108 30.2721 16.9102Z" fill="#FFFEFE"/>
<path d="M30.2751 28.6722C29.4555 28.6264 28.634 28.5968 27.8316 28.3996C27.6425 28.3532 27.4453 28.3248 27.2667 28.2531C27.0651 28.1721 26.9681 28.2463 26.876 28.415C26.5917 28.9355 26.3753 29.4683 26.4173 30.0821C26.4421 30.44 26.4248 30.8016 26.4198 31.1614C26.4186 31.2739 26.4631 31.4482 26.2616 31.4167C26.086 31.3895 25.8462 31.5619 25.7485 31.2442C25.594 30.7404 25.5167 30.2243 25.4506 29.7044C25.4326 29.5623 25.3993 29.4226 25.3733 29.281C25.3548 28.9955 25.3517 28.7074 25.3121 28.4249C25.2639 28.0837 25.2769 27.7487 25.3356 27.4124C26.9817 27.4106 28.6272 27.4087 30.2733 27.4062C30.2776 27.4198 30.2789 27.4341 30.277 27.4483C30.2751 27.8451 30.2739 28.242 30.2721 28.6382C30.2764 28.6493 30.2776 28.6604 30.2745 28.6716L30.2751 28.6722Z" fill="#8F6C4C"/>
<path d="M28.0574 35.3704C28.6372 35.7678 29.2355 36.1263 29.9384 36.2636C30.0484 36.2852 30.1615 36.292 30.2734 36.3056C30.2734 36.3272 30.274 36.3489 30.2746 36.3699C30.2721 36.9355 30.232 36.8564 30.7599 36.8632C31.3193 36.87 31.8787 36.8743 32.4381 36.8798C32.333 36.9373 32.2187 36.9169 32.1074 36.9169C31.4942 36.92 30.8816 36.92 30.2684 36.9213C29.5125 36.9151 28.7565 36.9089 28.0005 36.9021C28.0592 36.5034 28.0116 36.1029 28.0283 35.7035C28.0332 35.5917 28.0475 35.4804 28.0574 35.3691V35.3704Z" fill="#FEC575"/>
<path d="M23.849 24.1738C24.2582 24.447 24.6853 24.6832 25.1675 24.7988C25.2954 24.8297 25.3325 24.8921 25.3276 25.012C25.3189 25.2265 25.3189 25.4416 25.3152 25.6567C24.8046 25.1838 24.2953 24.7097 23.849 24.1738Z" fill="#A02124"/>
<path d="M25.1539 32.0938C25.2874 32.137 25.4278 32.2304 25.5539 32.2143C25.9025 32.1692 26.0305 32.354 26.125 32.6402C26.1973 32.8596 26.3129 33.0648 26.4094 33.2762C25.9396 32.9369 25.5316 32.532 25.1545 32.0938H25.1539Z" fill="#A02124"/>
<path d="M35.1603 29.2632C35.0627 29.9116 35.0027 30.565 34.8222 31.201C34.7177 31.5694 34.4464 31.4 34.2461 31.4105C34.0217 31.4229 34.1194 31.2158 34.1157 31.1064C34.1033 30.7368 34.0922 30.3647 34.1151 29.9957C34.1528 29.3893 33.9105 28.8768 33.6298 28.3694C33.5501 28.2253 33.4716 28.2049 33.3109 28.2507C32.3188 28.5338 31.3075 28.6834 30.2721 28.637C30.2739 28.2402 30.2752 27.8433 30.277 27.4471C31.9009 27.4372 33.5241 27.4181 35.148 27.457C35.2747 28.0584 35.2123 28.6605 35.1603 29.2632Z" fill="#73573A"/>
<path d="M30.2684 36.9218C30.8816 36.9206 31.4942 36.9199 32.1074 36.9175C32.2187 36.9175 32.333 36.9373 32.4381 36.8798C32.4746 36.873 32.511 36.8662 32.5475 36.8594C32.7385 36.8921 32.8343 36.9731 32.8133 37.1981C32.7836 37.514 32.8022 37.8348 32.7997 38.1537C32.0332 38.1587 31.2667 38.1605 30.5002 38.1704C30.3253 38.1729 30.2678 38.1018 30.2709 37.9337C30.2771 37.5962 30.2697 37.2587 30.2684 36.9218Z" fill="#FEE572"/>
<path d="M32.5475 36.861C32.511 36.8678 32.4746 36.8746 32.4381 36.8814C31.8787 36.8758 31.3193 36.8709 30.7598 36.8647C30.232 36.8585 30.2728 36.937 30.2746 36.3714C31.1097 36.2014 31.8675 35.8639 32.5327 35.3262C32.5803 35.8374 32.5531 36.3492 32.5475 36.861Z" fill="#FBAA25"/>
<path d="M35.1443 27.4538C35.161 27.4458 35.1777 27.4377 35.1937 27.4297C35.177 27.4377 35.1604 27.4458 35.1443 27.4538Z" fill="#8F6C4C"/>
<path d="M31.6963 40.9831C31.3885 40.985 31.1283 40.7291 31.1196 40.4175C31.1109 40.0893 31.3662 39.8235 31.6914 39.8223C32.0054 39.821 32.2588 40.0677 32.2669 40.3823C32.2755 40.7099 32.0171 40.9819 31.6957 40.9831H31.6963Z" fill="#74583C"/>
<path d="M31.6808 43.4722C31.3545 43.4648 31.1072 43.1972 31.1214 42.8658C31.135 42.5555 31.4027 42.3021 31.7074 42.3108C32.0307 42.3207 32.2804 42.5939 32.2662 42.9227C32.2532 43.2373 31.9955 43.4784 31.6802 43.4716L31.6808 43.4722Z" fill="#74583C"/>
<path d="M30.2752 28.6707C29.4555 28.625 28.634 28.5953 27.8317 28.3981C27.6425 28.3518 27.4454 28.3233 27.2667 28.2516C27.0652 28.1706 26.9682 28.2448 26.8761 28.4136C26.5917 28.934 26.3754 29.4669 26.4174 30.0807C26.4421 30.4385 26.4248 30.8002 26.4199 31.1599C26.4186 31.2724 26.4631 31.4467 26.2616 31.4152C26.0861 31.388 25.8462 31.5604 25.7486 31.2427C25.594 30.739 25.5168 30.2228 25.4506 29.703C25.4327 29.5608 25.3993 29.4211 25.3734 29.2796C25.0062 29.3921 24.678 29.6096 24.6359 29.9904C24.5531 30.7408 24.4832 31.5172 25.1539 32.0945C25.2874 32.1378 25.4278 32.2311 25.5539 32.215C25.9025 32.1699 26.0304 32.3547 26.125 32.6409C26.1973 32.8604 26.3129 33.0656 26.4094 33.277C26.5311 33.4785 26.6548 33.6794 26.7747 33.8821C27.1128 34.4558 27.5987 34.9008 28.0573 35.3706C28.6371 35.768 29.2355 36.1265 29.9383 36.2638C30.0483 36.2854 30.1615 36.2922 30.2733 36.3058C30.2746 35.3545 30.2764 34.4038 30.2777 33.4525C30.2808 32.3492 30.2845 31.2452 30.2876 30.1418C30.2832 29.6517 30.2789 29.1609 30.2752 28.6707Z" fill="#FEDBA9"/>
<path d="M35.1603 29.2628C35.0627 29.9112 35.0027 30.5645 34.8222 31.2006C34.7177 31.569 34.4464 31.3996 34.2461 31.4101C34.0217 31.4225 34.1194 31.2154 34.1157 31.106C34.1033 30.7364 34.0922 30.3643 34.1151 29.9953C34.1528 29.3889 33.9105 28.8764 33.6298 28.369C33.5501 28.2249 33.4716 28.2045 33.3109 28.2503C32.3188 28.5334 31.3075 28.683 30.2721 28.6366C30.2764 28.6477 30.2777 28.6589 30.2746 28.67C30.2789 29.1602 30.2832 29.651 30.2869 30.1411V33.4518C30.2838 33.4518 30.2801 33.4518 30.277 33.4518C30.2758 34.4031 30.2739 35.3538 30.2727 36.3051C30.2727 36.3267 30.2733 36.3484 30.2739 36.3694C31.1091 36.1994 31.8669 35.8619 32.532 35.3241C33.4617 34.5366 34.0842 33.5371 34.5342 32.4189C34.5799 32.3052 34.6275 32.2527 34.7728 32.2502C35.4397 32.236 35.8316 31.857 35.8965 31.1963C35.9176 30.9824 35.9398 30.7685 35.9639 30.5547C36.0399 29.8865 35.7952 29.4921 35.1597 29.2622L35.1603 29.2628Z" fill="#FECE89"/>
</g>
</g>
<defs>
<filter id="filter0_d_311_5310" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_311_5310"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_311_5310" result="shape"/>
</filter>
<filter id="filter1_i_311_5310" x="14.4545" y="14.1816" width="35.6363" height="35.6367" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_311_5310"/>
</filter>
<linearGradient id="paint0_linear_311_5310" x1="22.9091" y1="22.6364" x2="54.2727" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEFEFE"/>
<stop offset="1" stop-color="#CDCDCD"/>
</linearGradient>
<linearGradient id="paint1_linear_311_5310" x1="30" y1="6" x2="30" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#A1A1A1"/>
<stop offset="1" stop-color="#FEFEFE"/>
</linearGradient>
<clipPath id="clip0_311_5310">
<rect width="32.7273" height="32.7273" fill="white" transform="translate(13.6364 13.6367)"/>
</clipPath>
</defs>
</svg>
