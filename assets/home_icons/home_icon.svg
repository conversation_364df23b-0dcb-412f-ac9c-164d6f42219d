<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_165_1879)">
<rect x="6" y="6" width="48" height="48" rx="8" fill="url(#paint0_linear_165_1879)"/>
</g>
<g filter="url(#filter1_d_165_1879)">
<path d="M24.3667 36.0664C24.3667 35.5877 24.7547 35.1997 25.2334 35.1997H34.7667C35.2454 35.1997 35.6334 35.5877 35.6334 36.0664C35.6334 36.545 35.2454 36.933 34.7667 36.933H25.2334C24.7547 36.933 24.3667 36.545 24.3667 36.0664Z" fill="#FEFEFE"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.7438 18.2785C31.5456 16.5738 28.4544 16.5738 26.2562 18.2785L19.3229 23.6551C17.8585 24.7907 17 26.5303 17 28.3756V37.0011C17 40.3249 19.7269 43 23.0667 43H36.9333C40.2731 43 43 40.3249 43 37.0011V28.3756C43 26.5303 42.1415 24.7907 40.6771 23.6551L33.7438 18.2785ZM27.3184 19.6482C28.8914 18.4284 31.1086 18.4284 32.6816 19.6482L39.6149 25.0249C40.66 25.8353 41.2667 27.0711 41.2667 28.3756V37.0011C41.2667 39.3462 39.3373 41.2667 36.9333 41.2667H23.0667C20.6627 41.2667 18.7333 39.3462 18.7333 37.0011V28.3756C18.7333 27.0711 19.34 25.8353 20.3851 25.0249L27.3184 19.6482Z" fill="#FEFEFE"/>
</g>
<defs>
<filter id="filter0_d_165_1879" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1879"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1879" result="shape"/>
</filter>
<filter id="filter1_d_165_1879" x="13" y="15" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1879"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1879" result="shape"/>
</filter>
<linearGradient id="paint0_linear_165_1879" x1="30" y1="6" x2="30" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#E01010"/>
<stop offset="1" stop-color="#7A0909"/>
</linearGradient>
</defs>
</svg>
