<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_165_1949)">
<rect x="6" y="6" width="48" height="48" rx="10" fill="url(#paint0_linear_165_1949)"/>
<rect x="6.5" y="6.5" width="47" height="47" rx="9.5" stroke="url(#paint1_linear_165_1949)" stroke-opacity="0.4"/>
</g>
<g clip-path="url(#clip0_165_1949)">
<g filter="url(#filter1_i_165_1949)">
<path d="M40.8423 40.8455C46.8324 34.8554 46.8324 25.1436 40.8423 19.1535C34.8522 13.1634 25.1403 13.1634 19.1502 19.1535C13.1601 25.1436 13.1601 34.8554 19.1502 40.8455C25.1403 46.8356 34.8522 46.8356 40.8423 40.8455Z" fill="#46CC6B"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M45.2021 32.064C44.3203 38.6193 39.2988 43.8618 32.8541 45.0746L23.0611 35.2816C21.7265 34.1792 20.873 32.5129 20.873 30.6552C20.873 27.3525 23.5704 24.6551 26.8731 24.6551H34.4648V22.0659L39.1266 25.9884L45.2021 32.064Z" fill="#179C5F"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M34.4648 29.911V27.3218H26.8731C25.0425 27.3218 23.5397 28.8245 23.5397 30.6552C23.5397 32.4858 25.0425 33.9886 26.8731 33.9886H35.9565V36.6552H26.8731C23.5704 36.6552 20.873 33.9579 20.873 30.6552C20.873 27.3525 23.5704 24.6551 26.8731 24.6551H34.4648V22.0659L39.1266 25.9884L34.4648 29.911Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_165_1949" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1949"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1949" result="shape"/>
</filter>
<filter id="filter1_i_165_1949" x="14.6577" y="14.6611" width="32.6772" height="32.6768" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.26 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_165_1949"/>
</filter>
<linearGradient id="paint0_linear_165_1949" x1="22.9091" y1="22.6364" x2="54.2727" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEFEFE"/>
<stop offset="1" stop-color="#CDCDCD"/>
</linearGradient>
<linearGradient id="paint1_linear_165_1949" x1="30" y1="6" x2="30" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#A1A1A1"/>
<stop offset="1" stop-color="#FEFEFE"/>
</linearGradient>
<clipPath id="clip0_165_1949">
<rect width="32.7273" height="32.7273" fill="white" transform="translate(13.6362 13.6362)"/>
</clipPath>
</defs>
</svg>
