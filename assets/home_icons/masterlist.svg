<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_5659_1751)">
<rect x="6.5" y="6" width="88" height="88" rx="16" fill="url(#paint0_linear_5659_1751)"/>
</g>
<g filter="url(#filter1_d_5659_1751)">
<g filter="url(#filter2_i_5659_1751)">
<path d="M36.36 23C32.2952 23 29 26.2968 29 30.3636V69.6364C29 73.7033 32.2952 77 36.36 77H65.8C69.8649 77 73.16 73.7033 73.16 69.6364V30.3636C73.16 26.2968 69.8649 23 65.8 23H36.36Z" fill="#FEFEFE"/>
</g>
</g>
<g filter="url(#filter3_i_5659_1751)">
<circle cx="40.1602" cy="40" r="3" fill="#60C26F"/>
<circle cx="40.1602" cy="50" r="3" fill="#268EFF"/>
<circle cx="40.1602" cy="60" r="3" fill="#E01010"/>
<rect x="45.1602" y="39" width="20" height="2" rx="1" fill="#E0E0E0"/>
<rect x="45.1602" y="49" width="20" height="2" rx="1" fill="#E0E0E0"/>
<rect x="45.1602" y="59" width="20" height="2" rx="1" fill="#E0E0E0"/>
</g>
<defs>
<filter id="filter0_d_5659_1751" x="0.5" y="0" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5659_1751"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5659_1751" result="shape"/>
</filter>
<filter id="filter1_d_5659_1751" x="29" y="23" width="48.1602" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5659_1751"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5659_1751" result="shape"/>
</filter>
<filter id="filter2_i_5659_1751" x="29" y="23" width="45.1602" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5659_1751"/>
</filter>
<filter id="filter3_i_5659_1751" x="37.1602" y="37" width="29" height="27" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5659_1751"/>
</filter>
<linearGradient id="paint0_linear_5659_1751" x1="50.5" y1="6" x2="50.5" y2="94" gradientUnits="userSpaceOnUse">
<stop stop-color="#FC7182"/>
<stop offset="1" stop-color="#E63F45"/>
</linearGradient>
</defs>
</svg>
