<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_165_1942)">
<rect x="6" y="6" width="48" height="48" rx="10" fill="url(#paint0_linear_165_1942)"/>
</g>
<g clip-path="url(#clip0_165_1942)" filter="url(#filter1_i_165_1942)">
<path d="M37.4964 20.2157L29.9996 13.6362V17.3393L28.082 23.0921H29.9996V26.7951L37.4964 20.2157Z" fill="#0A77E8"/>
<g filter="url(#filter2_d_165_1942)">
<path d="M17.5352 29.8038C17.5352 32.6724 18.53 35.4692 20.3362 37.6791L21.0071 38.4998L25.0721 34.4348L24.5843 33.7713C23.7363 32.6177 23.288 31.2457 23.288 29.8038C23.288 26.103 26.2988 23.0922 29.9996 23.0922V17.3394C23.1267 17.3394 17.5352 22.9309 17.5352 29.8038Z" fill="#0F9AF0"/>
</g>
<path d="M29.9997 36.9076V33.2046L22.5029 39.784L29.9997 46.3634V42.6604L31.9173 36.9076H29.9997Z" fill="#0A77E8"/>
<g filter="url(#filter3_d_165_1942)">
<path d="M39.6634 22.3207L38.9926 21.5L34.9276 25.565L35.4153 26.2285C36.2634 27.3821 36.7116 28.7541 36.7116 30.1959C36.7116 33.8967 33.7008 36.9076 30 36.9076V42.6604C36.8729 42.6604 42.4645 37.0689 42.4645 30.1959C42.4645 27.3274 41.4698 24.5307 39.6634 22.3207Z" fill="#085AAE"/>
</g>
</g>
<defs>
<filter id="filter0_d_165_1942" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1942" result="shape"/>
</filter>
<filter id="filter1_i_165_1942" x="13.6362" y="13.6362" width="34.7271" height="34.7271" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_165_1942"/>
</filter>
<filter id="filter2_d_165_1942" x="16.5352" y="17.3394" width="16.4644" height="25.1606" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1942" result="shape"/>
</filter>
<filter id="filter3_d_165_1942" x="30" y="21.5" width="16.4644" height="25.1606" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_165_1942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_165_1942" result="shape"/>
</filter>
<linearGradient id="paint0_linear_165_1942" x1="30" y1="6" x2="30" y2="54" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE993"/>
<stop offset="1" stop-color="#C8A209"/>
</linearGradient>
<clipPath id="clip0_165_1942">
<rect width="32.7273" height="32.7273" fill="white" transform="translate(13.6362 13.6362)"/>
</clipPath>
</defs>
</svg>
