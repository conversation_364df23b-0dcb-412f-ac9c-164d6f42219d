import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/providers/auth/controllers/password_reset_provider.dart';
import '../../../../core/utils/device_utils.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../core/network/app_status.dart';


class PasswordResetDialog extends HookConsumerWidget {
  const PasswordResetDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final passwordResetState = ref.watch(passwordResetProvider);
    final passwordResetNotifier = ref.read(passwordResetProvider.notifier);
    final emailController =
    useTextEditingController(text: passwordResetNotifier.email);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    void sendResetLink() {
      if (formKey.currentState!.validate()) {
        passwordResetNotifier.setEmail(emailController.text);
        passwordResetNotifier.sendResetLink(context);
      }
    }

    void closeDialog() {
      passwordResetNotifier.reset();
      Navigator.of(context).pop();
    }

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: EdgeInsets.only(left: 30, top: 10, right: 10, bottom: 20),
        width: 500.w,
        child: passwordResetState.status == AppStatus.success
            ? _buildSuccessState(context, closeDialog)
            : _buildResetForm(
          context,
          emailController,
          sendResetLink,
          closeDialog,
          formKey,
          passwordResetState.status == AppStatus.loading,
        ),
      ),
    );
  }

  Widget _buildResetForm(
      BuildContext context,
      TextEditingController emailController,
      VoidCallback sendResetLink,
      VoidCallback closeDialog,
      GlobalKey<FormState> formKey,
      bool isLoading,
      ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: GestureDetector(
            onTap: closeDialog,
            child: SvgPicture.asset(AssetsManager.ic_close),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(right: 20.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 16),
              Text(
                "Reset Your Password",
                style: context.theme.textTheme.labelLarge!.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 35.sp,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Password reset link will be sent on your registered email.',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.blackTextColor,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 26),
              Form(
                key: formKey,
                child: CustomTextField(
                  hintText: 'Email Id',
                  fontSize: responsiveFont(20).sp,
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  autoFocus: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter email';
                    }
                    if (!value.contains('@')) {
                      return 'Enter a valid email';
                    }
                    return null;
                  },
                  isDense: true,
                ),
              ),
              SizedBox(height: 25.h),
              CustomButton(
                text: 'Send link',
                height: 30,
                width: 110,
                fontSize: responsiveFont(18).sp,
                isLoading: isLoading,
                onPressed: sendResetLink,
              ),
              SizedBox(height: 20),
              TextButton(
                onPressed: () {
                  Utils().launchURL(
                      url: 'https://support.mastercook.com/hc/en-us');
                },
                child: Text(
                  'Need Help?',
                  style: TextStyle(
                    color: AppColors.primaryBorderColor,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessState(BuildContext context, VoidCallback closeDialog) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: GestureDetector(
            onTap: closeDialog,
            child: SvgPicture.asset(AssetsManager.ic_close),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(right: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(AssetsManager.ic_tick),
              SizedBox(height: 8),
              Text(
                'Password reset link \nsent successfully.',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.blackTextColor,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 25),
              CustomButton(
                text: 'Check Email',
                isUnderline: true,
                height: 30,
                width: 120,
                fontSize: responsiveFont(18).sp,
                onPressed: () {
                  Utils().launchURL(url: 'mailto:');
                  closeDialog();
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}