import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import 'dart:io' show Platform;
import '../../../core/providers/auth/auth_controller.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/YouTubeLauncher.dart';
import '../../../core/widgets/YouTubePlayer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text_button.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../core/network/app_status.dart';
import '../../core/utils/Utils.dart';
import 'dialogs/password_reset_dialog.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);

    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    print("sdfjshdfjhgj ${DeviceUtils().isTabletOrIpad(context)}");


    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              AssetsManager.background_img,
              fit: BoxFit.cover,
            ),
          ),
          Center(
            child: FractionallySizedBox(
              widthFactor:DeviceUtils().isTabletOrIpad(context)?0.8 :0.45,
              heightFactor: 0.62,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.95),
                  borderRadius: BorderRadius.circular(30.r),
                ),
                child: Row(
                  children: [
                    // Left Section
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30.r),
                            bottomLeft: Radius.circular(30.r),
                          ),
                          image: DecorationImage(
                            image: AssetImage(AssetsManager.recipe_bg_img),
                            fit: BoxFit.cover,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 50),
                              child: Image.asset(
                                AssetsManager.mastercook_ai_logo,
                                height: 250.h,
                                width: 400.w,
                              ),
                            ),
                            SizedBox(height: 10.h),
                            Padding(
                              padding: const EdgeInsets.only(top: 70),
                              child: Align(
                                alignment: Alignment.bottomCenter,
                                child: Text(
                                  "Watch getting started video",
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: responsiveFont(23).sp,
                                    fontWeight: FontWeight.w400,
                                    height: 16 / 12,
                                    letterSpacing: 0,
                                    color: Color(0xFF4F4F4F),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 10.0, left: 20.0, right: 20.0),
                              child: Container(
                                width: 338,
                                height: 180,
                                decoration: BoxDecoration(
                                  color: Color(0xFF333333),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: (kIsWeb || Platform.isMacOS)
                                      ? const InAppYouTubePlayer(videoId: "d1q4nwMUegA")

                                      : (Platform.isWindows || Platform.isLinux)
                                      ? YouTubeLauncher()
                                      : Center(
                                    child: Icon(
                                      Icons.play_circle_fill,
                                      size: 48,
                                      color: Colors.white.withValues(alpha: 0.8),
                                    ),

                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // Right Section
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.all(18.w),
                        child: Form(
                          key: formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(height: 20.h),
                              RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: responsiveFont(18).sp,
                                    height: 16 / 12,
                                    letterSpacing: 0,
                                  ),
                                  children: [
                                    TextSpan(
                                      text: "Don’t have an Account? ",
                                      style: TextStyle(
                                        color: Color(0xFF4F4F4F),
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    TextSpan(
                                      text: "Register now",
                                      style: TextStyle(
                                        color: Color(0xFF007AFF),
                                        fontWeight: FontWeight.w400,
                                        decoration: TextDecoration.underline,
                                      ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          print('Register now button tapped');
                                        },
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              Text(
                                "Welcome Back!",
                                style: context.theme.textTheme.bodyLarge!.copyWith(
                                  color: AppColors.primaryGreyColor,
                                  fontSize: responsiveFont(40.sp),
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 100.h),
                              Container(
                                margin: EdgeInsets.symmetric(horizontal: 100.w),
                                child: Column(
                                  children: [
                                    CustomTextField(
                                      hintText: 'Username',
                                      fontSize: responsiveFont(20).sp,
                                      controller: emailController,
                                      keyboardType: TextInputType.emailAddress,
                                      autoFocus: true,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please enter email';
                                        }
                                        if (!value.contains('@')) {
                                          return 'Enter a valid email';
                                        }
                                        return null;
                                      },
                                      isDense: true,
                                    ),
                                    SizedBox(height: 20.h),
                                    CustomTextField(
                                      hintText: 'Password',
                                      controller: passwordController,
                                      keyboardType: TextInputType.text,
                                      autoFocus: true,
                                      fontSize: responsiveFont(20).sp,
                                      isPassword: true,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please enter password';
                                        }
                                        if (value.length < 6) {
                                          return 'Password must be at least 6 characters';
                                        }
                                        return null;
                                      },
                                      isDense: true,
                                    ),
                                    SizedBox(height: 20.h),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: CustomTextButton(
                                        text: 'Forgot Password?',
                                        size: responsiveFont(16).sp,
                                        onPressed: () {

                                          showDialog(
                                            context: context,
                                            builder: (context) => const PasswordResetDialog(),
                                          );
                                        },
                                        color: AppColors.primaryLightTextColor,
                                      ),
                                    ),
                                    SizedBox(height: 30.h),
                                    CustomButton(
                                      text: 'Login',
                                      height: 30,
                                      isLoading: authState.status == AppStatus.loading,
                                      width: 250,
                                      fontSize: responsiveFont(18).sp,
                                      onPressed: () {
                                        if (formKey.currentState!.validate()) {
                                          authNotifier.authenticate(
                                            context,
                                            emailController.text.trim(),
                                            passwordController.text.trim(),
                                          );
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              Text(
                                "Love MasterCook?",
                                style: context.theme.textTheme.labelSmall!.copyWith(
                                  color: AppColors.primaryLightTextColor,
                                  fontSize: responsiveFont(35).sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 30.h),
                              SizedBox(
                                height: 32,
                                width: 160,
                                child: CustomButton(
                                  onPressed: () {},
                                  text: "Buy More Products",
                                  height: 16 / 12,
                                  fontSize: responsiveFont(17.9).sp,
                                  fontFamily: 'Inter',
                                  isUnderline: true,
                                ),
                              ),
                              SizedBox(height: 50.h),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}