import 'dart:io';
import 'package:intl/intl.dart'; // Added for date formatting
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';

import '../../../core/helpers/local_storage_service.dart';
import '../../../core/utils/device_utils.dart';
import '../../core/utils/Utils.dart';
import '../../../core/providers/profile/user_profile_notifier.dart';
import '../../../core/network/app_status.dart';
import '../../../core/data/models/plans_response.dart';
import '../../../core/data/models/user_profile_response.dart';
import '../../../core/data/models/user_types_response.dart';
import '../../../core/data/request_query/update_profile_request.dart';
import '../../../core/services/file_picker_service.dart';
import 'package:country_pickers/country_pickers.dart';
import 'package:country_pickers/country.dart';

class myAccount extends ConsumerStatefulWidget {
  const myAccount({super.key});

  @override
  ConsumerState<myAccount> createState() => _myAccountState();
}

class _myAccountState extends ConsumerState<myAccount>
    with SingleTickerProviderStateMixin {
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _typesOfUsersController = TextEditingController();
  final TextEditingController _dateOfBirthController = TextEditingController();

  // Controllers for Settings tab
  final TextEditingController _currentPasswordController =
      TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmNewPasswordController =
      TextEditingController();

  // State for radio buttons (Profile tab)
  int _selectedGender = 0; // 0 for Male, 1 for Female

  // State for checkbox (Settings tab)
  bool _subscribeNewsletter = true;

  // State for password visibility
  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  // State for password validation
  bool _showPasswordValidation = false;

  // Selected user type ID for dropdown
  int? _selectedUserTypeId;

  // Selected image file for profile picture
  File? _selectedProfileImage;

  // Flag to track if form fields have been populated
  bool _formFieldsPopulated = false;

  // Selected user type for dropdown
  String? _selectedUserType;

  // Date dropdown state variables
  String? _selectedDay;
  String? _selectedMonth;
  String? _selectedYear;

  // Country picker state variable
  Country? _selectedCountry;

  // Both save password and delete account loading are handled by provider

  @override
  void initState() {
    super.initState();
    // Add listeners to password controllers for real-time validation
    _newPasswordController.addListener(_updatePasswordValidation);
    _confirmNewPasswordController.addListener(_updatePasswordValidation);

    // Initialize default country (US)
    _selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');

    // Fetch user profile data when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      ref.read(userTypesNotifierProvider.notifier).fetchUserTypes();
    });
  }

  void _updatePasswordValidation() {
    setState(() {
      _showPasswordValidation = _newPasswordController.text.isNotEmpty &&
                               _confirmNewPasswordController.text.isNotEmpty;
    });
  }

  // Password validation helper methods
  String? _validatePassword(String password) {
    if (password.isEmpty) return null;

    if (password.length < 8) {
      return 'Password must be at least 8 characters long.';
    }

    if (password.contains(' ')) {
      return 'Password must be at least 8 characters long, contain at least one special character, and have no spaces.';
    }

    // Check for special characters
    final specialCharRegex = RegExp(r'[!@#$%^&*(),.?":{}|<>]');
    if (!specialCharRegex.hasMatch(password)) {
      return 'Password must be at least 8 characters long, contain at least one special character, and have no spaces.';
    }

    return null; // Password is valid
  }

  bool _arePasswordsMatching() {
    return _newPasswordController.text == _confirmNewPasswordController.text;
  }

  // Helper method to format date from API response
  String _formatDate(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      return DateFormat('MM/dd/yyyy').format(date);
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }

  // Helper method to build billing text
  String _buildBillingText(CurrentPlan? currentPlan) {
    final billingInterval = currentPlan?.billingInterval ?? 'year';
    final billingDisplay = billingInterval.toLowerCase() == 'year' ? 'Yearly' : billingInterval;
    final nextPaymentDate = _formatDate(currentPlan?.nextPaymentDate ?? '2026-06-16');
    final planAmount = currentPlan?.planAmount ?? 70;

    return 'Billed $billingDisplay / Next payment on $nextPaymentDate for \$$planAmount';
  }

  // Helper method to populate form fields with user profile data
  void _populateFormFields(UserProfile? userProfile) {
    if (userProfile != null && !_formFieldsPopulated) {
      // Split name into first and last name
      final nameParts = userProfile.name.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : '';
      final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      _firstNameController.text = firstName;
      _lastNameController.text = lastName;
      _emailController.text = userProfile.email;
      _contactController.text = userProfile.phone;
      _companyNameController.text = userProfile.companyName;
      _typesOfUsersController.text = userProfile.userType;
      _dateOfBirthController.text = _formatDateForDisplay(userProfile.dob);

      // Try to detect country from phone number
      _detectCountryFromPhone(userProfile.phone);

      // Set gender selection and user type
      setState(() {
        _selectedGender = userProfile.gender.toLowerCase() == 'male' ? 0 : 1;
        _selectedUserType = userProfile.userType;
        _selectedUserTypeId = userProfile.userTypeId; // Set the user type ID for API calls
        _formFieldsPopulated = true;
      });
    }
  }

  // Helper method to detect country from phone number
  void _detectCountryFromPhone(String phoneNumber) {
    // Remove any non-digit characters
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Try to detect country based on common patterns
    if (cleanPhone.startsWith('1') && cleanPhone.length == 11) {
      // US/Canada number
      _selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
    } else if (cleanPhone.startsWith('44')) {
      // UK number
      _selectedCountry = CountryPickerUtils.getCountryByIsoCode('GB');
    } else if (cleanPhone.startsWith('91')) {
      // India number
      _selectedCountry = CountryPickerUtils.getCountryByIsoCode('IN');
    } else if (cleanPhone.length == 10) {
      // Assume US number without country code
      _selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
    } else {
      // Default to US
      _selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
    }
  }

  // Helper method to format date for display in form
  String _formatDateForDisplay(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      return DateFormat('dd MMM yyyy').format(date);
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }

  @override
  void dispose() {
    // It's crucial to dispose of TextEditingControllers to prevent memory leaks.
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _contactController.dispose();
    _companyNameController.dispose();
    _typesOfUsersController.dispose();
    _dateOfBirthController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmNewPasswordController.dispose();
    super.dispose();
  }

  // Method to pick profile image
  Future<void> _pickProfileImage() async {
    final pickFile = ref.read(filePickerServiceProvider.notifier);
    final file = await pickFile.pickFile(
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );

    if (file != null) {
      setState(() {
        _selectedProfileImage = file;
      });
    }
  }

  // Method to update profile with personal details and image
  Future<void> _updateProfile() async {
    try {
      // Validate required fields
      if (_firstNameController.text.trim().isEmpty) {
        Utils().showFlushbar(
          context,
          message: 'Please enter your first name',
          isError: true,
        );
        return;
      }

      if (_contactController.text.trim().isEmpty) {
        Utils().showFlushbar(
          context,
          message: 'Please enter your contact number',
          isError: true,
        );
        return;
      }

      if (_dateOfBirthController.text.trim().isEmpty) {
        Utils().showFlushbar(
          context,
          message: 'Please enter your date of birth',
          isError: true,
        );
        return;
      }

      if (_selectedUserTypeId == null) {
        Utils().showFlushbar(
          context,
          message: 'Please select a user type',
          isError: true,
        );
        return;
      }

      // Create update profile request
      final request = UpdateProfileRequest(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim().isNotEmpty
            ? _lastNameController.text.trim()
            : null,
        gender: _selectedGender == 0 ? 'Male' : 'Female',
        dob: _dateOfBirthController.text.trim(),
        countryCode: '+${_selectedCountry?.phoneCode ?? '1'}', // Dynamic country code
        contact: _contactController.text.trim(),
        companyName: _companyNameController.text.trim().isNotEmpty
            ? _companyNameController.text.trim()
            : null,
        userTypeId: _selectedUserTypeId!,
        profilePic: _selectedProfileImage,
      );

      // Call update profile API
      final success = await ref.read(userProfileNotifierProvider.notifier)
          .updateProfile(context: context, request: request);

      if (success) {
        // Fetch updated profile data to refresh the UI
        await ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();

        // Clear selected image after successful update
        setState(() {
          _selectedProfileImage = null;
        });
      }
    } catch (e) {
      if (context.mounted) {
        Utils().showFlushbar(
          context,
          message: 'Failed to update profile: $e',
          isError: true,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch user profile state
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final userProfileNotifier = ref.read(userProfileNotifierProvider.notifier);

    // Populate form fields when user profile data is available (only once)
    if (userProfileState.data?.userProfile != null && !_formFieldsPopulated) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _populateFormFields(userProfileState.data!.userProfile!);
      });
    }

    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              AssetsManager.background_img,
              fit: BoxFit.cover,
            ),
          ),
          Positioned.fill(
            child: Column(
              crossAxisAlignment:
                  CrossAxisAlignment.start, // Align "My Account" to left
              children: [
                Stack(
                  children: [
                    // Your existing "My Account" section
                    Container(
                      color: AppColors.whiteColor,
                      padding: const EdgeInsets.fromLTRB(20, 20, 0, 20),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          'My Account',
                          style: TextStyle(
                            fontSize: responsiveFont(40).sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[800],
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 20,
                      right: 20,
                      child: GestureDetector(
                        onTap: () async {
                          final bool? confirmed =
                              await Utils().showCommonConfirmDialog(
                            context: context,
                            title: 'Logout',
                            subtitle:
                                'Are you sure you want to Logout?',
                            confirmText: 'Logout',
                            cancelText: 'Cancel',
                          );
                          if (confirmed == true) {
                            context.go('/splash');
                            print('Logout button pressed!');
                            final localStorage = ref.read(localStorageProvider);
                            await localStorage.clearLoginData();
                          }

                          // Continue with the update process here
                          // e.g., call your update method
                        },

                        // async {
                        //   context.go('/splash');
                        //   print('Logout button pressed!');
                        //   final localStorage = ref.read(localStorageProvider);
                        //   await localStorage.clearLoginData();
                        // },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: Image.asset(
                            AssetsManager.logoutImage,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 20.0, horizontal: 100.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.whiteColor,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                spreadRadius: 2,
                                blurRadius: 5,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(20),
                          child: Row(
                            children: [
                              // Profile picture and details
                              Row(
                                children: [
                                  SizedBox(
                                    width: 100,
                                    height: 100,
                                    child: Stack(
                                      children: [
                                        const Center(
                                          child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: AppColors.primaryColor),
                                        ),
                                        ClipOval(
                                          child: userProfileNotifier.hasProfilePic
                                              ? Image.network(
                                                  userProfileNotifier.profilePicUrl!,
                                                  width: 100,
                                                  height: 100,
                                                  fit: BoxFit.cover,
                                                  loadingBuilder: (context, child,
                                                      loadingProgress) {
                                                    if (loadingProgress == null)
                                                      return child;
                                                    return const SizedBox();
                                                  },
                                                  errorBuilder:
                                                      (context, error, stackTrace) =>
                                                          Container(
                                                    color: Colors.grey.shade300,
                                                    child: const Icon(Icons.person,
                                                        color: Colors.grey),
                                                  ),
                                                )
                                              : Container(
                                                  width: 100,
                                                  height: 100,
                                                  color: Colors.grey.shade300,
                                                  child: const Icon(Icons.person,
                                                      color: Colors.grey, size: 50),
                                                ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Image.asset(
                                            AssetsManager.personImage,
                                            width: 20,
                                            height: 20,
                                            fit: BoxFit.contain,
                                          ),
                                          const SizedBox(width: 10),
                                          Text(
                                            userProfileNotifier.userName.isNotEmpty
                                                ? userProfileNotifier.userName
                                                : 'Loading...',
                                            style: TextStyle(
                                                fontFamily: 'inter',
                                                fontSize: responsiveFont(24).sp,
                                                fontWeight: FontWeight.w600,
                                                color:
                                                    AppColors.primaryGreyColor),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Icon(Icons.mail,
                                              color: AppColors.primaryColor,
                                              size: 20), // Added mail icon
                                          SizedBox(width: 10),
                                          Text(
                                            userProfileNotifier.userEmail.isNotEmpty
                                                ? userProfileNotifier.userEmail
                                                : 'Loading...',
                                            style: TextStyle(
                                                fontFamily: 'inter',
                                                fontSize: responsiveFont(22).sp,
                                                fontWeight: FontWeight.w400,
                                                color:
                                                    AppColors.primaryGreyColor),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Icon(Icons.phone,
                                              color: AppColors.primaryColor,
                                              size: 20),
                                          SizedBox(width: 5),
                                          Text(
                                            userProfileNotifier.userProfile?.phone ?? '',
                                            style: TextStyle(
                                                fontFamily: 'inter',
                                                fontSize: responsiveFont(22).sp,
                                                fontWeight: FontWeight.w400,
                                                color:
                                                    AppColors.primaryGreyColor),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              const Spacer(),
                              const Spacer(),
                              // Current plan details
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    'Your Current plan',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w800,
                                      fontFamily: 'inter',
                                      fontSize: responsiveFont(28).sp,
                                      color: AppColors.primaryGreyColor,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Center(
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 30, vertical: 10),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryBorderColor
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        userProfileNotifier.userProfile?.currentPlan?.planName ?? 'Basic',
                                        style: TextStyle(
                                            fontFamily: 'inter',
                                            fontSize: responsiveFont(22).sp,
                                            fontWeight: FontWeight.w500,
                                            color:
                                                AppColors.primaryBorderColor),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              Container(
                                width: 1,
                                height: 90,
                                color: Colors.grey.shade300,
                              ),
                              const Spacer(),
                              Center(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      '\$${userProfileNotifier.userProfile?.currentPlan?.planAmount ?? 70}/${userProfileNotifier.userProfile?.currentPlan?.billingInterval ?? 'year'}',
                                      style: TextStyle(
                                        fontFamily: 'inter',
                                        fontSize: responsiveFont(50).sp,
                                        fontWeight: FontWeight.w900,
                                        color: AppColors.primaryColor,
                                      ),
                                    ),
                                    const SizedBox(height: 5),
                                    Text(
                                      _buildBillingText(userProfileNotifier.userProfile?.currentPlan),
                                      style: TextStyle(
                                        fontSize: responsiveFont(22).sp,
                                        fontFamily: 'inter',
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.primaryGreyColor,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              Container(
                                width: 1,
                                height: 90,
                                color: Colors.grey.shade300,
                              ),
                              const Spacer(),
                              // Storage section
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.cloud_outlined,
                                        color: Colors.blue,
                                        size: 24,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Storage',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w800,
                                          fontFamily: 'inter',
                                          fontSize: responsiveFont(28).sp,
                                          color: AppColors.primaryGreyColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    '75GB',
                                    style: TextStyle(
                                      fontFamily: 'inter',
                                      fontSize: responsiveFont(50).sp,
                                      fontWeight: FontWeight.w900,
                                      color: AppColors.primaryColor,
                                    ),
                                  ),
                                  const SizedBox(height: 5),
                                  Text(
                                    'used of 100GB',
                                    style: TextStyle(
                                      fontSize: responsiveFont(22).sp,
                                      fontFamily: 'inter',
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.primaryGreyColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                              const Spacer(),
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          // padding: const EdgeInsets.symmetric(
                          //     vertical: 0.0, horizontal: 100.0),
                          padding: const EdgeInsets.only(
                              left: 100.0, 
                              right: 100.0, 
                              bottom: 140.0
                          ),
                          child: DefaultTabController(
                            length: 4,
                            child: Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryGreyColor,
                                    // Dark background for tab bar
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(20),
                                      topRight: Radius.circular(20),
                                    ),
                                  ),
                                  child: TabBar(
                                    indicator: BoxDecoration(
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(
                                            20), // Adjust this value as needed
                                        topRight: Radius.circular(
                                            20), // Adjust this value as needed
                                      ),
                                      color: AppColors.whiteColor,
                                      border: Border(
                                        bottom: BorderSide(
                                          color: AppColors.primaryGreyColor,
                                          width: 3.0,
                                        ),
                                      ),
                                    ),
                                    indicatorSize: TabBarIndicatorSize.tab,
                                    labelColor: AppColors.primaryGreyColor,
                                    unselectedLabelColor: Colors.white24,
                                    tabs: [
                                      Tab(
                                        child: Text(
                                          'Profile',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: responsiveFont(18),
                                            fontFamily: 'inter',
                                          ),
                                        ),
                                      ),
                                      Tab(
                                        child: Text(
                                          'Subscription',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: responsiveFont(20),
                                            fontFamily: 'inter',
                                          ),
                                        ),
                                      ),
                                      Tab(
                                        child: Text(
                                          'Setting',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: responsiveFont(20),
                                            fontFamily: 'inter',
                                          ),
                                        ),
                                      ),
                                      Tab(
                                        child: Text(
                                          'Storage',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: responsiveFont(20),
                                            fontFamily: 'inter',
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                    child: Container(
                                    clipBehavior: Clip.none, // Allow dropdown to overflow
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(20),
                                        bottomRight: Radius.circular(20),
                                      ),
                                      color: AppColors.whiteColor,
                                    ),
                                  child: Stack(
                                    clipBehavior: Clip.none, // Allow dropdown to overflow beyond container
                                    children: [
                                      TabBarView(
                                        children: [
                                          // Profile Tab Content
                                          _buildProfileTabContent(),
                                          // Subscription Tab Content
                                          SubscriptionTabContent(),
                                          // Settings Tab Content
                                          _buildSettingTabContent(),
                                          // Storage Tab Content
                                          _buildStorageTabContent(),
                                        ],
                                      ),
                                    ],
                                  ),
                                )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // ... (rest of your helper methods like _buildProfileTabContent, _buildSubscriptionTabContent, etc. remain the same)
  // Ensure that _buildProfileTabContent, _buildSubscriptionTabContent, and _buildSettingTabContent
  // have their root Container color set to Colors.white as they represent the white cards.
  // The current code already does this.

  // Widget for Profile Tab Content
  Widget _buildProfileTabContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 30.0, bottom: 30.0, right: 50, left: 50),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 30),
              clipBehavior: Clip.none, // Allow dropdown to overflow
              decoration: BoxDecoration(
                color: Colors.white, // Card background (matches screenshot)
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color:
                        AppColors.blackColor.withValues(alpha: 0.1), // Subtle shadow
                    spreadRadius: 0,
                    blurRadius: 24,
                    offset: const Offset(0, 0), // changes position of shadow
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Personal Details :',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildTwoColumnRow('Gender', _buildGenderRadioButtons()),
                  const SizedBox(height: 15),
                  _buildTwoColumnRow('First Name', _buildSimpleTextField(_firstNameController)),
                  const SizedBox(height: 15),
                  _buildTwoColumnRow('Last Name', _buildSimpleTextField(_lastNameController)),
                  const SizedBox(height: 15),
                  _buildTwoColumnRow('Date of birth', _buildDateDropdowns()),
                  const SizedBox(height: 15),
                  _buildTwoColumnRow('Email', _buildSimpleTextField(_emailController)),
                  const SizedBox(height: 15),
                  _buildTwoColumnRow('Contact', _buildContactTextField(_contactController)),
                  const SizedBox(height: 30),
                  Text(
                    'Company Details :',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildTwoColumnRow('Company Name', _buildSimpleTextField(_companyNameController)),
                  const SizedBox(height: 15),
                  _buildTwoColumnRow('Types of Users', _buildUserTypesDropdownField()),
                  const SizedBox(height: 30),
                  SizedBox(
                    width: 200, // Fixed width for the button
                    child: ElevatedButton(
                      onPressed: () async {
                        await _updateProfile();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors
                            .redAccent, // Background color (matches screenshot)
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: const Text(
                        'Save Changes',
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 30),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                // Upload Profile Picture section
                Container(
                  height: 400,
                  width: 400,
                  decoration: BoxDecoration(
                    color: Colors.grey[200], // Background for image upload box
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(color: Colors.grey, width: 1),
                  ),
                  child: IconButton(
                    onPressed: () async {
                      await _pickProfileImage();
                    },
                    icon: _selectedProfileImage != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              _selectedProfileImage!,
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Image.asset(
                            AssetsManager.addProfile,
                            width: 60,
                            height: 60,
                            color: Colors.grey,
                          ),
                  ),
                ),
                
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () async {
                    await _updateProfile();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors
                        .redAccent, // Background color (matches screenshot)
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 15),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: const Text(
                    'Upload Profile Picture',
                    style: TextStyle(fontSize: 16, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Widget for Settings Tab Content
  Widget _buildSettingTabContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(left: 40.0, right: 40.0, top: 30.0, bottom: 30.0),
      child: Column(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: 540, // Exact width to match reference image
              padding: const EdgeInsets.all(50), // Exact padding from reference
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.15),
                    spreadRadius: 2,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Change Password Section
                  Text(
                    'Change Password:',
                    style: TextStyle(
                      fontSize: responsiveFont(22).sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Current Password Field
                  _buildExactSettingsField(
                    'Current Password',
                    _currentPasswordController,
                    obscureText: true,
                    showValue: '********',
                    isPasswordVisible: _isCurrentPasswordVisible,
                    onToggleVisibility: () {
                      setState(() {
                        _isCurrentPasswordVisible = !_isCurrentPasswordVisible;
                      });
                    },
                  ),
                  const SizedBox(height: 18),

                  // New Password Field
                  _buildExactSettingsField(
                    'New Password',
                    _newPasswordController,
                    obscureText: true,
                    isPasswordVisible: _isNewPasswordVisible,
                    onToggleVisibility: () {
                      setState(() {
                        _isNewPasswordVisible = !_isNewPasswordVisible;
                      });
                    },
                  ),
                  const SizedBox(height: 18),

                  // Confirm New Password Field
                  _buildExactSettingsField(
                    'Confirm New Password',
                    _confirmNewPasswordController,
                    obscureText: true,
                    isPasswordVisible: _isConfirmPasswordVisible,
                    onToggleVisibility: () {
                      setState(() {
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                      });
                    },
                  ),
                  const SizedBox(height: 10),

                  // Password validation messages
                  if (_showPasswordValidation)
                    Padding(
                      padding: const EdgeInsets.only(left: 220, bottom: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // New password validation
                          if (_newPasswordController.text.isNotEmpty)
                            Builder(
                              builder: (context) {
                                final error = _validatePassword(_newPasswordController.text);
                                if (error != null) {
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 5),
                                    child: Text(
                                      error,
                                      style: TextStyle(
                                        fontSize: responsiveFont(16).sp,
                                        color: Colors.red[600],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),

                          // Password match validation
                          if (_newPasswordController.text.isNotEmpty && _confirmNewPasswordController.text.isNotEmpty)
                            Text(
                              _arePasswordsMatching()
                                  ? 'Passwords match ✓'
                                  : 'Passwords do not match ✗',
                              style: TextStyle(
                                fontSize: responsiveFont(16).sp,
                                color: _arePasswordsMatching()
                                    ? Colors.green[600]
                                    : Colors.red[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        ],
                      ),
                    ),

                  const SizedBox(height: 10),

                  // Save Password Button
                  Consumer(
                    builder: (context, ref, child) {
                      final userProfileState = ref.watch(userProfileNotifierProvider);
                      final isLoading = userProfileState.status == AppStatus.updating;

                      return SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: isLoading ? null : () async {
                        // Validate password fields
                        if (_currentPasswordController.text.trim().isEmpty) {
                          Utils().showSnackBar(context, 'Please enter your current password');
                          return;
                        }
                        if (_newPasswordController.text.trim().isEmpty) {
                          Utils().showSnackBar(context, 'Please enter a new password');
                          return;
                        }
                        if (_confirmNewPasswordController.text.trim().isEmpty) {
                          Utils().showSnackBar(context, 'Please confirm your new password');
                          return;
                        }

                        // Validate new password format
                        final passwordError = _validatePassword(_newPasswordController.text.trim());
                        if (passwordError != null) {
                          Utils().showSnackBar(context, passwordError);
                          return;
                        }

                        // Check if passwords match
                        if (!_arePasswordsMatching()) {
                          Utils().showSnackBar(context, 'New passwords do not match');
                          return;
                        }

                        try {
                          // Call reset password API using the notifier with success callback
                          await ref.read(userProfileNotifierProvider.notifier).resetPassword(
                            context,
                            _currentPasswordController.text.trim(),
                            _newPasswordController.text.trim(),
                            _confirmNewPasswordController.text.trim(),
                            onSuccess: () {
                              // Clear all password fields immediately when success callback is triggered
                              print('🔥 Success callback triggered - clearing fields immediately');
                              _currentPasswordController.clear();
                              _newPasswordController.clear();
                              _confirmNewPasswordController.clear();
                              print('🔥 Fields cleared via callback - Current: "${_currentPasswordController.text}", New: "${_newPasswordController.text}", Confirm: "${_confirmNewPasswordController.text}"');
                            },
                          );

                          // Check the final state after API call
                          final userProfileState = ref.read(userProfileNotifierProvider);

                          if (userProfileState.status == AppStatus.success) {
                            // Clear all password text fields after successful update
                            if (context.mounted) {
                              print('🔥 Clearing password fields after success');
                              _currentPasswordController.clear();
                              _newPasswordController.clear();
                              _confirmNewPasswordController.clear();
                              print('🔥 Password fields cleared - Current: "${_currentPasswordController.text}", New: "${_newPasswordController.text}", Confirm: "${_confirmNewPasswordController.text}"');
                            }
                          }
                          // Note: Success/error messages are now handled by the notifier with flushbar
                        } catch (e) {
                          // Handle any unexpected errors that might not be caught by the notifier
                          print('Unexpected error in UI: $e');
                        }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFE53E3E),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 0,
                          ),
                          child: isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  'Save Password',
                                  style: TextStyle(
                                    fontSize: responsiveFont(20).sp,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 30),

                  // Newsletter Section
                  Text(
                    'Newsletter:',
                    style: TextStyle(
                      fontSize: responsiveFont(22).sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Subscribe Newsletter Checkbox
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _subscribeNewsletter = !_subscribeNewsletter;
                          });
                        },
                        child: Text(
                          'Subscribe Newsletter',
                          style: TextStyle(
                            fontSize: responsiveFont(20).sp,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _subscribeNewsletter = !_subscribeNewsletter;
                          });
                        },
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: _subscribeNewsletter ? const Color(0xFFE53E3E) : Colors.white,
                            border: Border.all(
                              color: _subscribeNewsletter ? const Color(0xFFE53E3E) : Colors.grey[400]!,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: _subscribeNewsletter
                              ? const Icon(
                                  Icons.check,
                                  size: 14,
                                  color: Colors.white,
                                )
                              : null,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Remove Account Button - Outside the container at bottom
          const SizedBox(height: 30),
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: 540, // Same width as the white container above
              child: Center(
                child: Consumer(
                  builder: (context, ref, child) {
                    final userProfileState = ref.watch(userProfileNotifierProvider);
                    final isDeleteLoading = userProfileState.status == AppStatus.deleting;

                    return GestureDetector(
                      onTap: isDeleteLoading ? null : () async {
                    // Show confirmation dialog
                    final bool? confirmed = await Utils().showCommonConfirmDialog(
                      context: context,
                      title: 'Delete Account',
                      subtitle: 'Are you sure you want to delete your account?',
                      confirmText: 'Delete Account',
                      cancelText: 'Cancel',
                    );

                        if (confirmed == true) {
                          try {
                            // Call delete account API with success callback
                            await ref.read(userProfileNotifierProvider.notifier).deleteAccount(
                              context,
                              onSuccess: () async {
                                // Navigate to splash screen on successful deletion
                                print('🔥 Delete account success callback triggered');
                                if (context.mounted) {
                                  final localStorage = ref.read(localStorageProvider);
                                  await localStorage.clearLoginData();
                                  context.go('/splash');
                                }
                              },
                            );
                          } catch (e) {
                            print('🔥 Delete account error: $e');
                          }
                        }
                  },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                          decoration: BoxDecoration(
                            color: isDeleteLoading ? Colors.grey[200] : Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          child: isDeleteLoading
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        color: const Color(0xFFE53E3E),
                                        strokeWidth: 2,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Deleting...',
                                      style: TextStyle(
                                        fontSize: responsiveFont(20).sp,
                                        color: const Color(0xFFE53E3E),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  'Remove Account',
                                  style: TextStyle(
                                    fontSize: responsiveFont(20).sp,
                                    color: const Color(0xFFE53E3E),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Helper widget for input fields (reused from Profile tab, now with optional obscureText)
  Widget _buildInputField(String label, TextEditingController controller,
      {bool obscureText = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
        const SizedBox(height: 5),
        TextField(
          controller: controller,
          obscureText: obscureText,
          decoration: InputDecoration(
            isDense: true,
            // Reduce the vertical space
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Colors.grey[100], // Input field background color
          ),
          style: const TextStyle(fontSize: 16, color: Colors.black),
        ),
      ],
    );
  }

  // Helper widget for contact/phone number field with number-only input and max length
  Widget _buildContactField(String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
        const SizedBox(height: 5),
        TextField(
          controller: controller,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s\(\)]')), // Allow numbers, +, -, spaces, parentheses
            LengthLimitingTextInputFormatter(20), // Maximum 20 characters for global numbers
          ],
          decoration: InputDecoration(
            isDense: true,
            // Reduce the vertical space
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Colors.grey[100], // Input field background color
            hintText: '1234 567 8900', // Example format
            hintStyle: TextStyle(color: Colors.grey[400]),
          ),
          style: const TextStyle(fontSize: 16, color: Colors.black),
        ),
      ],
    );
  }

  // Helper widget for exact Settings tab input fields matching reference image
  Widget _buildExactSettingsField(String label, TextEditingController controller,
      {bool obscureText = false, String? showValue, bool? isPasswordVisible, VoidCallback? onToggleVisibility}) {
    return Row(
      children: [
        // Label
        SizedBox(
          width: 200,
          child: Text(
            label,
            style: TextStyle(
              fontSize: responsiveFont(22).sp,
              color: Colors.grey[700],
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const SizedBox(width: 20),
        // Input Field
        Expanded(
          child: Container(
            height: 36,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: TextField(
              controller: controller,
              obscureText: obscureText && !(isPasswordVisible ?? false),
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                border: InputBorder.none,
                hintText: showValue,
                hintStyle: TextStyle(
                  fontSize: responsiveFont(20).sp,
                  color: Colors.grey[600],
                ),
                suffixIcon: obscureText && onToggleVisibility != null
                    ? IconButton(
                        icon: Icon(
                          (isPasswordVisible ?? false) ? Icons.visibility : Icons.visibility_off,
                          color: Colors.grey[600],
                          size: 20,
                        ),
                        onPressed: onToggleVisibility,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      )
                    : null,
              ),
              style: TextStyle(
                fontSize: responsiveFont(20).sp,
                color: Colors.black87,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper widget for date field (from Profile tab) - Separate dropdowns like Figma
  Widget _buildDateField(String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
        const SizedBox(height: 5),
        Row(
          children: [
            // Day dropdown
            Expanded(
              flex: 1,
              child: DropdownButtonFormField<String>(
                value: _selectedDay,
                decoration: InputDecoration(
                  hintText: '12',
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
                items: List.generate(31, (index) => (index + 1).toString())
                    .map((day) => DropdownMenuItem(
                          value: day,
                          child: Text(day, style: const TextStyle(fontSize: 16, color: Colors.black)),
                        ))
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedDay = value;
                    _updateDateController();
                  });
                },
              ),
            ),
            const SizedBox(width: 8),
            // Month dropdown
            Expanded(
              flex: 1,
              child: DropdownButtonFormField<String>(
                value: _selectedMonth,
                decoration: InputDecoration(
                  hintText: 'Mar',
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
                items: const [
                  DropdownMenuItem(value: 'Jan', child: Text('Jan', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Feb', child: Text('Feb', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Mar', child: Text('Mar', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Apr', child: Text('Apr', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'May', child: Text('May', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Jun', child: Text('Jun', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Jul', child: Text('Jul', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Aug', child: Text('Aug', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Sep', child: Text('Sep', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Oct', child: Text('Oct', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Nov', child: Text('Nov', style: TextStyle(fontSize: 16, color: Colors.black))),
                  DropdownMenuItem(value: 'Dec', child: Text('Dec', style: TextStyle(fontSize: 16, color: Colors.black))),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedMonth = value;
                    _updateDateController();
                  });
                },
              ),
            ),
            const SizedBox(width: 8),
            // Year dropdown
            Expanded(
              flex: 1,
              child: DropdownButtonFormField<String>(
                value: _selectedYear,
                decoration: InputDecoration(
                  hintText: '1982',
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
                items: List.generate(100, (index) => (DateTime.now().year - index).toString())
                    .map((year) => DropdownMenuItem(
                          value: year,
                          child: Text(year, style: const TextStyle(fontSize: 16, color: Colors.black)),
                        ))
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedYear = value;
                    _updateDateController();
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method to update the date controller when dropdowns change
  void _updateDateController() {
    if (_selectedDay != null && _selectedMonth != null && _selectedYear != null) {
      _dateOfBirthController.text = '$_selectedDay $_selectedMonth $_selectedYear';
    }
  }

  // Helper widget for gender selection (from Profile tab)
  Widget _buildGenderSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Gender',
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
        Row(
          children: [
            Expanded(
              child: RadioListTile<int>(
                title: const Text('Male'),
                value: 0,
                groupValue: _selectedGender,
                activeColor: Colors.red, // Red radio button like in Figma
                onChanged: (int? value) {
                  setState(() {
                    _selectedGender = value!;
                  });
                },
              ),
            ),
            Expanded(
              child: RadioListTile<int>(
                title: const Text('Female'),
                value: 1,
                groupValue: _selectedGender,
                activeColor: Colors.red, // Red radio button like in Figma
                onChanged: (int? value) {
                  setState(() {
                    _selectedGender = value!;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper widget for user types dropdown
  Widget _buildUserTypesDropdown() {
    return Consumer(
      builder: (context, ref, child) {
        final userTypesState = ref.watch(userTypesNotifierProvider);

        // Debug information removed for production

        // Mock data for testing (remove this in production)
        final mockUserTypes = [
          UserType(id: 1, type: 'Admin'),
          UserType(id: 2, type: 'Manager'),
          UserType(id: 3, type: 'User'),
          UserType(id: 4, type: 'Guest'),
        ];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            // Show loading state
            if (userTypesState.status == AppStatus.loading)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 15),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: const Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 12),
                    Text('Loading user types...'),
                  ],
                ),
              )

            // Show error state
            else if (userTypesState.status == AppStatus.error)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 15),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: Text(
                  userTypesState.errorMessage ?? 'Failed to load user types',
                  style: TextStyle(color: Colors.red.shade700),
                ),
              )

            // Show dropdown when data is available
            else if (userTypesState.status == AppStatus.success && userTypesState.data != null)
              _buildSimpleWorkingDropdown(userTypesState.data!.userTypes)

            // Show fallback with mock data for testing
            else if (userTypesState.status == AppStatus.error || userTypesState.status == AppStatus.empty)
              Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.orange.shade300),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                    child: Text(
                      'API Error - Using mock data for testing',
                      style: TextStyle(color: Colors.orange.shade700, fontSize: 12),
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildSimpleWorkingDropdown(mockUserTypes),
                ],
              )

            // Show empty state
            else
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 15),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade100,
                ),
                child: const Text('No user types available'),
              ),
          ],
        );
      },
    );
  }

  // Simple working dropdown that matches profile form styling
  Widget _buildSimpleWorkingDropdown(List<UserType> userTypes) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Types of Users',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedUserType,
          decoration: InputDecoration(
            hintText: 'Select user type',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: const Color.fromARGB(255, 80, 170, 244), width: 1),
            ),
            filled: true,
            fillColor: Colors.grey.shade50, // Same background as other fields
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12), // Reduced height
          ),
          icon: const Icon(
            Icons.keyboard_arrow_down,
            color: Colors.red, // Red dropdown arrow like in Figma
            size: 24,
          ),
          items: userTypes.map((userType) {
            return DropdownMenuItem<String>(
              value: userType.type,
              child: Text(
                userType.type,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedUserType = value;
              _typesOfUsersController.text = value ?? '';
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a user type';
            }
            return null;
          },
        ),
      ],
    );
  }

  // Widget for Storage Tab Content
  Widget _buildStorageTabContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 30.0, bottom: 30.0, right: 50, left: 50),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side - Storage overview with circular progress
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(50),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blackColor.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 24,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Circular progress indicator without center text
                  SizedBox(
                    width: 220,
                    height: 220,
                    child: CircularProgressIndicator(
                      value: 0.75, // 75GB of 100GB
                      strokeWidth: 35,
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF007AFF)),
                      strokeCap: StrokeCap.round,
                    ),
                  ),
                  const SizedBox(height: 40),
                  // "75GB used of 100GB" text with larger font size
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: '75GB',
                          style: TextStyle(
                            fontSize: responsiveFont(26).sp,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF007AFF),
                            fontFamily: 'inter',
                          ),
                        ),
                        TextSpan(
                          text: ' used of ',
                          style: TextStyle(
                            fontSize: responsiveFont(26).sp,
                            color: Colors.grey[600],
                            fontFamily: 'inter',
                          ),
                        ),
                        TextSpan(
                          text: '100GB',
                          style: TextStyle(
                            fontSize: responsiveFont(26).sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                            fontFamily: 'inter',
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 15),
                  // Cloud icon with storage text
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.cloud_outlined,
                        color: Color(0xFF007AFF),
                        size: 18,
                      ),
                      const SizedBox(width: 5),
                      Text(
                        'Storage(78% Full)',
                        style: TextStyle(
                          fontSize: responsiveFont(18).sp,
                          color: Colors.grey[600],
                          fontFamily: 'inter'
                        )
                      ),
                    ],
                  ),
                  const SizedBox(height: 25),
                  // Storage breakdown legend with labels
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStorageLegendItemWithLabel(Colors.red, '100GB', 'Total'),
                      _buildStorageLegendItemWithLabel(Color(0xFF007AFF), '75GB', 'Used'),
                      _buildStorageLegendItemWithLabel(Colors.grey[300]!, '25GB', 'Remaining'),
                    ],
                  ),
                  const SizedBox(height: 40),
                  // Get More Storage button
                  SizedBox(
                    width: 216,
                    child: ElevatedButton(
                      onPressed: () {
                        // Handle get more storage
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xFFFF4444),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Get More Storage',
                        style: TextStyle(
                          fontSize: responsiveFont(20).sp,
                          color: Colors.white,
                          fontFamily: 'inter',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 30),
          // Right side - Storage breakdown by category
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blackColor.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 24,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: Column(
                children: [
                  _buildStorageItem('Cookbook', AssetsManager.profileCookbook, 90, Colors.green),
                  const SizedBox(height: 30),
                  _buildStorageItem('Recipe', AssetsManager.profileReciepe, 8, Colors.orange),
                  const SizedBox(height: 30),
                  _buildStorageItem('Meal Plan', AssetsManager.profileMealplan, 8, Colors.red),
                  const SizedBox(height: 30),
                  _buildStorageItem('Videos', AssetsManager.profileVideos, 10, Colors.purple),
                  const SizedBox(height: 30),
                  _buildStorageItem('Images', AssetsManager.profileImages, 10, Colors.blue),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget for storage legend items with labels
  Widget _buildStorageLegendItemWithLabel(Color color, String value, String label) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: responsiveFont(18).sp,
                color: Colors.black87,
                fontFamily: 'inter',
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: responsiveFont(15).sp,
            color: Colors.grey[600],
            fontFamily: 'inter',
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  // Helper widget for storage breakdown items
  Widget _buildStorageItem(String title, String iconPath, int sizeGB, Color progressColor) {
    // Format GB display to match image (08GB for single digits)
    String formattedSize = sizeGB < 10 ? '0${sizeGB}GB' : '${sizeGB}GB';

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Icon
        Image.asset(
          iconPath,
          width: 70,
          height: 70,
          fit: BoxFit.contain,
        ),
        const SizedBox(width: 25),
        // Title and progress bar section - centered vertically
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Title and size text row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: responsiveFont(22).sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                      fontFamily: 'inter',
                    ),
                  ),
                  Text(
                    formattedSize,
                    style: TextStyle(
                      fontSize: responsiveFont(22).sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      fontFamily: 'inter',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Progress bar with visible gray background
              Container(
                height: 12,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    // Filled progress portion
                    FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: sizeGB / 100.0, // Convert GB to percentage
                      child: Container(
                        height: 12,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: progressColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Two-column layout helper method for Figma design
  Widget _buildTwoColumnRow(String label, Widget field) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Label
        Expanded(
          flex: 2,
          child: Padding(
            padding: const EdgeInsets.only(top: 12.0), // Align with field content
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(width: 20),
        // Right column - Field
        Expanded(
          flex: 3,
          child: field,
        ),
      ],
    );
  }

  // Simple text field without label (for two-column layout)
  Widget _buildSimpleTextField(TextEditingController controller) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: Colors.grey[100],
      ),
      style: const TextStyle(fontSize: 16, color: Colors.black),
    );
  }

  // Contact text field without label (for two-column layout) with country picker
  Widget _buildContactTextField(TextEditingController controller) {
    // Define common countries list
    final List<Country> commonCountries = [
      CountryPickerUtils.getCountryByIsoCode('US'),
      CountryPickerUtils.getCountryByIsoCode('CA'),
      CountryPickerUtils.getCountryByIsoCode('GB'),
      CountryPickerUtils.getCountryByIsoCode('AU'),
      CountryPickerUtils.getCountryByIsoCode('IN'),
      CountryPickerUtils.getCountryByIsoCode('DE'),
      CountryPickerUtils.getCountryByIsoCode('FR'),
      CountryPickerUtils.getCountryByIsoCode('JP'),
      CountryPickerUtils.getCountryByIsoCode('CN'),
      CountryPickerUtils.getCountryByIsoCode('BR'),
      CountryPickerUtils.getCountryByIsoCode('MX'),
      CountryPickerUtils.getCountryByIsoCode('IT'),
      CountryPickerUtils.getCountryByIsoCode('ES'),
      CountryPickerUtils.getCountryByIsoCode('RU'),
      CountryPickerUtils.getCountryByIsoCode('KR'),
    ];

    return Row(
      children: [
        // Country picker dropdown with overlay to render above tab bar
        SizedBox(
          height: 40,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<Country>(
              value: _selectedCountry,
              icon: const Icon(Icons.arrow_drop_down, size: 20),
              elevation: 16, // High elevation to appear above tab bar
              style: const TextStyle(color: Colors.black, fontSize: 14),
              dropdownColor: Colors.white,
              borderRadius: BorderRadius.circular(8),
              menuMaxHeight: 200, // Limit dropdown height
              items: commonCountries
                  .map<DropdownMenuItem<Country>>((Country country) {
                return DropdownMenuItem<Country>(
                  value: country,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CountryPickerUtils.getDefaultFlagImage(country),
                        const SizedBox(width: 8),
                        Text(
                          '+${country.phoneCode}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
              onChanged: (Country? country) {
                if (country != null) {
                  setState(() {
                    _selectedCountry = country;
                  });
                }
              },
              selectedItemBuilder: (BuildContext context) {
                return commonCountries.map<Widget>((Country country) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CountryPickerUtils.getDefaultFlagImage(country),
                        const SizedBox(width: 4),
                        Text(
                          '+${country.phoneCode}',
                          style: const TextStyle(fontSize: 14, color: Colors.black),
                        ),
                      ],
                    ),
                  );
                }).toList();
              },
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Contact number text field
        Expanded(
          child: TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(15), // Global phone number limit
            ],
            decoration: InputDecoration(
              isDense: true,
              contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            style: const TextStyle(fontSize: 16, color: Colors.black),
          ),
        ),
      ],
    );
  }



  // Gender radio buttons without label (for two-column layout)
  Widget _buildGenderRadioButtons() {
    return Row(
      children: [
        Expanded(
          child: RadioListTile<int>(
            title: const Text('Male'),
            value: 0,
            groupValue: _selectedGender,
            activeColor: Colors.red,
            contentPadding: EdgeInsets.zero,
            onChanged: (int? value) {
              setState(() {
                _selectedGender = value!;
              });
            },
          ),
        ),
        Expanded(
          child: RadioListTile<int>(
            title: const Text('Female'),
            value: 1,
            groupValue: _selectedGender,
            activeColor: Colors.red,
            contentPadding: EdgeInsets.zero,
            onChanged: (int? value) {
              setState(() {
                _selectedGender = value!;
              });
            },
          ),
        ),
      ],
    );
  }

  // Date dropdowns without label (for two-column layout)
  Widget _buildDateDropdowns() {
    return Row(
      children: [
        // Day dropdown
        Expanded(
          flex: 1,
          child: DropdownButtonFormField<String>(
            value: _selectedDay,
            decoration: InputDecoration(
              hintText: '12',
              isDense: true,
              contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            items: List.generate(31, (index) => (index + 1).toString())
                .map((day) => DropdownMenuItem(
                      value: day,
                      child: Text(day, style: const TextStyle(fontSize: 16, color: Colors.black)),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _selectedDay = value;
                _updateDateController();
              });
            },
          ),
        ),
        const SizedBox(width: 8),
        // Month dropdown
        Expanded(
          flex: 1,
          child: DropdownButtonFormField<String>(
            value: _selectedMonth,
            decoration: InputDecoration(
              hintText: 'Mar',
              isDense: true,
              contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            items: const [
              DropdownMenuItem(value: 'Jan', child: Text('Jan', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Feb', child: Text('Feb', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Mar', child: Text('Mar', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Apr', child: Text('Apr', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'May', child: Text('May', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Jun', child: Text('Jun', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Jul', child: Text('Jul', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Aug', child: Text('Aug', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Sep', child: Text('Sep', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Oct', child: Text('Oct', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Nov', child: Text('Nov', style: TextStyle(fontSize: 16, color: Colors.black))),
              DropdownMenuItem(value: 'Dec', child: Text('Dec', style: TextStyle(fontSize: 16, color: Colors.black))),
            ],
            onChanged: (value) {
              setState(() {
                _selectedMonth = value;
                _updateDateController();
              });
            },
          ),
        ),
        const SizedBox(width: 8),
        // Year dropdown
        Expanded(
          flex: 1,
          child: DropdownButtonFormField<String>(
            value: _selectedYear,
            decoration: InputDecoration(
              hintText: '1982',
              isDense: true,
              contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            items: List.generate(100, (index) => (DateTime.now().year - index).toString())
                .map((year) => DropdownMenuItem(
                      value: year,
                      child: Text(year, style: const TextStyle(fontSize: 16, color: Colors.black)),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _selectedYear = value;
                _updateDateController();
              });
            },
          ),
        ),
      ],
    );
  }

  // User types dropdown field without label (for two-column layout)
  Widget _buildUserTypesDropdownField() {
    return Consumer(
      builder: (context, ref, child) {
        final userTypesState = ref.watch(userTypesNotifierProvider);

        // Mock data for testing (remove this in production)
        final mockUserTypes = [
          UserType(id: 1, type: 'Admin'),
          UserType(id: 2, type: 'Manager'),
          UserType(id: 3, type: 'User'),
        ];

        // Show loading state
        if (userTypesState.status == AppStatus.loading) {
          return Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        }

        // Show dropdown when data is available
        else if (userTypesState.status == AppStatus.success && userTypesState.data != null) {
          return _buildSimpleUserTypesDropdown(userTypesState.data!.userTypes);
        }

        // Show fallback with mock data for testing
        else if (userTypesState.status == AppStatus.error || userTypesState.status == AppStatus.empty) {
          return _buildSimpleUserTypesDropdown(mockUserTypes);
        }

        // Show empty state
        else {
          return Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text('No user types available', style: TextStyle(color: Colors.grey)),
            ),
          );
        }
      },
    );
  }

  // Simple user types dropdown without label
  Widget _buildSimpleUserTypesDropdown(List<UserType> userTypes) {
    return DropdownButtonFormField<String>(
      value: _selectedUserType,
      decoration: InputDecoration(
        hintText: 'Select user type',
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: Colors.grey[100],
      ),
      icon: const Icon(
        Icons.keyboard_arrow_down,
        color: Colors.red,
        size: 24,
      ),
      items: userTypes.map((userType) {
        return DropdownMenuItem<String>(
          value: userType.type,
          child: Text(
            userType.type,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedUserType = value;
          _typesOfUsersController.text = value ?? '';
          // Find and set the corresponding user type ID
          final selectedUserTypeObj = userTypes.firstWhere(
            (userType) => userType.type == value,
            orElse: () => UserType(id: 0, type: ''),
          );
          _selectedUserTypeId = selectedUserTypeObj.id;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select a user type';
        }
        return null;
      },
    );
  }
}

class SubscriptionTabContent extends ConsumerStatefulWidget {
  const SubscriptionTabContent({super.key});

  @override
  ConsumerState<SubscriptionTabContent> createState() => _SubscriptionTabContentState();
}

class _SubscriptionTabContentState extends ConsumerState<SubscriptionTabContent> {

  @override
  void initState() {
    super.initState();
    // Fetch plans when the widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(plansNotifierProvider.notifier).fetchPlans();
    });
  }

  // Helper method to check if a feature is available for a plan
  bool _isFeatureAvailable(Plan plan, String featureName) {
    return plan.features?.any((feature) =>
      feature.featureName?.toLowerCase().contains(featureName.toLowerCase()) ?? false
    ) ?? false;
  }



  @override
  Widget build(BuildContext context) {
    final plansState = ref.watch(plansNotifierProvider);

    return Column(
      children: [
        // Show loading state
        if (plansState.status == AppStatus.loading)
          const Expanded(
            child: Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryColor,
              ),
            ),
          )
        // Show error state
        else if (plansState.status == AppStatus.error)
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load plans',
                    style: TextStyle(
                      fontSize: responsiveFont(20).sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    plansState.errorMessage ?? 'Unknown error occurred',
                    style: TextStyle(
                      fontSize: responsiveFont(16).sp,
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(plansNotifierProvider.notifier).fetchPlans();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                    ),
                    child: const Text(
                      'Retry',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          )
        // Show plans data
        else if (plansState.data?.plans != null && plansState.data!.plans!.isNotEmpty)
          ..._buildPlansContent(plansState.data!.plans!)
        // Show empty state
        else
          const Expanded(
            child: Center(
              child: Text(
                'No plans available',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
      ],
    );
  }

  List<Widget> _buildPlansContent(List<Plan> plans) {
    // Get current plan name from user profile
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final currentPlanName = userProfileState.data?.userProfile?.currentPlan?.planName;

    // Get all unique features from all plans
    final Set<String> allFeatures = <String>{};
    for (final plan in plans) {
      for (final feature in plan.features ?? []) {
        if (feature.featureName != null) {
          allFeatures.add(feature.featureName!);
        }
      }
    }
    final List<String> featuresList = allFeatures.toList();

    return [
      // Column headers
      Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 42, top: 20),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'Features',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: responsiveFont(25).sp,
                  color: Colors.grey[700],
                ),
              ),
            ),
            for (int i = 0; i < plans.length; i++)
              Expanded(
                child: Column(
                  children: [
                    Stack(
                      alignment: Alignment.topCenter,
                      children: [
                        if (plans[i].planName == currentPlanName) // Current Plan (Dynamic)
                          Positioned(
                            top: 0,
                            child: Text(
                              "Current Plan",
                              style: TextStyle(
                                fontSize: responsiveFont(20),
                                fontWeight: FontWeight.w600,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        if (i == 2) // Popular Plan (Premium Plan)
                          Positioned(
                            top: 0,
                            child: Container(
                              color: Colors.red,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 15, vertical: 2),
                              child: Text(
                                "POPULAR",
                                style: TextStyle(
                                  fontSize: responsiveFont(16),
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        Padding(
                          padding: const EdgeInsets.only(top: 30),
                          child: Text(
                            plans[i].planName ?? 'Unknown Plan',
                            style: TextStyle(
                              fontSize: responsiveFont(25),
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              color: AppColors.primaryGreyColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),

      // List of features
      Expanded(
        child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 30), // 30px bottom spacing from tab bar
          itemCount: featuresList.length + 1, // +1 for the Update Plan row
          itemBuilder: (context, index) {
            if (index < featuresList.length) {
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 42),
                decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300)),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        featuresList[index],
                        style: TextStyle(
                          fontSize: responsiveFont(21).sp,
                          fontFamily: 'inter',
                          fontWeight: FontWeight.w500,
                          color: AppColors.primaryGreyColor,
                        ),
                      ),
                    ),
                    for (int i = 0; i < plans.length; i++)
                      Expanded(
                        child: Center(
                          child: Image.asset(
                            _isFeatureAvailable(plans[i], featuresList[index])
                                ? AssetsManager.featureAvailable
                                : AssetsManager.featureNotAvailable,
                            width: 13,
                            height: 13,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            } else {
              // Row with Update Plan buttons
              return Container(
                  padding: const EdgeInsets.only(
                      top: 20.0, right: 20.0, left: 20.0, bottom: 0.0),
                  child: Row(children: [
                    SizedBox(
                      width: 200,
                      child: ElevatedButton(
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Update Plan button clicked!'),
                              duration: Duration(seconds: 1),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          // Matches image
                          padding: const EdgeInsets.symmetric(vertical: 15.0),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          elevation: 5.0,
                          minimumSize: const Size.fromHeight(50),
                        ),
                        child: Text(
                          'Update Plan',
                          style: TextStyle(
                            fontSize: responsiveFont(18),
                            color: Colors.white,
                            fontFamily: 'inter',
                            fontWeight: FontWeight.w500, // Bold text
                          ),
                        ),
                      ),
                    ),
                    Container(width: 1, color: Colors.grey[200]),
                    const SizedBox(width: 550),
                    Expanded(
                      // Total flex for the three plan columns
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        // Distribute space
                        children: [
                          for (int i = 0; i < plans.length; i++) ...[
                            if (i > 0) const SizedBox(width: 20),
                            Expanded(
                              child: FreemiumPlanButton(
                                priceText: plans[i].yearlyFee != null
                                    ? '\$${plans[i].yearlyFee}'
                                    : 'Free',
                                billingCycleText: plans[i].yearlyFee != null
                                    ? '/Yearly'
                                    : '/Lifetime',
                                planNameText: plans[i].planName ?? 'Unknown Plan',
                                onPressed: () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content: Text(
                                            '${plans[i].planName} button tapped!')),
                                  );
                                },
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ]));
            }
          },
        ),
      ),
    ];
  }
}

class FreemiumPlanButton extends StatelessWidget {
  final String priceText;
  final String billingCycleText;
  final String planNameText;
  final VoidCallback? onPressed;
  final bool isPopular;

  const FreemiumPlanButton({
    super.key,
    required this.priceText,
    required this.billingCycleText,
    required this.planNameText,
    this.onPressed,
    this.isPopular = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.0),
          side: BorderSide(
            color: Colors.grey[300]!,
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Keep column compact
        children: [
          const SizedBox(height: 8.0),
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.baseline, // Align baselines
            textBaseline: TextBaseline.alphabetic, // Keep row compact
            children: [
              Text(
                priceText,
                style: TextStyle(
                  fontSize: responsiveFont(35).sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  color: AppColors
                      .primaryColor, // Red for "Free", white for popular
                ),
              ),
              Text(
                billingCycleText,
                style: TextStyle(
                  fontSize: responsiveFont(15).sp,
                  color: AppColors
                      .textGreyColor, // Grey for "/Lifetime", white70 for popular
                ),
              ),
            ],
          ),
          Text(
            planNameText,
            style: TextStyle(
              fontSize: responsiveFont(25).sp,
              fontWeight: FontWeight.w600,
              color: AppColors.primaryGreyColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8.0)
        ],
      ),
    );
  }
}
