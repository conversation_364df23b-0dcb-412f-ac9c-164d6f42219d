import 'package:flutter_svg/flutter_svg.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../../core/providers/profile/user_profile_notifier.dart';

final homeScreenProvider = Provider((ref) => HomeScreenController());
final TextEditingController searchController = TextEditingController();

class HomeScreenController {
  List<HomeIcon> get icons => [
        HomeIcon('Cookbooks', AssetsManager.cookbook_svg, "cookbook"),
        HomeIcon('Meals', AssetsManager.meals_svg, "meal"),
        HomeIcon('Shopping', AssetsManager.shopping_svg, "shopping"),
        HomeIcon('Tips', AssetsManager.tips_svg, "tip"),
        HomeIcon('Favorites', AssetsManager.fav_svg, "fav"),
        HomeIcon('MasterList', AssetsManager.masterList_svg, "masterList"),
        HomeIcon('Media', AssetsManager.media_svg, "media"),
        HomeIcon('GPT', AssetsManager.gpt_svg, "gpt"),
        HomeIcon('Sync', AssetsManager.sync_svg, "sync"),
        HomeIcon('Backup', AssetsManager.backup_svg, "backUp"),
        HomeIcon('Account', AssetsManager.myProfile, "account"),
      ];
}

class HomeIcon {
  final String label;
  final String id;
  final String assetPath;

  HomeIcon(this.label, this.assetPath, this.id);
}

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final icons = ref.watch(homeScreenProvider).icons;

    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              SizedBox(
                height: 100.h,
              ),

              // Top Search Bar
              CustomSearchBar(
                width: 500.w,
                hintText: "Search",
                controller: searchController,
                autoFocus: true,
                formats: [
                  FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9\s,]')),
                ],
              ),

              SizedBox(
                height: 80.h,
              ),
              LayoutBuilder(
                builder: (context, constraints) {
                  final double totalWidth = constraints.maxWidth;
                  final int iconsPerRow = 6;
                  final double spacing = 20.w;

                  // Calculate ideal icon size based on spacing and screen width
                  final double availableWidth =
                      totalWidth - (spacing * (iconsPerRow - 1));
                  final double iconSize =
                      (availableWidth / iconsPerRow).clamp(250.w, 250.w);

                  final firstRow = icons.take(6).toList();
                  final secondRow = icons.skip(6).toList();

                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // First row: exactly 6 icons
                      Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: firstRow
                              .map((icon) => Padding(
                                    padding: EdgeInsets.only(right: 10.w),
                                    child:
                                        _buildHomeIcon(icon, iconSize, context),
                                  ))
                              .toList()),
                      SizedBox(height: 32.h),

                      // Second row: 4 icons, centered
                      if (secondRow.isNotEmpty)
                        Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: secondRow
                                .map((icon) => Padding(
                                      padding: EdgeInsets.only(right: 10.w),
                                      child: _buildHomeIcon(
                                          icon, iconSize, context),
                                    ))
                                .toList()),
                    ],
                  );
                },
              )
            ],
          )
          //  const Spacer(),
        ],
      ),
    );
  }

  Widget _buildHomeIcon(HomeIcon item, double width, BuildContext context) {
    return GestureDetector(
      onTap: () {
        print(item.id);
        if (item.id == "gpt") {
          context.go('/home/<USER>');
        } else if (item.id == "meal") {
          context.go('/home/<USER>');
        } else if (item.id == "masterList") {
          context.go('/home');
        } else if (item.id == "account") {
          context.go(Routes.myAccount);
        } else {
          context.go('/${item.id}');
        }
      },
      child: SizedBox(
        width: width,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(item.assetPath, width: 160.w, height: 160.h),
            SizedBox(height: 10.h),
            Text(
              item.label,
              style: context.theme.textTheme.titleMedium!
                  .copyWith(fontSize: 24.sp, fontWeight: FontWeight.w400),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
