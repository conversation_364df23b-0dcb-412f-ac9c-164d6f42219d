import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../../../../core/widgets/custom_doted_lines.dart';
import '../../cookbook/widgets/custom_desc_text.dart';

class MenuTile extends StatelessWidget {
  final Map<String, dynamic> data;
  final bool isSelected;

  const MenuTile({super.key, required this.data , this.isSelected = false,});

  @override
  Widget build(BuildContext context) {
    return  Card(
      elevation: 5,
      color: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
        side: isSelected
            ? BorderSide(color:AppColors.selectionColor, width: 3)
            : BorderSide(color: AppColors.greyBorderColor, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 15.sp , bottom: 15.sp, left: 15.sp , right: 15.sp),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Image Section
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.asset(
                AssetsManager.mealPlan,
                width: 100.w,
                height: 110.h,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 15.w),
            // Text and Action Section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // SizedBox(height: 5.h,),
                  // Title and icons row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          data["title"],
                          style: context.theme.textTheme.displaySmall!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontSize: 25.sp,
                            fontWeight: FontWeight.w400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Row(
                        children: [
                          SvgPicture.asset(
                            AssetsManager.share,
                            height: 30.h,
                            width: 30.w,
                          ),
                          SizedBox(width: 20.w),
                          SvgPicture.asset(
                            AssetsManager.more,
                            height: 30.h,
                            width: 30.w,
                          ),
                        ],
                      ),
                    ],
                  ),

                  SizedBox(height: 5.h),
                  CustomDescText(
                    desc: data["note"],
                    size: 20.sp,
                    textColor: AppColors.textGreyColor,
                  ),
                  SizedBox(height: 15.h),
                  CustomPaint(
                    painter: DottedLinePainter(
                      strokeWidth: 1,
                      dashWidth: 6,
                      color: AppColors.lightGreyColor,
                    ),
                    size: Size(double.infinity, 2),
                  ),
                  SizedBox(height: 5.h),
                  // Footer: recipes count and creation date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomDescText(
                            desc: "5 Items",
                            size: 20.sp,
                            textColor: AppColors.textGreyColor,
                          ),
                        ],
                      ),
                      SizedBox(
                        width: 40.w,
                      ),
                      Center(child: CustomDescText(desc: "5 servings" ,  size: 20.sp,  textColor: AppColors.textGreyColor,))
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }


}

Widget dottedDivider = Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: List.generate(
    10, // Number of dots
        (_) => Container(
      width: 10,
      height: 2,
      decoration: BoxDecoration(
        color: AppColors.lightestGreyColor,
        shape: BoxShape.circle,
      ),
    ),
  ),
);
