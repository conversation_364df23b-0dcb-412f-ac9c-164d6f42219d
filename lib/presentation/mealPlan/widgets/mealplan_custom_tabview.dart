
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class MealPlanCustomTabview extends StatelessWidget {
  final List<String> tabs;
  final String selected;
  final double ? height;
  final double ? width;
  final double ? fontSize;
  final void Function(String) onChanged;

  const MealPlanCustomTabview({  super.key,
    required this.tabs,
    required this.selected,
    required this.onChanged,
    required this.fontSize,
    this.height,
    this.width
  });

  @override
  Widget build(BuildContext context) {
    return Container(

      //margin: EdgeInsets.only(left: 20.w , right: 20.w),

      decoration: BoxDecoration(
        color: AppColors.primaryColor,
        borderRadius: BorderRadius.circular(10),
        border: Border(
          left: BorderSide(color: Colors.black.withOpacity(0.1), width: 1),
          top: BorderSide(color: Colors.black.withOpacity(0.1), width: 1),
          bottom: BorderSide(color: Colors.black.withOpacity(0.1), width: 1),
          // No right border
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: tabs.map((tab) {
          final isSelected = tab == selected;
          return GestureDetector(
            onTap: () => onChanged(tab),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              padding: EdgeInsets.only(top: 15.h , bottom: 15.h , left: 20.w , right: 20.w),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white : Colors.transparent,
                borderRadius: BorderRadius.circular(10),

              ),
              child: SizedBox(
                width: width ?? 100.w,
                child: Center(
                  child: Text(tab ,  style: context.theme.textTheme.labelMedium!.copyWith(
                    color: Colors.black,
                    fontSize: fontSize,
                    fontWeight: FontWeight.normal,
                  )),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
