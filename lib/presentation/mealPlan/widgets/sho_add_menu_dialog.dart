import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import '../../../../../app/imports/packages_imports.dart';

final cookbookNameProvider = StateProvider<String>((ref) => '');
final isCookbookCreatedProvider = StateProvider<bool>((ref) => false);

class AddMenuItemDialog extends HookConsumerWidget {
  const AddMenuItemDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController notesController = TextEditingController();
    String selectedMealType = 'Lunch';
    String selectedPortion = '1';
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 10,
      backgroundColor: Colors.white,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        // important to clip children also
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(
            horizontal: 20, // left & right
            vertical: 10, // top & bottom
          ),
          width: 850.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: SvgPicture.asset(
                    AssetsManager.cross,
                    height: 40.h,
                    width: 40.w,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),

              /// Title & Close Button
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Add Menu",
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w600,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),

              SizedBox(height: 35.h),

              /// Image Placeholder
              GestureDetector(
                  onTap: () {
                    // Add image picker logic here
                  },
                  child: DottedBorder(
                    color: Colors.grey,
                    strokeWidth: 1,
                    borderType: BorderType.RRect,
                    radius: Radius.circular(12),
                    dashPattern: [4, 4],
                    child: Container(
                      height: 130,
                      width: 150,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100, // light background
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          AssetsManager.addImage,
                          height: 50.h,
                          width: 50.w,
                          color: AppColors.lightGreyColor,
                        ),
                      ),
                    ),
                  )),
              const SizedBox(height: 6),
              Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      AssetsManager.addImage,
                      height: 30.h,
                      width: 30.w,
                    ),
                    const SizedBox(width: 3), // spacing between icon and text
                    const Text("Add Menu Image",
                        style: TextStyle(
                            color: AppColors.primaryGreyColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 14)),
                  ],
                ),
              ),

              SizedBox(height: 30.h),

              /// Meal Type Dropdown
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 50.w),
                // Horizontal padding
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// Title Input
                    buildLabel("Title:", context),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                            color: AppColors.lightestGreyColor, width: 1),
                        borderRadius: BorderRadius.circular(8),
                        // boxShadow: const [
                        //   BoxShadow(
                        //     color: Colors.black12,
                        //     blurRadius: 4,
                        //     offset: Offset(0, 1),
                        //   ),
                        // ],
                      ),
                      child: SizedBox(
                        height: 60.h, // Set your desired width here
                        child: TextField(
                          style: context.theme.textTheme.displaySmall!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 18.sp,
                          ),
                          controller: titleController,
                          decoration: InputDecoration(
                            filled: context.theme.inputDecorationTheme.filled,
                            fillColor:
                                context.theme.inputDecorationTheme.fillColor,
                            hintStyle: context
                                .theme.inputDecorationTheme.hintStyle
                                ?.copyWith(
                              fontSize: 18.sp,
                            ),
                            errorStyle:
                                context.theme.inputDecorationTheme.errorStyle,
                            hintText: "Untitled",
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5),
                            ),
                            suffixIcon: Padding(
                              padding: EdgeInsets.all(8),
                              // optional: adds spacing inside the field
                              child: SvgPicture.asset(
                                AssetsManager.clear,
                                width: 16, // reduce width
                                height: 16, // reduce height
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 10.h),

                    /// Meal Type Dropdown
                    buildLabel("Meal Type:", context),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                            color: AppColors.lightestGreyColor, width: 1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: SizedBox(
                        height: 60.h,
                        child: DropdownButtonFormField<String>(
                          isDense: true,
                          value: selectedMealType,
                          style: context.theme.textTheme.displaySmall!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 16.sp,
                          ),
                          icon: SvgPicture.asset(
                            AssetsManager.droparrow,
                            height: 24,
                            width: 24,
                          ),
                          items: ['Breakfast', 'Lunch', 'Dinner']
                              .map((type) => DropdownMenuItem(
                            value: type,
                            child: Text(type),
                          ))
                              .toList(),
                          onChanged: (val) => selectedMealType = val!,
                          decoration: const InputDecoration(
                            isDense: true,
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            fillColor: Colors.white,
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 10.h),

                    /// Portion Dropdown
                    buildLabel("Meal Portion:", context),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: AppColors.lightestGreyColor,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: SizedBox(
                        height: 60.h,
                        child: DropdownButtonFormField<String>(
                          isDense: true, // removes default vertical padding
                          style: context.theme.textTheme.displaySmall!.copyWith(
                            color: AppColors.primaryGreyColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 16.sp,
                          ),
                          icon: SvgPicture.asset(
                            AssetsManager.droparrow,
                            height: 24,
                            width: 24,
                          ),
                          value: selectedPortion,
                          items: ['1', '2', '3', '4', '5']
                              .map((portion) => DropdownMenuItem(
                              value: portion, child: Text(portion)))
                              .toList(),
                          onChanged: (val) => selectedPortion = val!,
                          decoration: const InputDecoration(
                            isDense: true,
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            border: OutlineInputBorder(
                              borderSide: BorderSide.none,
                            ),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 10.h),

                    /// Notes
                    buildLabel("Menu Notes:", context),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                            color: AppColors.lightestGreyColor, width: 1),
                        borderRadius: BorderRadius.circular(8),
                        // boxShadow: const [
                        //   BoxShadow(
                        //     color: Colors.black12,
                        //     blurRadius: 4,
                        //     offset: Offset(0, 1),
                        //   ),
                        // ],
                      ),
                      child: TextField(
                        style: context.theme.textTheme.displaySmall!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontWeight: FontWeight.w400,
                          fontSize:
                             16.sp,
                        ),
                        controller: notesController,
                        decoration: InputDecoration(
                          filled: context.theme.inputDecorationTheme.filled,
                          fillColor:
                              context.theme.inputDecorationTheme.fillColor,
                          hintStyle: context
                              .theme.inputDecorationTheme.hintStyle
                              ?.copyWith(
                            fontSize: 18.sp,
                          ),
                          errorStyle:
                              context.theme.inputDecorationTheme.errorStyle,
                          hintText: "Description",
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                    ),

                    const SizedBox(height: 24),

                    /// Save Button
                    Center(
                      child: CustomButton(
                        text: "Save",
                        onPressed: () => Navigator.pop(context),
                        width: 250.w,
                        height: 55.h,
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget buildLabel(String text, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0, left: 2),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(text,
            style: Theme.of(context).textTheme.displaySmall!.copyWith(
                  color: AppColors.primaryLightTextColor,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w400,
                )),
      ),
    );
  }
}
