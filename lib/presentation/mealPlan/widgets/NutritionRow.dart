import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class NutritionRow extends StatelessWidget {
  final String name;
  final String? percent;
  final bool isBold;
  final bool indent;

  const NutritionRow({
    required this.name,
    this.percent,
    this.isBold = false,
    this.indent = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: indent ? 16.0 : 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            name,
            style: context.theme.textTheme.displaySmall!.copyWith(
                fontSize: 16.sp,
                color: context.theme.hintColor,
                fontWeight: isBold ? FontWeight.w700 : FontWeight.w400),
          ),

          if (percent != null)
            Padding(
              padding: EdgeInsets.only(right: 16.0), // <-- End (right) padding
              child: Text(
                percent!,
                style: context.theme.textTheme.displaySmall!.copyWith(
                  fontSize: 16.sp,
                  color: context.theme.hintColor,
                  fontWeight: isBold ? FontWeight.w700 : FontWeight.w700,
                ),
              ),
            )


        ],
      ),
    );
  }
}
