import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/mealPlan/widgets/NutritionDialog.dart';
import 'package:mastercookai/presentation/mealPlan/widgets/meal_plan_tile.dart';
import '../../../../app/assets_manager.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../core/providers/mealplan/meal_plan_provider.dart';
import '../cookbook/widgets/custom_desc_text.dart';
import '../shopping/sub_view/custom_tabview.dart';
import 'meal_plan_screen.dart';
import 'menu_screen.dart';
import 'widgets/custom_add_item_tile.dart';
import 'widgets/menu_tile.dart';
import 'widgets/sho_add_menu_dialog.dart';

class MealPlanHomeScreen extends ConsumerStatefulWidget {
  const MealPlanHomeScreen({super.key});

  @override
  ConsumerState<MealPlanHomeScreen> createState() => _MealPlanHomeScreenState();
}

class _MealPlanHomeScreenState extends ConsumerState<MealPlanHomeScreen> {
  int _selectedCookbookIndex = 0;
  String selectedTab = 'Menu';
  final TextEditingController recipeNameController = TextEditingController();
  final TextEditingController recipeDescController = TextEditingController();
  final TextEditingController searchController = TextEditingController();
  final TextEditingController topSearchController = TextEditingController();

  final Map<String, List<String>> _filterOptions = {
    "Lunch": ["Breakfast", "Lunch", "Dinner"],
  };
  final Map<String, String> _selectedFilters = {
    "Lunch": "Lunch",
  };

  @override
  Widget build(BuildContext context) {
    final mealPlans = ref.watch(mealPlansProvider);
    final selectedIndex = ref.watch(selectedMealPlanProvider);
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      appBar: CustomAppBar(title: "Meal Plan" ,
      actions: [
        // Nutrition Analysis Button
        TextButton.icon(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => NutritionFactsDialog(),
            );
           // showDialog(context: context, builder: (context) => NutritionDialog());
          },
          icon: SvgPicture.asset(
            AssetsManager.analysis,
            height: 35.h,
            width: 35.w,
          ),
          label: CustomDescText(desc: "Nutrition Analysis" , size: 22.sp,  textColor: Colors.black,) ,
        ),

       SizedBox(width: 10.w),
        // Add Button
        TextButton.icon(
          onPressed: () {},
          icon: SvgPicture.asset(
            AssetsManager.add,
            height: 35.h,
            width: 35.w,
          ),
          label: CustomDescText(desc: "Add",  size: 22.sp,  textColor: Colors.black)
        ),

        SizedBox(width: 10.w),

        // Search Bar
        CustomSearchBar(
          controller: topSearchController,
          width: 400.w,
        ),
      ],),
      body: Row(
        children: [
          // Sidebar
          Expanded(
            flex: 2,
            child: Container(
           // padding: EdgeInsets.all(12.w),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 20.h),
                CustomTabView(
                    width: 210.w,
                    height: 70.h,
                    selectedTabColor: Colors.white,
                    tabs:  ['Menu', 'Meal Plans'],
                    selected: selectedTab,
                    bgTabColor: AppColors.primaryColor,
                    onChanged: (tab) => setState(() => selectedTab = tab),
                    fontSize: 24.sp),
                SizedBox(height: 16.h),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CustomSearchBar(
                    controller: searchController,
                  ),
                ),
                selectedTab == "Menu" ? Expanded(
                  child: ListView.separated(
                    padding: const EdgeInsets.all(12),
                    itemCount: mealPlans.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      if(index == 0){
                        return CustomAddItem(
                          isSelected: _selectedCookbookIndex == index,
                         title: "Add New Menu",
                          onTap: ()=>  showAddMenuDialog(context, ref)
                        );
                      }
                      return GestureDetector(
                        onTap: () => ref.read(selectedMealPlanProvider.notifier).state = index,
                        child: MenuTile(
                          isSelected: _selectedCookbookIndex == index, data: mealPlans[index],
                        ),
                      );
                    },
                  ),
                ) :Expanded(
                  child: ListView.separated(
                    padding: const EdgeInsets.all(10),
                    itemCount: mealPlans.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      if(index == 0 ){
                        return CustomAddItem(
                          isSelected: _selectedCookbookIndex == index,

                          title: "Add New Meal Plan",
                            onTap: ()=>  showAddMenuDialog(context, ref)

                        );
                      }
                      return GestureDetector(
                        onTap: () => ref.read(selectedMealPlanProvider.notifier).state = index,
                        child: MealPlanTile(
                          isSelected: _selectedCookbookIndex == index, data: mealPlans[index],
                        ),
                      );
                    },
                  ),
                ),
                SizedBox(height: 16.h),
                Divider(),
                Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ColorScheme.light(
                      primary: AppColors.primaryColor, // Header background & selected date
                      onPrimary: Colors.white, // Selected date text color
                      onSurface: Colors.black, // Default text color
                    ),
                    textButtonTheme: TextButtonThemeData(
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.primaryColor, // Button (e.g., month/year selector)
                      ),
                    ),
                    dialogTheme: DialogThemeData(
                      backgroundColor: Colors.grey,
                    ),
                  ),
                  child:
                  CalendarDatePicker(
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2020),
                    lastDate: DateTime(2030),
                    onDateChanged: (date) {
                      print("Selected: $date");
                    },
                  ),
                ),
              ],
            ),
          ),
          ),
          selectedTab == "Menu" ? MenuScreen() : MealPlanScreen()
          // Main Content

        ],
      ),
    );
  }

  Widget _buildDropdownFilter(String filterKey) {
    final selected = _selectedFilters[filterKey]!;
    return PopupMenuButton<String>(
      color: Colors.white,
      onSelected: (value) {
        //setState(() {
        _selectedFilters[filterKey] = value;
        //});
      },
      itemBuilder: (context) {
        return _filterOptions[filterKey]!.map((option) {
          return PopupMenuItem<String>(
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              decoration: option == selected
                  ? BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(6),
              )
                  : null,
              child: Text(
                option,
                style: TextStyle(
                  color: option == selected ? Colors.white : Colors.black,
                  fontWeight: option == selected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          );
        }).toList();
      },
      child: Container(
        height: 80.h,
        width: 300.w,
        padding:  EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
        margin: EdgeInsets.only( right: 20.w),
        decoration: BoxDecoration(
          border: Border.all(
              color: Colors.black38.withValues(alpha: .2)
          ),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomDescText(desc: filterKey),
            SizedBox(width: 5.w,),
            Container(
                margin:  EdgeInsets.symmetric(horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(
                      color: Colors.black38.withValues(alpha: .2)
                  ),
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Icon(Icons.keyboard_arrow_down, size: 20.w , color: AppColors.secondaryColor,)),
          ],
        ),
      ),
    );
  }

  void showAddMenuDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      useRootNavigator: true,  //<--- add this
      barrierDismissible: false,
      builder: (_) => AddMenuItemDialog(),
    );
  }
}
