import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/presentation/mealPlan/widgets/add_meal_item.dart';
import 'package:mastercookai/presentation/mealPlan/widgets/add_meal_type_dialog.dart';
import 'package:mastercookai/presentation/mealPlan/widgets/ingredient_amount_dialog.dart';
import 'package:mastercookai/presentation/mealPlan/widgets/meal_item.dart';
import 'package:mastercookai/presentation/mealPlan/widgets/sho_add_menu_dialog.dart';
import '../../../../../app/imports/core_imports.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../core/providers/mealplan/meal_plan_provider.dart';
import '../cookbook/widgets/custom_desc_text.dart';
import '../cookbook/widgets/custom_title_text.dart';
import '../recipe/subview/custom_ingredent_textfield.dart';


class MealPlanScreen extends ConsumerStatefulWidget {
  const MealPlanScreen({super.key});

  @override
  ConsumerState<MealPlanScreen> createState() => _MealPlanScreenState();
}

class _MealPlanScreenState extends ConsumerState<MealPlanScreen> {
  int _selectedCookbookIndex = 0;
  String selectedTab = 'Menu';
  final TextEditingController recipeNameController = TextEditingController();
  final TextEditingController recipePortionController = TextEditingController();

  final Map<String, List<String>> _filterOptions = {
    "Lunch": ["Breakfast", "Lunch", "Dinner"],
  };
  final Map<String, String> _selectedFilters = {
    "Lunch": "Lunch",
  };

  final Map<String, List<String>> meals = {
    'Breakfast': ['Milk', 'Bread'],
    'Lunch': ['Rice', 'Chicken'],
    'Dinner': ['Salad', 'Soup'],
  };

  final List<String> allRecipes = [
    'Pasta',
    'Pizza',
    'Chines Noodles',
    'Corn Salty',
    'Shushi',
  ];

  List<String> selectedRecipes = ['Sushi', 'Pasta'];
  String searchText = '';

  @override
  Widget build(BuildContext context) {
    final mealPlans = ref.watch(mealPlansProvider);
    final selectedIndex = ref.watch(selectedMealPlanProvider);
    final screenSize = MediaQuery.of(context).size;

    String? expandedCategory;

    Map<String, List<MealItem>> meals = {
      'Breakfast': [
        MealItem(name: 'Tater tots', portion: 2),
        MealItem(name: 'San Francisco sourdough bread', portion: 2),
        MealItem(name: 'Milk', portion: 2, selected: true),
        MealItem(name: 'Pot roast', portion: 1),
      ],
      'Lunch': [
        MealItem(name: 'Tater tots', portion: 2),
        MealItem(name: 'San Francisco sourdough bread', portion: 2),
      ],
      'Dinner': [
        MealItem(name: 'Tater tots', portion: 2),
        MealItem(name: 'San Francisco sourdough bread', portion: 2),
      ],
    };

    final filteredRecipes = allRecipes
        .where((item) =>
            item.toLowerCase().contains(searchText.toLowerCase()) &&
            !selectedRecipes.contains(item))
        .toList();

    return Expanded(
      flex: 6,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(AssetsManager.background_img, fit: BoxFit.cover),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.03,
              vertical: screenSize.width * 0.03,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          CustomDescText(
                              desc: "Title:",
                              size: 22.sp,
                              textColor: AppColors.primaryLightTextColor),
                          SizedBox(
                            width: 10.w,
                          ),
                          Expanded(
                            child: IngredientTextField(
                              maxLines: 2,
                              controller: recipeNameController,
                              hintText: 'Title',
                            ),
                          ),
                          SizedBox(
                            width: 20.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              CustomDescText(
                                  desc: "Menu Portions",
                                  size: 22.sp,
                                  textColor: AppColors.primaryLightTextColor),
                              SizedBox(
                                width: 10.w,
                              ),
                              Container(
                                height: 80.h,
                                width: 160.w,
                                // padding: EdgeInsets.all(16),
                                padding:
                                    EdgeInsets.only(left: 10.w, right: 10.w),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Center(
                                  child: IngredientTextField(
                                    controller: recipePortionController,
                                    hintText: '2',
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                          CustomButton(
                              fontSize: 20.sp,
                              width: 200.w,
                              onPressed: () {
                                showIngredientAmountDialog(context);
                              },
                              text: "Set Amount"),
                          SizedBox(
                            width: 10.w,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Title and Inputs

                SizedBox(height: 24.h),

                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        _buildHeader("Items", "Portion"),
                        _buildDayHeader("Day 1 of 7"),
                        Expanded(
                          child: ListView.separated(
                            padding: EdgeInsets.zero,
                            separatorBuilder: (context, index) => Divider(
                              height: 1,
                              color: Colors.grey.shade200,
                            ),
                            itemCount: meals.length,
                            itemBuilder: (context, index) {
                              final category = meals.keys.elementAt(index);
                              final isExpanded = expandedCategory == category;

                              return ExpansionTile(
                                shape: Border(),
                                collapsedShape: Border(),
                                tilePadding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 4),
                                leading: SvgPicture.asset(
                                  isExpanded
                                      ? AssetsManager.drop_up
                                      : AssetsManager.drop_down,
                                  height: 20,
                                  width: 20,
                                ),
                                title: Text(
                                  category,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.black,
                                  ),
                                ),
                                trailing: TextButton.icon(
                                  onPressed: () {
                                    // setState(() {
                                    //   item.selected = true;
                                    // });
                                    showAddMealItemDialog(context, ref);
                                    // _addNewItem(category, filteredRecipes );
                                  },
                                  icon: SvgPicture.asset(
                                    AssetsManager.ic_addnew,
                                    height: 30.h,
                                    width: 30.w,
                                  ),
                                  label: Text(
                                    "Add Item",
                                    style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.blue),
                                  ),
                                ),
                                initiallyExpanded: isExpanded,
                                onExpansionChanged: (expanded) {
                                  setState(() {
                                    expandedCategory =
                                        expanded ? category : null;
                                  });
                                },
                                children: [
                                  ...meals[category]!
                                      .asMap()
                                      .entries
                                      .map((entry) {
                                    final item = entry.value;
                                    final bgColor = entry.key.isEven
                                        ? Colors.white
                                        : Colors.blue.shade50;

                                    return Container(
                                      decoration: BoxDecoration(
                                        color: bgColor,
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.05),
                                            blurRadius: 8,
                                            offset: const Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12, horizontal: 16),
                                      margin: const EdgeInsets.only(bottom: 4),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Row(
                                              children: [
                                              Image.asset(
                                              AssetsManager.menu_img, // Replace with your image path
                                              height: 18,
                                              width: 18,
                                            ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  item.name,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w500,
                                                    color: Colors.black,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Row(
                                            children: [
                                              if (item.selected)
                                                TextButton.icon(
                                                  onPressed: () {
                                                    setState(() {
                                                      item.selected = false;
                                                    });
                                                  },
                                                  icon: SvgPicture.asset(
                                                    AssetsManager.ic_remove,
                                                    height: 30.h,
                                                    width: 30.w,
                                                  ),
                                                  label: Text("Remove",
                                                      style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color: Colors.black)),
                                                ),
                                              const SizedBox(width: 8),
                                              Text(
                                                "${item.portion}",
                                                style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w500,
                                                    color: Colors.black),
                                              ),
                                              const SizedBox(width: 15),
                                            ],
                                          )
                                        ],
                                      ),
                                    );
                                  }).toList(),
                                  // ListTile(
                                  //   leading: Icon(Icons.add_circle_outline_outlined,
                                  //       color: Colors.black),
                                  //   title: Text("Add New",
                                  //       style: TextStyle(fontSize: 16, color: Colors.black)),
                                  //   onTap: () {
                                  //     // your add logic
                                  //   },
                                  // )
                                ],
                              );
                            },
                          ),
                        ),
                        // _buildCollapsedMealsSection(label: "Day 2 of 7"),
                        // _buildCollapsedMealsSection(label: "Day 3 of 7"),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(String left, String right) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.primaryLightTextColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            left,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            right,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayHeader(String dayLabel) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      color: AppColors.selectionMealPortion,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            dayLabel,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          TextButton.icon(
            onPressed: () {
              // Add logic here
              showMealTypeDialog(context, ref);

            },
            icon: SvgPicture.asset(
              AssetsManager.ic_add_day,
              height: 40.h,
              width: 40.w,
            ),
            label: const Text(
              "",
              style: TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCollapsedMealsSection({required String label}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDayHeader(label),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: ["Breakfast", "Lunch", "Dinner"].map((meal) {
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ListTile(
                  leading: SvgPicture.asset(
                    AssetsManager.ic_expand,
                    height: 30.h,
                    width: 30.w,
                  ),
                  title: Text(
                    meal,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  trailing: TextButton.icon(
                    onPressed: () {
                      // Add logic here
                      showMealTypeDialog(context, ref);
                    },
                    icon: Icon(Icons.add_circle_outline_outlined),
                    label: Text("Add Item", style: TextStyle(fontSize: 14)),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _addNewItem(String category, List<String> filteredRecipes) {
    TextEditingController controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Container(
              color: Colors.white,
              width: 1000.w,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomeTitleText(title: "Add $category Meal"),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: _closeButton(context),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  // Search Field
                  TextField(
                    controller: controller,
                    style: context.theme.textTheme.labelMedium!
                        .copyWith(fontWeight: FontWeight.w400),
                    onChanged: (value) => setState(() => searchText = value),
                    decoration: InputDecoration(
                      filled: context.theme.inputDecorationTheme.filled,
                      fillColor: context.theme.inputDecorationTheme.fillColor,
                      hintText: "Recipe",
                      hintStyle: context.theme.inputDecorationTheme.hintStyle,
                      errorStyle: context.theme.inputDecorationTheme.errorStyle,
                      //border: context.theme.inputDecorationTheme.border,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 14.h),

                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      suffixIcon: controller.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear, size: 18),
                              onPressed: () {
                                controller.clear();
                              },
                            )
                          : null,
                    ),
                  ),
                  SizedBox(height: 10),
                  // Selected Chips
                  Wrap(
                    spacing: 8.0,
                    children: selectedRecipes
                        .map((recipe) => Chip(
                              backgroundColor: Colors.white,
                              label: CustomDescText(desc: recipe),
                              deleteIcon: Icon(Icons.close),
                              onDeleted: () => setState(() {
                                selectedRecipes.remove(recipe);
                              }),
                            ))
                        .toList(),
                  ),
                  SizedBox(height: 10),
                  // List of Filtered Items
                  Container(
                    height: 150.h,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ListView.builder(
                      itemCount: filteredRecipes.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          title: CustomDescText(desc: filteredRecipes[index]),
                          onTap: () {
                            setState(() {
                              selectedRecipes.add(filteredRecipes[index]);
                              searchText = '';
                            });
                          },
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 20.h),
                  CustomButton(
                      text: "Create now",
                      width: 230.w,
                      fontSize: 20.sp,
                      onPressed: () {
                        if (controller.text.isNotEmpty) {
                          setState(() {
                            meals[category]!.add(controller.text);
                          });
                          Navigator.pop(context);
                        }
                      })
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _closeButton(BuildContext context) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  Widget _buildDropdownFilter(String filterKey) {
    final selected = _selectedFilters[filterKey]!;
    return PopupMenuButton<String>(
      color: Colors.white,
      onSelected: (value) {
        //setState(() {
        _selectedFilters[filterKey] = value;
        //});
      },
      itemBuilder: (context) {
        return _filterOptions[filterKey]!.map((option) {
          return PopupMenuItem<String>(
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              decoration: option == selected
                  ? BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    )
                  : null,
              child: Text(
                option,
                style: TextStyle(
                  color: option == selected ? Colors.white : Colors.black,
                  fontWeight:
                      option == selected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          );
        }).toList();
      },
      child: Container(
        height: 80.h,
        width: 300.w,
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
        margin: EdgeInsets.only(right: 20.w),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black38.withOpacity(.2)),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomDescText(desc: filterKey),
            SizedBox(
              width: 5.w,
            ),
            Container(
                margin: EdgeInsets.symmetric(horizontal: 5.w),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.black38.withOpacity(.2)),
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Icon(
                  Icons.keyboard_arrow_down,
                  size: 20.w,
                  color: AppColors.secondaryColor,
                )),
          ],
        ),
      ),
    );
  }
}
void showMealTypeDialog(BuildContext context, WidgetRef ref) {
  showDialog(
    context: context,
    useRootNavigator: true,  //<--- add this
    barrierDismissible: false,
    builder: (_) => AddMealTypeDialog(),
  );
}

void showAddMealItemDialog(BuildContext context, WidgetRef ref) {
  showDialog(
    context: context,
    useRootNavigator: true,  //<--- add this
    barrierDismissible: false,
    builder: (_) => AddMealItemsDialog(),
  );
}

