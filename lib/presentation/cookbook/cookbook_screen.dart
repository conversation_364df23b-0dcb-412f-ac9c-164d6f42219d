import 'dart:async';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/core/widgets/no_data_widget.dart';
import 'package:mastercookai/presentation/cookbook/widgets/CookbookDialog.dart';
import 'package:mastercookai/presentation/cookbook/widgets/cookbook_card.dart';
import 'package:mastercookai/presentation/cookbook/widgets/create_cookbook_card.dart';

import 'package:mastercookai/presentation/cookbook/widgets/update_cookbook_dialog.dart';

import '../../core/network/app_status.dart';
import '../../core/providers/cookboor_notifier.dart';
import '../../core/utils/screen_sizer.dart';
import '../../core/widgets/pagination_indicator.dart';
import '../../core/widgets/uploading_dialog.dart';
import '../shimer/Recipe_shimmer.dart';
import '../shimer/cookbook_shimmer.dart';

class CookbookScreen extends ConsumerStatefulWidget {
  const CookbookScreen({super.key});

  @override
  ConsumerState<CookbookScreen> createState() => _CookbookScreenState();
}

class _CookbookScreenState extends ConsumerState<CookbookScreen> {
  final PageController _pageController = PageController();
  final TextEditingController searchController = TextEditingController();
  int _currentPage = 0;
  bool _showLoader = false;

  // Search functionality variables
  Timer? _debounceTimer;
  String _currentSearchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startLoadingDelayed();
      Future.delayed(const Duration(milliseconds: 100), () {
        ref
            .read(cookbookNotifierProvider.notifier)
            .fetchCookbooks(context: context);
      });
    });
  }

  void _startLoadingDelayed() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted &&
          ref.read(cookbookNotifierProvider).status == AppStatus.loading) {
        setState(() {
          _showLoader = true;
        });
      }
    });
  }

  void _onSearchChanged(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set new timer for debouncing
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentSearchQuery) {
        setState(() {
          _currentSearchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    // Search in cookbooks
    ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
      context: context,
      search: query.isEmpty ? null : query,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final crossAxisCount = isHighRes ? 5 : 5;

    return WillPopScope(
      onWillPop: () async {
        if (_currentPage > 0) {
          _pageController.previousPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          return false; // Prevent default pop
        }
        return false; // Prevent exiting the screen
        // Or return true to allow exiting: return true;
      },
      child: Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            // Background Image
            Image.asset(
              AssetsManager.background_img,
              fit: BoxFit.cover,
            ),

            // Main Content


            cookbookState.status == AppStatus.loading
                ? CookbookShimmer(
                    crossAxisCount: crossAxisCount,
                    itemsPerPage: 10,
                  )
                : Column(
                    children: [
                      SizedBox(height: 60.h),
                      // SearchBar (shown if data exists or there's an active search)
                      if ((cookbookState.data?.isNotEmpty ?? false) || _currentSearchQuery.isNotEmpty)
                        CustomSearchBar(
                          controller: searchController,
                          width: 500.w,
                          onChanged: _onSearchChanged,
                        ),

                      if ((cookbookState.data?.isNotEmpty ?? false))
                        SizedBox(height: 80.h),

                      // Main Grid View
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.symmetric(
                              horizontal: screenSize.width * 0.05),
                          child: _buildMainContent(
                              cookbookState, screenSize, isHighRes),
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent(
      AppState<List<Cookbook>> state, Size screenSize, bool isHighRes) {
    // Loading State
    if (state.status == AppStatus.loading && (state.data?.isEmpty ?? true)) {
      return const SizedBox
          .shrink(); // Don't show empty state during initial loading
    }

    // Error State
    if (state.status == AppStatus.error && (state.data?.isEmpty ?? true)) {
      return Center(
          child: Text('Error: ${state.errorMessage ?? "An error occurred"}'));
    }

    // Empty State (only after API call completes)
    if (state.status == AppStatus.empty ||
        (state.status == AppStatus.success && (state.data?.isEmpty ?? true))) {

      // Check if this is a search result with no data
      if (_currentSearchQuery.isNotEmpty) {
        // Show animated no data for search results
        return const NoDataWidget(
          title: "No Cookbooks Found",
          subtitle: "Try adjusting your search terms or create a new cookbook",
          width: 200,
          height: 200,
        );
      } else {
        // Show grid with create card at index 0 when list is empty (no search)
        final width = MediaQuery.of(context).size.width;
        final crossAxisCount = ScreenSizer().calculateCrossAxisCount(width);

        return LayoutBuilder(
          builder: (context, constraints) {
            final size = MediaQuery.of(context).size;
            final aspectRatio = ScreenSizer()
                .calculateChildAspectRatio(size.width, size.height);
            return GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: screenSize.height * 0.01,
                crossAxisSpacing: screenSize.width * 0.01,
                childAspectRatio: aspectRatio,
              ),
              itemCount: 1, // Only show the create card
              itemBuilder: (context, index) {
                return ImportCreateCard(
                  title: "Create New Cookbook",
                  importText: "Import Cookbook",
                  isRecipe: false,
                  onImport: () {
                    showTopRightDialog(context);
                  },
                  onCreate: () => showCookbookDialog(context, ref),
                );
              },
            );
          },
        );
      }
    }

    // Data Loaded - Build Grid View
    final width = MediaQuery.of(context).size.width;
    final crossAxisCount = ScreenSizer().calculateCrossAxisCount(width);
    final itemsPerPage = crossAxisCount * 2;
    final totalItems = (state.data?.length ?? 0) + 1; // +1 for create card
    final totalPages = (totalItems / itemsPerPage).ceil();

    return NotificationListener<ScrollNotification>(
      onNotification: (scrollNotification) {
        if (scrollNotification is ScrollEndNotification) {
          final metrics = scrollNotification.metrics;
          if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
              state.hasMore &&
              state.status != AppStatus.loadingMore) {
            ref
                .read(cookbookNotifierProvider.notifier)
                .fetchCookbooks(
                  context: context,
                  loadMore: true,
                  search: _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                );
          }
        }
        return false;
      },
      child: Stack(
        children: [
          Listener(
            onPointerSignal: (pointerSignal) {
              if (pointerSignal is PointerScrollEvent) {
                if (pointerSignal.scrollDelta.dx != 0 || pointerSignal.scrollDelta.dy != 0) {
                  final delta = pointerSignal.scrollDelta.dx != 0
                      ? pointerSignal.scrollDelta.dx
                      : pointerSignal.scrollDelta.dy;
                  if (delta > 0 && _currentPage < (state.hasMore ? totalPages : totalPages - 1)) {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  } else if (delta < 0 && _currentPage > 0) {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                }
              }
            },
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (int page) {
                setState(() {
                  _currentPage = page;
                });
                if (page == totalPages - 1 && state.hasMore) {
                  ref
                      .read(cookbookNotifierProvider.notifier)
                      .fetchCookbooks(
                        context: context,
                        loadMore: true,
                        search: _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                      );
                }
              },
              itemCount: state.hasMore ? totalPages + 1 : totalPages,
              itemBuilder: (context, pageIndex) {
                if (pageIndex >= totalPages && state.hasMore) {
                  return Center(
                    child: LoadingAnimationWidget.fallingDot(
                      color: Colors.white,
                      size: 50.0,
                    ),
                  );
                }

                final startIndex = pageIndex * itemsPerPage;
                var endIndex = startIndex + itemsPerPage;
                if (endIndex > totalItems) endIndex = totalItems;

                return LayoutBuilder(
                  builder: (context, constraints) {
                    final size = MediaQuery.of(context).size;
                    final aspectRatio = ScreenSizer()
                        .calculateChildAspectRatio(size.width, size.height);
                    return GridView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        mainAxisSpacing: screenSize.height * 0.01,
                        crossAxisSpacing: screenSize.width * 0.01,
                        childAspectRatio: aspectRatio,
                      ),
                      itemCount: endIndex - startIndex,
                      itemBuilder: (context, index) {
                        final itemIndex = startIndex + index;

                        if (index == itemsPerPage - 1 &&
                            state.status == AppStatus.loadingMore) {
                          return Center(
                            child: LoadingAnimationWidget.fallingDot(
                              color: Colors.white,
                              size: 50.0,
                            ),
                          );
                        }

                        if (itemIndex == 0) {
                          return ImportCreateCard(
                            title: "Create New Cookbook",
                            importText: "Import Cookbook",
                            isRecipe: false,
                            onImport: () {
                              showTopRightDialog(context);
                            },
                            onCreate: () => showCookbookDialog(context, ref),
                          );
                        }

                        final cookbookIndex = itemIndex - 1;
                        if (cookbookIndex < (state.data?.length ?? 0)) {
                          return CookbookCard(
                            cookbook: state.data![cookbookIndex],
                            isHighRes: isHighRes,
                            onTap: () {
                              ref
                                  .read(cookbookNotifierProvider.notifier)
                                  .resetToIdle();
                              showUpdateCookbookDialog(
                                context,
                                ref,
                                cookbookId:
                                    state.data![cookbookIndex].id.toString(),
                                cookbookName: state.data![cookbookIndex].name,
                              );
                            },
                          );
                        }

                        return const SizedBox.shrink();
                      },
                    );
                  },
                );
              },
            ),
          ),

          // Pagination Dots
          if (totalPages > 1)
            PaginationIndicator(
              totalPages: totalPages,
              currentPage: _currentPage,
              hasMore: state.hasMore,
              screenSize: screenSize,
              context: context,
            ),
        ],
      ),
    );
  }


}

void showCookbookDialog(BuildContext context, WidgetRef ref) {
// Reset the cookbookNotifierProvider state
  ref.read(cookbookNotifierProvider.notifier).resetToIdle();
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => CookbookDialog(
      title: "Create new cookbook",
      hintText: "Cookbook name",
      successText: "Cookbook created\nsuccessfully.",
      buttonText: "Create now",
      successButtonText: "Go to Cookbooks",
    ),
  );
}


//Uploading PopUo open At Right
void showTopRightDialog(BuildContext context) {
  final overlay = Overlay.of(context);
  OverlayEntry? overlayEntry; // Declare nullable

  overlayEntry = OverlayEntry(
    builder: (_) => Positioned(
      top: 40.h,
      right: 20.w,
      child: Material(
        color: Colors.transparent,
        child: UploadingDialog(
          title: "Cookbook list is uploading...",
          subtitle: "Fruits CookBook",
          progress: 0.54,
          onCancel: () {
            overlayEntry?.remove(); // Use nullable-safe access
          },
          onClose: () {
            overlayEntry?.remove();
          },
        ),
      ),
    ),
  );

  overlay.insert(overlayEntry);
}
