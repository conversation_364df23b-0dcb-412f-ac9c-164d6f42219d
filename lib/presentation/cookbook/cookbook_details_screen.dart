import 'dart:async';

import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/presentation/cookbook/widgets/create_cookbook_card.dart';
import 'package:mastercookai/presentation/cookbook/widgets/drop_down_filter.dart';
import 'package:mastercookai/presentation/cookbook/widgets/recipe_cards.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../core/data/models/cookbook.dart';
import '../../core/data/models/recipe_response.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/cookboor_notifier.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/utils/screen_sizer.dart';
import '../../core/widgets/pagination_indicator.dart';
import '../shimer/Recipe_shimmer.dart';
import '../shimer/cookbook_list_shimmer.dart';
import 'widgets/cookbook_details_card.dart';

class CookbookDetailScreen extends ConsumerStatefulWidget {
  const CookbookDetailScreen({super.key});

  @override
  ConsumerState<CookbookDetailScreen> createState() =>
      _CookbookDetailsScreenState();
}

class _CookbookDetailsScreenState extends ConsumerState<CookbookDetailScreen> {
  final PageController _pageController = PageController();
  final TextEditingController searchController = TextEditingController();
  TextEditingController topSearchController = TextEditingController();
  int _currentPage = 0;
  int totalPages = 0;
  int _selectedCookbookIndex = 0;
  int cuisine = 0;
  int category = 0;
  int cookbookId = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    topSearchController = TextEditingController();
    topSearchController.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final id = GoRouterState.of(context).uri.queryParameters['id'];
      final name = GoRouterState.of(context).uri.queryParameters['name'];
      await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
            context: context,
            loadMore: false,
          );
      final extras = GoRouterState.of(context).extra as Map<String, dynamic>?;
      final Cookbook? passedCookbook = extras?['cookbook'] as Cookbook?;
      final cookbookState = ref.read(cookbookNotifierProvider);
      cookbookId = int.parse(id!);
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: cookbookId,
            cuisineId: cuisine,
            categoryId: category,
            cookbookName: name,
            reset: true,
            context: context,
          );

      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        if (passedCookbook != null) {
          final index = cookbookState.data!
              .indexWhere((cb) => cb.id == passedCookbook.id);
          if (index != -1) {
            setState(() {
              _selectedCookbookIndex = index;
            });
          }
        }
      }

      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);

      final cuisines = ref.read(cuisinesNotifierProvider).data ?? [];
      _filterOptions['Cuisine'] = cuisines.map((c) => c.name ?? '').toList();

      final categories = ref.read(categoriesNotifierProvider).data ?? [];
      _filterOptions['Category'] = categories.map((c) => c.name ?? '').toList();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    searchController.dispose();
    _scrollController.dispose();
    topSearchController.removeListener(_onSearchChanged);
    topSearchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.hasMore &&
          cookbookState.status != AppStatus.loadingMore) {
        ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              loadMore: true,
              context: context,
            );
      }
    }
  }

  void _onSearchChanged() {
    final text = topSearchController.text.trim();

    if (text.length >= 3) {
      // Debounce to avoid calling too many times
      _debounceSearch(text);
    }
  }

  Timer? _debounce;

  void _debounceSearch(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: cookbookId,
            cuisineId: cuisine,
            categoryId: category,
            search: query,
            reset: true,
            context: context,
          );
    });
  }

  final Map<String, List<String>> _filterOptions = {
    "Cuisine": [],
    "Category": [],
  };

  final Map<String, String> _selectedFilters = {
    "Cuisine": "",
    "Category": "",
  };

  @override
  Widget build(BuildContext context) {
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final recipeState = ref.watch(recipeNotifierProvider);
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;

    final crossAxisCount = isHighRes ? 5 : 4;
    final itemsPerPage = crossAxisCount * 2;

    return Scaffold(
      appBar: CustomAppBar(
        title: "Cookbooks",
        actions: [
          Wrap(
            spacing: 8,
            children: _filterOptions.keys.map((filterName) {
              return DropdownFilter(
                filterName: filterName,
                selectedFilters: _selectedFilters,
                filterOptions: _filterOptions,
                onFilterChanged: (filterName, selectedValue) {
                  setState(() {
                    _selectedFilters[filterName] = selectedValue;
                    print(
                        'Selected $filterName: ${_selectedFilters[filterName]}');

                    switch (filterName) {
                      case 'Cuisine':
                        final cuisineModel = ref
                            .read(cuisinesNotifierProvider)
                            .data
                            ?.firstWhere((c) => c.name == selectedValue);
                        if (cuisineModel != null) {
                          cuisine = cuisineModel.id!;
                          ref
                              .read(recipeMetadataProvider.notifier)
                              .updateCuisineId(cuisineModel.id!);

                          ref
                              .read(recipeNotifierProvider.notifier)
                              .fetchRecipes(
                                  cookbookId: cookbookId,
                                  cuisineId: cuisine,
                                  categoryId: category,
                                  reset: true,
                                  context: context);
                        }
                        break;
                        // case 'Ratings':
                        // // Handle rating filter change
                        break;
                      case 'Category':
                        final categoryModel = ref
                            .read(categoriesNotifierProvider)
                            .data
                            ?.firstWhere((c) => c.name == selectedValue);
                        if (categoryModel != null) {
                          category = categoryModel.id!;
                          ref
                              .read(recipeMetadataProvider.notifier)
                              .updateCategoryId(categoryModel.id);

                          ref
                              .read(recipeNotifierProvider.notifier)
                              .fetchRecipes(
                                  cookbookId: cookbookId,
                                  cuisineId: cuisine,
                                  categoryId: category,
                                  reset: true,
                                  context: context);
                        }
                        break;
                      // case 'Preference':
                      // // Handle rating filter change
                      //   break;
                    }
                  });
                },
              );
            }).toList(),
          ),
          SizedBox(width: 12.w),
          CustomSearchBar(
            width: 440.w,
            height: 60.h,
            controller: topSearchController,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                // Left side: vertical list of cookbooks
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      SizedBox(height: 20.h),
                      CustomSearchBar(controller: searchController),
                      Expanded(
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (scrollNotification) {
                            if (scrollNotification is ScrollEndNotification &&
                                scrollNotification.metrics.pixels ==
                                    scrollNotification
                                        .metrics.maxScrollExtent) {
                              final cookbookState =
                                  ref.read(cookbookNotifierProvider);
                              if (cookbookState.hasMore &&
                                  cookbookState.status !=
                                      AppStatus.loadingMore) {
                                ref
                                    .read(cookbookNotifierProvider.notifier)
                                    .fetchCookbooks(
                                      loadMore: true,
                                      context: context,
                                    );
                              }
                            }
                            return false;
                          },
                          child: Builder(
                            builder: (context) {
                              if (cookbookState.status == AppStatus.loading ||
                                  cookbookState.status ==
                                      AppStatus.loadingMore) {
                                return CookbookListShimmer(
                                  itemCount: 20,
                                );
                              } else if (cookbookState.status ==
                                  AppStatus.error) {
                                return Center(
                                    child: Text(
                                        'Error: ${cookbookState.errorMessage}'));
                              } else if (cookbookState.data == null ||
                                  cookbookState.data!.isEmpty) {
                                return const Center(
                                    child: Text('No cookbooks found'));
                              }

                              return ListView.separated(
                                controller: _scrollController,
                                padding: const EdgeInsets.all(12),
                                itemCount: cookbookState.data!.length +
                                    (cookbookState.hasMore ? 1 : 0),
                                separatorBuilder: (_, __) =>
                                    const SizedBox(height: 12),
                                itemBuilder: (context, index) {
                                  if (index >= cookbookState.data!.length) {
                                    return CookbookListShimmer(
                                      itemCount: 8,
                                    );
                                  }

                                  final cookbook = cookbookState.data![index];
                                  return GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _selectedCookbookIndex = index;
                                        _currentPage = 0;
                                      });
                                      cookbookId = cookbook.id;
                                      ref
                                          .read(recipeNotifierProvider.notifier)
                                          .fetchRecipes(
                                              cookbookId: cookbookId,
                                              cuisineId: cuisine,
                                              categoryId: category,
                                              cookbookName: cookbook.name,
                                              reset: true,
                                              context: context);
                                    },
                                    child: CookbookDetailCard(
                                      cookbook: cookbook,
                                      isSelected:
                                          _selectedCookbookIndex == index,
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Right side: grid of recipe cards + import card
                Expanded(
                  flex: 6,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      Image.asset(
                        AssetsManager.background_img,
                        fit: BoxFit.cover,
                      ),
                      Builder(builder: (context) {
                        List<Recipe> recipeList = recipeState.data ?? [];
                        totalPages = recipeState.totalItems > 0
                            ? (recipeState.totalItems / itemsPerPage).ceil()
                            : 1;

                        if ((recipeState.status == AppStatus.loading ||
                            recipeState.status == AppStatus.loadingMore)) {
                          return RecipeShimmer(
                            crossAxisCount: crossAxisCount,
                            itemsPerPage: 8,
                          );
                        } else if (recipeState.status == AppStatus.error) {
                          return Center(
                              child:
                                  Text('Error: ${recipeState.errorMessage}'));
                        } else if (recipeState.status == AppStatus.empty ||
                            recipeList.isEmpty) {
                          return Container(
                            margin: EdgeInsets.only(
                              right: screenSize.height * 0.9,
                              bottom: screenSize.height * 0.5,
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: screenSize.width * 0.03,
                              vertical: screenSize.width * 0.03,
                            ),
                            child: ImportCreateCard(
                              title: "Add New Recipe",
                              importText: "Import Recipe",
                              isRecipe: true,
                              onImport: () => [],
                              onCreate: () => context.go(
                                '/cookbook/cookbookDetail/addRecipe',
                                extra: cookbookState.data!.isNotEmpty
                                    ? cookbookState
                                        .data![_selectedCookbookIndex]
                                    : null,
                              ),
                            ),
                          );
                        }

                        return Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenSize.width * 0.03,
                            vertical: screenSize.width * 0.03,
                          ),
                          child: PageView.builder(
                            controller: _pageController,
                            scrollDirection:  Axis.vertical,
                            onPageChanged: (int page) {
                              setState(() {
                                _currentPage = page;
                              });
                              if ((page + 1) >= totalPages &&
                                  recipeState.hasMore) {
                                cookbookId = cookbookState
                                    .data![_selectedCookbookIndex].id;
                                ref
                                    .read(recipeNotifierProvider.notifier)
                                    .fetchRecipes(
                                        cookbookId: cookbookState
                                            .data![_selectedCookbookIndex].id,
                                        cuisineId: cuisine,
                                        categoryId: category,
                                        cookbookName: cookbookState
                                            .data![_selectedCookbookIndex].name,
                                        reset: false,
                                        context: context);
                              }
                            },
                            itemCount: totalPages,
                            itemBuilder: (context, pageIndex) {
                              final startIndex = pageIndex * itemsPerPage;
                              final endIndex = (pageIndex + 1) * itemsPerPage;

                              final pageItems = recipeList.sublist(
                                startIndex,
                                endIndex < recipeList.length
                                    ? endIndex
                                    : recipeList.length,
                              );

                              return LayoutBuilder(builder: (context, con) {
                                final size = MediaQuery.of(context).size;
                                final aspectRatio = ScreenSizer()
                                    .calculateChildDetailScrenAspectRatio(
                                        size.width, size.height);
                                return GridView.builder(
                                  //  physics: const NeverScrollableScrollPhysics(),
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 4,
                                    // crossAxisCount: ScreenSizer()
                                    //     .calculateCrossAxisCountScreenSize(
                                    //   context: context,
                                    //   screenSize: size,
                                    // ),
                                    mainAxisSpacing: screenSize.height * 0.004,
                                    crossAxisSpacing: screenSize.width * 0.004,
                                    childAspectRatio: aspectRatio,
                                  ),
                                  itemCount:recipeList.length+ (pageIndex == 0 ? 1 : 0),
                                 // pageItems.length + (pageIndex == 0 ? 1 : 0),
                                  itemBuilder: (context, index) {
                                    final selectedCookbook =
                                        cookbookState.data!.isNotEmpty
                                            ? cookbookState
                                                .data![_selectedCookbookIndex]
                                            : null;

                                    if (pageIndex == 0 && index == 0) {
                                      return ImportCreateCard(
                                        title: "Add New Recipe",
                                        importText: "Import Recipe",
                                        isRecipe: true,
                                        onImport: () => [],
                                        onCreate: () => context.go(
                                          '/cookbook/cookbookDetail/addRecipe',
                                          extra: selectedCookbook,
                                        ),
                                      );
                                    }

                                    final adjustedIndex =
                                        (pageIndex == 0) ? index - 1 : index;
                                    final adjustedGlobalIndex =
                                        startIndex + adjustedIndex;

                                    if (adjustedGlobalIndex >=
                                        recipeList.length) {
                                      return const SizedBox.shrink();
                                    }

                                    final recipe =
                                        recipeList[adjustedGlobalIndex];
                                    return RecipeCard(
                                      recipe: recipe,
                                      cookbookId: selectedCookbook?.id,
                                      onPressed: () {
                                        context.go(
                                          '/cookbook/cookbookDetail/recipeDetail/${recipe.id}',
                                          extra: {
                                            'id': recipe.id,
                                            'recipeList': recipeList,
                                            'recipeName': recipe.name,
                                            'cookbookId': selectedCookbook?.id,
                                          },
                                        );
                                      },
                                    );
                                  },
                                );
                              });
                            },
                          ),
                        );
                      }),

                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
