import 'package:flutter/gestures.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/presentation/cookbook/widgets/create_cookbook_card.dart';
import 'package:mastercookai/presentation/cookbook/widgets/drop_down_filter.dart';
import 'package:mastercookai/presentation/cookbook/widgets/recipe_cards.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar.dart';
import '../../core/data/models/cookbook.dart';
import '../../core/data/models/recipe_response.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/cookboor_notifier.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/utils/screen_sizer.dart';
import '../../core/widgets/pagination_indicator.dart';
import '../shimer/Recipe_shimmer.dart';
import '../shimer/cookbook_list_shimmer.dart';
import 'widgets/cookbook_details_card.dart';

class CookbookDetailScreen extends ConsumerStatefulWidget {
  const CookbookDetailScreen({super.key});

  @override
  ConsumerState<CookbookDetailScreen> createState() =>
      _CookbookDetailsScreenState();
}

class _CookbookDetailsScreenState extends ConsumerState<CookbookDetailScreen> {
  final PageController _pageController = PageController();
  final TextEditingController searchController = TextEditingController();
  final TextEditingController topSearchController = TextEditingController();
  int _currentPage = 0;
  int totalPages = 0;
  int _selectedCookbookIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final id = GoRouterState.of(context).uri.queryParameters['id'];
      final name = GoRouterState.of(context).uri.queryParameters['name'];
      await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
        context: context,
        loadMore: false,
      );
      final extras = GoRouterState.of(context).extra as Map<String, dynamic>?;
      final Cookbook? passedCookbook = extras?['cookbook'] as Cookbook?;
      final cookbookState = ref.read(cookbookNotifierProvider);

      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
        cookbookId: int.parse(id!),
        cookbookName: name,
        reset: true,
        context: context,
      );

      if (cookbookState.data != null && cookbookState.data!.isNotEmpty) {
        if (passedCookbook != null) {
          final index = cookbookState.data!
              .indexWhere((cb) => cb.id == passedCookbook.id);
          if (index != -1) {
            setState(() {
              _selectedCookbookIndex = index;
            });
          }
        }
      }

      ref.read(categoriesNotifierProvider.notifier).fetchCategories(context: context);
      ref.read(cuisinesNotifierProvider.notifier).fetchCuisines(context: context);

    });


  }

  @override
  void dispose() {
    _pageController.dispose();
    searchController.dispose();
    topSearchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      final cookbookState = ref.read(cookbookNotifierProvider);
      if (cookbookState.hasMore &&
          cookbookState.status != AppStatus.loadingMore) {
        ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
          loadMore: true,
          context: context,
        );
      }
    }
  }

  final Map<String, String> _selectedFilters = {
    "Cuisine": "Item One",
    "Ratings": "Item One",
    "Category": "Item One",
    "Preference": "Healthy",
  };

  final Map<String, List<String>> _filterOptions = {
    "Cuisine": [
      "Item One",
      "Item Two",
      "Item Three",
      "Item Four",
      "Item Five",
      "Item Six"
    ],
    "Ratings": [
      "Item One",
      "Item Two",
      "Item Three",
      "Item Four",
      "Item Five",
      "Item Six"
    ],
    "Category": [
      "Item One",
      "Item Two",
      "Item Three",
      "Item Four",
      "Item Five",
      "Item Six"
    ],
    "Preference": [
      "Healthy",
      "Veg",
      "Non-Veg",
      "High protein",
      "High calorie",
      "High fats"
    ],
  };

  @override
  Widget build(BuildContext context) {
    final cookbookState = ref.watch(cookbookNotifierProvider);
    final recipeState = ref.watch(recipeNotifierProvider);
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;

    final crossAxisCount = isHighRes ? 5 : 4;
    final itemsPerPage = crossAxisCount * 2;

    return WillPopScope(
      onWillPop: () async {
        // Custom logic when back is pressed
        print("Back button pressed on CookbookDetailScreen");

        // Example: Reset some state or perform cleanup
      //  ref.read(recipeNotifierProvider.notifier).reset();
        return true; // Return true to allow the back navigation
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: "Cookbooks",
          actions: [
            Wrap(
              spacing: 8,
              children: _filterOptions.keys.map((filterName) {
                return DropdownFilter(
                  filterName: filterName,
                  selectedFilters: _selectedFilters,
                  filterOptions: _filterOptions,
                );
              }).toList(),
            ),
            SizedBox(width: 12.w),
            CustomSearchBar(
              width: 440.w,
              height: 60.h,
              controller: topSearchController,
            ),
          ],
        ),
        body: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  // Left side: vertical list of cookbooks
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        SizedBox(height: 20.h),
                        CustomSearchBar(controller: searchController),
                        Expanded(
                          child: NotificationListener<ScrollNotification>(
                            onNotification: (scrollNotification) {
                              if (scrollNotification is ScrollEndNotification &&
                                  scrollNotification.metrics.pixels ==
                                      scrollNotification
                                          .metrics.maxScrollExtent) {
                                final cookbookState =
                                ref.read(cookbookNotifierProvider);
                                if (cookbookState.hasMore &&
                                    cookbookState.status !=
                                        AppStatus.loadingMore) {
                                  ref
                                      .read(cookbookNotifierProvider.notifier)
                                      .fetchCookbooks(
                                    loadMore: true,
                                    context: context,
                                  );
                                }
                              }
                              return false;
                            },
                            child: Builder(
                              builder: (context) {
                                if (cookbookState.status == AppStatus.loading ||
                                    cookbookState.status ==
                                        AppStatus.loadingMore) {
                                  return CookbookListShimmer(
                                    itemCount: 20,
                                  );
                                } else if (cookbookState.status ==
                                    AppStatus.error) {
                                  return Center(
                                      child: Text(
                                          'Error: ${cookbookState.errorMessage}'));
                                } else if (cookbookState.data == null ||
                                    cookbookState.data!.isEmpty) {
                                  return const Center(
                                      child: Text('No cookbooks found'));
                                }

                                return ListView.separated(
                                  controller: _scrollController,
                                  padding: const EdgeInsets.all(12),
                                  itemCount: cookbookState.data!.length +
                                      (cookbookState.hasMore ? 1 : 0),
                                  separatorBuilder: (_, __) =>
                                  const SizedBox(height: 12),
                                  itemBuilder: (context, index) {
                                    if (index >= cookbookState.data!.length) {
                                      return CookbookListShimmer(
                                        itemCount: 8,
                                      );
                                    }

                                    final cookbook = cookbookState.data![index];
                                    return GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _selectedCookbookIndex = index;
                                          _currentPage = 0;
                                        });
                                        ref
                                            .read(recipeNotifierProvider.notifier)
                                            .fetchRecipes(
                                            cookbookId: cookbook.id,
                                            cookbookName: cookbook.name,
                                            reset: true,
                                            context: context);
                                      },
                                      child: CookbookDetailCard(
                                        cookbook: cookbook,
                                        isSelected:
                                        _selectedCookbookIndex == index,
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Right side: grid of recipe cards + import card
                  Expanded(
                    flex: 6,
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.asset(
                          AssetsManager.background_img,
                          fit: BoxFit.cover,
                        ),
                        Builder(builder: (context) {
                          List<Recipe> recipeList = recipeState.data ?? [];
                          final crossAxisCount = ScreenSizer().calculateCrossAxisCountScreenSize(
                            context: context,
                            screenSize: MediaQuery.of(context).size,
                          );
                          final itemsPerPage = crossAxisCount * 2;
                          final totalPages = recipeState.totalItems > 0
                              ? (recipeState.totalItems / itemsPerPage).ceil()
                              : 1;

                          if (recipeState.status == AppStatus.loading ||
                              recipeState.status == AppStatus.loadingMore) {
                            return RecipeShimmer(
                              crossAxisCount: crossAxisCount,
                              itemsPerPage: itemsPerPage,
                            );
                          } else if (recipeState.status == AppStatus.error) {
                            return Center(child: Text('Error: ${recipeState.errorMessage}'));
                          } else if (recipeState.status == AppStatus.empty || recipeList.isEmpty) {
                            return Container(
                              margin: EdgeInsets.only(
                                right: screenSize.height * 0.9,
                                bottom: screenSize.height * 0.5,
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: screenSize.width * 0.03,
                                vertical: screenSize.width * 0.03,
                              ),
                              child: ImportCreateCard(
                                title: "Add New Recipe",
                                importText: "Import Recipe",
                                isRecipe: true,
                                onImport: () => [],
                                onCreate: () async {
                                  final result = await context.push(
                                    '/cookbook/cookbookDetail/addRecipe',
                                    extra: cookbookState.data!.isNotEmpty
                                        ? cookbookState.data![_selectedCookbookIndex]
                                        : null,
                                  );
                                  print("Add Recipe Result: $result");
                                },
                              ),
                            );
                          }

                          return Column(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: screenSize.width * 0.03,
                                    vertical: screenSize.width * 0.03,
                                  ),
                                  child: Listener(
                                    onPointerSignal: (pointerSignal) {
                                      if (pointerSignal is PointerScrollEvent) {
                                        final delta = pointerSignal.scrollDelta.dx != 0
                                            ? pointerSignal.scrollDelta.dx
                                            : pointerSignal.scrollDelta.dy;
                                        if (delta > 0 && _currentPage < totalPages - 1) {
                                          _pageController.nextPage(
                                            duration: const Duration(milliseconds: 300),
                                            curve: Curves.easeInOut,
                                          );
                                        } else if (delta < 0 && _currentPage > 0) {
                                          _pageController.previousPage(
                                            duration: const Duration(milliseconds: 300),
                                            curve: Curves.easeInOut,
                                          );
                                        }
                                      }
                                    },
                                    child: PageView.builder(
                                      controller: _pageController,
                                      onPageChanged: (int page) {
                                        setState(() {
                                          _currentPage = page;
                                        });

                                        if ((page + 1) >= totalPages && recipeState.hasMore) {
                                          ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                                            cookbookId: cookbookState.data![_selectedCookbookIndex].id,
                                            cookbookName: cookbookState.data![_selectedCookbookIndex].name,
                                            reset: false,
                                            context: context,
                                          );
                                        }
                                      },
                                      itemCount: totalPages,
                                      itemBuilder: (context, pageIndex) {
                                        final startIndex = pageIndex * itemsPerPage;
                                        final endIndex = (pageIndex + 1) * itemsPerPage;

                                        final pageItems = recipeList.sublist(
                                          startIndex,
                                          endIndex < recipeList.length ? endIndex : recipeList.length,
                                        );

                                        return LayoutBuilder(builder: (context, con) {
                                          final size = MediaQuery.of(context).size;
                                          final aspectRatio = ScreenSizer().calculateChildDetailScrenAspectRatio(
                                            size.width,
                                            size.height,
                                          );
                                          return GridView.builder(
                                            physics: const NeverScrollableScrollPhysics(),
                                            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                              crossAxisCount: crossAxisCount,
                                              mainAxisSpacing: screenSize.height * 0.004,
                                              crossAxisSpacing: screenSize.width * 0.004,
                                              childAspectRatio: aspectRatio,
                                            ),
                                            itemCount: pageIndex == 0 ? itemsPerPage : pageItems.length,
                                            itemBuilder: (context, index) {
                                              final selectedCookbook = cookbookState.data!.isNotEmpty
                                                  ? cookbookState.data![_selectedCookbookIndex]
                                                  : null;

                                              if (pageIndex == 0 && index == 0) {
                                                return ImportCreateCard(
                                                  title: "Add New Recipe",
                                                  importText: "Import Recipe",
                                                  isRecipe: true,
                                                  onImport: () => [],
                                                  onCreate: () => context.go(
                                                    '/cookbook/cookbookDetail/addRecipe',
                                                    extra: selectedCookbook,
                                                  ),
                                                );
                                              }

                                              final adjustedIndex = pageIndex == 0 ? index - 1 : index;
                                              final adjustedGlobalIndex = startIndex + adjustedIndex;

                                              if (adjustedGlobalIndex >= recipeList.length) {
                                                return const SizedBox.shrink();
                                              }

                                              final recipe = recipeList[adjustedGlobalIndex];
                                              return RecipeCard(
                                                recipe: recipe,
                                                cookbookId: selectedCookbook?.id,
                                                onPressed: () async {
                                                  final result = await context.push(
                                                    '/cookbook/cookbookDetail/recipeDetail/${recipe.id}',
                                                    extra: {
                                                      'id': recipe.id,
                                                      'recipeList': recipeList,
                                                      'recipeName': recipe.name,
                                                      'cookbookId': selectedCookbook?.id,
                                                    },
                                                  );
                                                  print("Recipe Detail Result: $result");
                                                },
                                              );
                                            },
                                          );
                                        });
                                      },
                                    ),
                                  ),
                                ),
                              ),
                              if (totalPages > 1)
                                PaginationIndicator(
                                  totalPages: totalPages,
                                  currentPage: _currentPage,
                                  hasMore: recipeState.hasMore,
                                  screenSize: screenSize,
                                  context: context,
                                ),
                              SizedBox(height: 100,)
                            ],
                          );
                        }),
                      ],
                    ),
                  ),


                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}