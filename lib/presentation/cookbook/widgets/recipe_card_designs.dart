
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../app/assets_manager.dart';

class RecipeCardDesigns extends StatelessWidget {
  const RecipeCardDesigns({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(220, 312),
      builder: (_, child) => Container(
        width: 320.w,
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10.r,
              offset: Offset(0, 4),
            )
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(15.r),
                  child: Image.network(
                    AssetsManager.meal,
                    width: double.infinity,
                    height: 160.h,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  top: 10.h,
                  right: 10.w,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    padding: EdgeInsets.all(6.r),
                    child:  SvgPicture.asset(
                      AssetsManager.rating,
                      height: 24.h,
                      width: 24.w,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 10.h,
                  left: 10.w,
                  child: Row(
                    children: [
                      Icon(Icons.access_time, color: Colors.white, size: 18.r),
                      SizedBox(width: 4.w),
                      Text("15 min",
                          style: TextStyle(color: Colors.white, fontSize: 14.sp)),
                      SizedBox(width: 10.w),
                      Container(width: 1.w, height: 18.h, color: Colors.white),
                      SizedBox(width: 10.w),
                      Icon(Icons.restaurant, color: Colors.white, size: 18.r),
                      SizedBox(width: 4.w),
                      Text("4 persons",
                          style: TextStyle(color: Colors.white, fontSize: 14.sp)),
                    ],
                  ),
                )
              ],
            ),
            SizedBox(height: 10.h),
            Row(
              children: [
                Expanded(
                  child: Text(
                    "Butter Scotch Cake",
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Icon(Icons.share, color: Colors.blue, size: 20.r),
                SizedBox(width: 4.w),
                Icon(Icons.more_vert, size: 20.r),
              ],
            ),
            SizedBox(height: 6.h),
            Text(
              "In a bowl, mix together the ricotta cheese, milk, lemon zest, lemon juice, and......",
              style: TextStyle(fontSize: 13.sp, color: Colors.grey[600]),
            ),
            Divider(height: 20.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.star, color: Colors.green, size: 18.r),
                    SizedBox(width: 4.w),
                    Text("4.7(23 reviews)",
                        style: TextStyle(fontSize: 13.sp, color: Colors.black)),
                  ],
                ),
                Text("-5 days ago", style: TextStyle(color: Colors.grey, fontSize: 13.sp))
              ],
            ),
            SizedBox(height: 10.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                ),
                onPressed: () {},
                child: Text("View recipe",
                    style: TextStyle(fontSize: 16.sp, color: Colors.white)),
              ),
            )
          ],
        ),
      ),
    );
  }
}