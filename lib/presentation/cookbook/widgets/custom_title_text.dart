import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class CustomeTitleText extends StatelessWidget {
  final String title;
  const CustomeTitleText({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Text(title,
        style: context.theme.textTheme.labelLarge!.copyWith(
          color: context.theme.hintColor,
          fontSize: 28.sp,
          fontWeight: FontWeight.w700,
        ));
  }
}
