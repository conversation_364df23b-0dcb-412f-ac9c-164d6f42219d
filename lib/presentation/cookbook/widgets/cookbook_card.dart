import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../core/data/models/cookbook.dart';
import '../../../core/providers/cookboor_notifier.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/common_image.dart';

class CookbookCard extends ConsumerWidget {
  final Cookbook cookbook;
  final bool isHighRes;
  final VoidCallback onTap;

  const CookbookCard({
    super.key,
    required this.cookbook,
    required this.isHighRes,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final cookbookState = ref.watch(cookbookNotifierProvider);

    // Show loading indicator when deleting
    final isDeleting = cookbookState.status == AppStatus.loading;

    return Card(
      color: context.theme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: screenSize.width * 0.005,
          right: screenSize.width * 0.005,
          top: screenSize.width * 0.004,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(15),
                        bottom: Radius.circular(15),
                      ),
                      child: CommonImage(
                        imageSource: cookbook.coverImageUrl,
                        placeholder: AssetsManager.cb_place_holder,
                        // or File or asset path
                        height: ScreenSizer().calculateImageHeight(context),
                        width: screenSize.width,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Align(
                      alignment: Alignment.topRight,
                      child: IconButton(
                          onPressed: () async {
                            cookbookNotifier.pickImage(context, ref,cookbook.id.toString(), cookbook.name);
                          },
                          icon: Icon(Icons.camera_alt_rounded)),
                    )
                  ],
                ),
                SizedBox(height: 16.h),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            cookbook.name,
                            style:
                                context.theme.textTheme.labelSmall!.copyWith(
                              color: AppColors.primaryGreyColor,
                              fontSize: responsiveFont(26).sp,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 15.h),
                        Row(
                          children: [
                            // Edit button
                            GestureDetector(
                              onTap: onTap,
                              child: Padding(
                                padding: EdgeInsets.all(0.w),
                                child: SvgPicture.asset(
                                  AssetsManager.edit,
                                  height: 30.h,
                                  width: 30.w,
                                ),
                              ),
                            ),
                            SizedBox(width: 15.h),
                            // Delete button with confirmation dialog
                            GestureDetector(
                              onTap: () async {
                                if (isDeleting) return;
                                // Show confirmation dialog
                                await cookbookNotifier.deleteCookbook(
                                    context, cookbook.id.toString());
                              },
                              child: SvgPicture.asset(
                                AssetsManager.dlt_recipe,
                                height: 30.h,
                                width: 30.w,
                              ),
                            ),
                            SizedBox(width: 5.h),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: 16.h),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${cookbook.recipeCount} recipes',
                          style: context.theme.textTheme.labelSmall!.copyWith(
                            color: AppColors.textGreyColor,
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Text(
                          'Created : ${timeago.format(cookbook.dateAdded, locale: 'en_short').replaceAll('just now ago', 'just now')}',
                          style: context.theme.textTheme.labelSmall!.copyWith(
                            color: AppColors.textGreyColor,
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16.h),
                    CustomButton(
                      text: 'Open Cookbook',
                      fontSize: 22.sp,
                      onPressed: () {
                        if (!isDeleting) {
                          context.go('/cookbook/cookbookDetail?id=${cookbook.id}&name=${Uri.encodeComponent(cookbook.name)}');
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
