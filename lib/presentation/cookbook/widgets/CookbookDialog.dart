import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/cookboor_notifier.dart';

// Assuming AppState and AppStatus are defined in a shared file
//final cookbookNameProvider = StateProvider<String>((ref) => '');

class CookbookDialog extends HookConsumerWidget {
  final String title;
  final String hintText;
  final String successText;
  final String buttonText;
  final String successButtonText;
  final bool callFromUpdate; // Default to false, can be set to true if needed
  final VoidCallback? onSuccess;
  final Function(String, BuildContext, WidgetRef)? onCreate;
  final String? cookbookId;
  final String? cookbookName;


  const CookbookDialog({
    super.key,
    required this.title,
    required this.hintText,
    required this.successText,
    required this.buttonText,
    required this.successButtonText,
    this.onSuccess,
    this.onCreate,
    this.cookbookId,
    this.cookbookName,
    this.callFromUpdate = false,
  });


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final appState = ref.watch(cookbookNotifierProvider);
    final nameController = useTextEditingController(text: cookbookName ?? '');
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // Show SnackBar for errors
    ref.listen(cookbookNotifierProvider, (previous, next) {
      if (next.status == AppStatus.error && next.errorMessage != null) {
        Utils().showSnackBar(context, next.errorMessage!);
      }
    });

    return Dialog(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(26)),
      child: Container(
        padding: EdgeInsets.only(left: 25.w, right: 25.w, top: 20.h, bottom: 15.h),
        width: MediaQuery.of(context).size.width / 4,
        height: MediaQuery.of(context).size.height / 3.5,
        child:  appState.status == (callFromUpdate ? AppStatus.updateSuccess : AppStatus.createSuccess)?
              _buildSuccessContent(context, ref, successText, successButtonText)
            : _buildFormContent(context, ref, nameController, formKey, cookbookNotifier),
      ),
    );
  }

  Widget _buildFormContent(
      BuildContext context,
      WidgetRef ref,
      TextEditingController controller,
      GlobalKey<FormState> formKey,
      CookbookNotifier cookbookNotifier,
      ) {
    final appState = ref.watch(cookbookNotifierProvider);

    return Form(
      key: formKey,
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _closeButton(context, ref),
              Text(
                title,
                style: context.theme.textTheme.labelLarge!.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 40.sp,
                ),
              ),
              SizedBox(height: 50.h),
              Stack(
                children: [
                  SizedBox(
                    width: 300,
                    child: Container(
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      child: CustomTextField(
                        hintText: hintText,
                        controller: controller,
                        keyboardType: TextInputType.text,
                        autoFocus: true,
                        formats: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'[a-zA-Z0-9\s]'),
                          ),
                        ],
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter cookbook name';
                          }
                          if (value.trim().length < 3) {
                            return 'Cookbook name must be at least 3 characters';
                          }
                          if (value.trim().length > 100) {
                            return 'Cookbook name must not exceed 100 characters';
                          }
                          if (RegExp(r'\s{2,}').hasMatch(value)) {
                            return 'Cookbook name should not contain multiple spaces in a row';
                          }
                          if (value != value.trim()) {
                            return 'Cookbook name should not start or end with a space';
                          }
                          return null;
                        },
                        onChanged: (val){}//ref.read(cookbookNameProvider.notifier).state = val,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 50.h),
              CustomButton(
                width: 200.w,
                fontSize: 22.sp,
                text: buttonText,
                isLoading: appState.status == (callFromUpdate ? AppStatus.updating : AppStatus.creating), // Bind to AppStatus.loading
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    if (onCreate != null) {
                      await onCreate!(controller.text.trim(), context, ref);
                    } else {
                      if(callFromUpdate){
                        await cookbookNotifier.updateCookbook(id: cookbookId!, name: controller.text.trim(), filePath: File(''), context,callFromUpdate: false);
                      } else {
                       await cookbookNotifier.createCookbook(context, controller.text.trim());

                      }

                    }
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessContent(
      BuildContext context,
      WidgetRef ref,
      String successResponse,
      String buttonText,
      ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: IconButton(
            icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
            onPressed: (){
              Navigator.of(context).pop();
              ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(context: context);
            }
          ),
        ),
      //  _closeButton(context, ref),
        SvgPicture.asset(
          AssetsManager.success,
          height: 80.h,
          width: 80.w,
        ),
        SizedBox(height: 40.h),
        Center(
          child: Text(
            successResponse,
            textAlign: TextAlign.center,
            style: context.theme.textTheme.displaySmall!.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.primaryGreyColor,
              fontSize: 32.sp,
            ),
          ),
        ),
        SizedBox(height: 40.h),
        CustomButton(
          width: 250.w,
          fontSize: 22.sp,
          isLoading: ref.watch(cookbookNotifierProvider).status == AppStatus.loading,
          text: buttonText,
          onPressed: () async {
            Navigator.of(context).pop();
            ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(context: context);

          },
        ),
      ],
    );
  }

  Widget _closeButton(BuildContext context, WidgetRef ref) {
     return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () {
          // Reset the cookbook notifier state when closing
          ref.read(cookbookNotifierProvider.notifier).resetToIdle();
          Navigator.of(context).pop();
        },
      ),
    );
  }
}