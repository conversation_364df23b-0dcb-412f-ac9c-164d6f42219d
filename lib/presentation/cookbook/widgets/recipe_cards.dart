import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_popup_menu.dart';
import '../../../../../core/utils/device_utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_doted_lines.dart';
import '../../../../../core/widgets/custom_text_medium.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/data/models/recipe_response.dart';
import '../../../../core/helpers/date_formatter.dart';
import '../../../../core/widgets/custom_loading.dart';
import '../../../../main.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/common_image.dart';

class RecipeCard extends ConsumerWidget {
  final Recipe recipe;
  final VoidCallback onPressed;
  final int? cookbookId;

  const RecipeCard({super.key, required this.recipe, required this.onPressed,this.cookbookId });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;

     return Stack(
      children: [
        Card(
          color: context.theme.cardColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          clipBehavior: Clip.hardEdge,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: screenSize.width * 0.005,
                vertical: screenSize.width * 0.005),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// Image with badge and bottom overlay
                Stack(
                  children: [
                    /// Recipe Image
                    ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CommonImage(
                          imageSource: recipe.mediaUrl ?? '',
                          placeholder: AssetsManager.recipe_place_holder,
                          // or File or asset path
                          width: double.infinity,
                          height:
                              ScreenSizer().calculateRecipeImageHeight(context),
                          fit: BoxFit.cover,
                        )),

                    /// Bottom Overlay: Time & Persons
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          borderRadius: const BorderRadius.vertical(
                              bottom: Radius.circular(12)),
                        ),
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 15.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            /// Time Section
                            Row(
                              children: [
                                Icon(Icons.access_time,
                                    color: Colors.white, size: 25.sp),
                                SizedBox(width: 8.w),
                                Text(recipe.totalTime ?? '',
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 20.sp)),
                              ],
                            ),

                            /// Vertical Divider
                            Container(
                              width: 1.w,
                              height: 20.h,
                              color: Colors.white,
                            ),

                            /// Person Section
                            Row(
                              children: [
                                SvgPicture.asset(AssetsManager.dining),
                                //Icon(Icons.restaurant, color: Colors.white, size: 20.sp),
                                SizedBox(width: 8.w),
                                Text(recipe.servings.toString(),
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 20.sp)),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                /// Below image content
                SizedBox(height: 10.h),

                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        recipe.name,
                        style: context.theme.textTheme.bodyMedium!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontSize: responsiveFont(28).sp,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () {},
                          child: SvgPicture.asset(AssetsManager.share,
                              height: 35.h, width: 35.w),
                        ),
                        CustomPopupMenu(
                          onSelected: (value) async {
                            final loading = ref.read(loadingProvider.notifier);
                            final recipeNotifier =
                                ref.read(recipeNotifierProvider.notifier);

                            await recipeNotifier.deleteRecipe(
                                context: context,
                                cookbookId: cookbookId??0,
                                recipeId: recipe.id);


                          },

                          backgroundColor: Colors.white,
                          textColor: Colors.black,
                          deleteTextColor: AppColors.primaryColor, // optional
                        ),
                      ],
                    ),
                  ],
                ),

                SizedBox(height: 1.h),

                CustomTextMedium(
                  title: recipe.description ?? '',
                  maxLines: 2,
                ),

                // SizedBox(height: 12.h),
                Spacer(),
                CustomPaint(
                  painter: DottedLinePainter(
                    strokeWidth: 1,
                    dashWidth: 6,
                    color: AppColors.lightestGreyColor,
                  ),
                  size: Size(double.infinity, 2),
                ),
                Spacer(),
                Row(
                  children: [
                    SvgPicture.asset(
                      AssetsManager.rating,
                      height: 24.h,
                      width: 24.w,
                    ),
                    SizedBox(width: 4),
                    CustomTextMedium(
                        title: "${recipe.reviewsCount} ( reviews)"),
                    Spacer(),
                    CustomTextMedium(
                        title: DateFormatter.timeAgo(recipe.dateAdded!)),
                  ],
                ),

                //  SizedBox(height: 20.h),

                Spacer(),
                CustomButton(
                    fontSize: 22.sp, text: 'View Recipe', onPressed: onPressed),
                Spacer(),
              ],
            ),
          ),
        ),

        /// Top Right SVG Badge
        Positioned(
          top: 8.h,
          right: 20.w,
          child: SvgPicture.asset(
            AssetsManager.rewards, // your reward.svg asset path
            width: 50.w,
            height: 50.h,
          ),
        ),
      ],
    );
  }
}
