import 'dart:io';

import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/controllers/shopping_notifier.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_loading.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../core/providers/shopping/models/pantry_list_state.dart';
import '../../../core/providers/shopping/models/shopping_list_state.dart';
import '../../../core/providers/shopping/pantry_provider.dart';
import '../../../core/providers/shopping/controllers/pantry_notifier.dart';
import '../../../core/network/app_status.dart';
// Removed unused import
import '../../../core/providers/shopping/shopping_provider.dart';


enum DialogType { shopping, pantry }

final shoppingItemProvider = StateProvider<String>((ref) => '');
final isShoppingListCreatedProvider = StateProvider<bool>((ref) => false);

class ShoppingDialog extends HookConsumerWidget {
  final DialogType dialogType;
  final String title;
  final String hintText;
  final String successText;
  final String buttonText;
  final String successButtonText;
   final bool callFromUpdate; // Default to false, can be set to true if needed
  final VoidCallback? onSuccess;
  final Function(String, BuildContext, WidgetRef)? onCreate;
  final String? shoppingId;
  final String? shoppingName;

  const ShoppingDialog({
    super.key,
    required this.dialogType,
    required this.title,
    required this.hintText,
    required this.successText,
    required this.buttonText,
    required this.successButtonText,
    this.onSuccess,
    this.onCreate,
    this.shoppingId,
    this.shoppingName,
    this.callFromUpdate = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final shoppingNotifier = ref.read(shoppingNotifierProvider.notifier);
    final shoppingListNotifier = ref.read(shoppingListNotifierProvider.notifier);
    final pantryListNotifier = ref.read(pantryListNotifierProvider.notifier);
    final pantryNotifier = ref.read(pantryNotifierProvider.notifier);
    final itemController = useTextEditingController(text: shoppingName ?? '');
    final isCreated = ref.watch(isShoppingListCreatedProvider);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    final shoppingListState = ref.watch(shoppingNotifierProvider);


    // Watch the appropriate notifier based on dialog type
    final shoppingState = ref.watch(shoppingNotifierProvider);
    final pantryState = ref.watch(pantryNotifierProvider);

    // Listen for errors and success in update operations
    if (callFromUpdate) {
      ref.listen(shoppingNotifierProvider, (previous, next) {
        if (next.status == AppStatus.updateError && next.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                next.errorMessage!,
                style: context.theme.textTheme.labelMedium!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                ),
              ),
              backgroundColor: Theme.of(context).primaryColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Success state will be handled by user interaction (Done button or close button)
        // No automatic closure
      });

      ref.listen(pantryNotifierProvider, (previous, next) {
        if (next.status == AppStatus.updateError && next.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                next.errorMessage!,
                style: context.theme.textTheme.labelMedium!.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                ),
              ),
              backgroundColor: Theme.of(context).primaryColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Success state will be handled by user interaction (Done button or close button)
        // No automatic closure
      });
    }

    // Determine if we should show success content
    final shouldShowSuccess = callFromUpdate
        ? (dialogType == DialogType.shopping
            ? shoppingState.status == AppStatus.updateSuccess
            : pantryState.status == AppStatus.updateSuccess)
        : isCreated;

    return Dialog(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(10),
        width: MediaQuery.of(context).size.width / 4,
        height: MediaQuery.of(context).size.height /3.5,
        child: shouldShowSuccess
            ? _buildSuccessContent(context, ref, successText, successButtonText)
            : _buildFormContent(
                context, ref, itemController, formKey, shoppingNotifier, shoppingListNotifier, pantryListNotifier, pantryNotifier),
      ),
    );
  }

  Widget _buildFormContent(
    BuildContext context,
    WidgetRef ref,
    TextEditingController controller,
    GlobalKey<FormState> formKey,
    shoppingNotifier,
    shoppingListNotifier,
    pantryListNotifier,
    pantryNotifier,
  ) {
    return Form(
      key: formKey,
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _closeButton(context, ref),
              Text(
                title,
                style: context.theme.textTheme.labelLarge!.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 40.sp,
                ),
              ),
              SizedBox(height: 50.h),
              Stack(
                children: [
                  SizedBox(
                    width: 300, // Set your desired width here
                    child:
                    Container(
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),

                      child: CustomTextField(
                      hintText: hintText,
                      controller: controller,
                      keyboardType: TextInputType.text,
                      autoFocus: true,
                      formats: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'[a-zA-Z0-9\s]'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter ${dialogType == DialogType.shopping ? 'shopping' : 'pantry'} name';
                        }
                        if (value.trim().length < 3) {
                          return '${dialogType == DialogType.shopping ? 'shopping' : 'pantry'} name must be at least 3 characters';
                        }
                        if (value.trim().length > 100) {
                          return '${dialogType == DialogType.shopping ? 'shopping' : 'pantry'} name must not exceed 100 characters';
                        }
                        if (RegExp(r'\s{2,}').hasMatch(value)) {
                          return '${dialogType == DialogType.shopping ? 'shopping' : 'pantry'} name should not contain multiple spaces in a row';
                        }
                        if (value != value.trim()) {
                          return '${dialogType == DialogType.shopping ? 'shopping' : 'pantry'} name should not start or end with a space';
                        }
                        return null;
                      },
                      onChanged: (val) =>
                          ref.read(shoppingItemProvider.notifier).state = val,
                    ),
                  ),
                  ),
                ],
              ),
              SizedBox(height: 60.h),
              CustomButton(
                width: 200.w,
                fontSize: 22.sp,
                isLoading: callFromUpdate
                    ? (dialogType == DialogType.shopping
                        ? ref.watch(shoppingNotifierProvider).status == AppStatus.updating
                        : ref.watch(pantryNotifierProvider).status == AppStatus.updating)
                    : ref.watch(loadingProvider),
                text: buttonText,
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    if (callFromUpdate) {
                      // Handle update operations
                      if (dialogType == DialogType.shopping) {
                        await shoppingNotifier.updateShopping(
                          context,
                          id: shoppingId!,
                          name: controller.text.trim(),
                          filePath: File(''), // Empty file for name-only updates
                          callFromUpdate: true, // Enable list refresh after update
                        );
                      } else if (dialogType == DialogType.pantry) {
                        await pantryNotifier.updatePantry(
                          context,
                          id: shoppingId!, // Using shoppingId as it's the generic ID field
                          name: controller.text.trim(),
                          filePath: File(''), // Empty file for name-only updates
                          callFromUpdate: true, // Enable list refresh after update
                        );
                      }     
                    } else {
                      // Handle create operations
                      final loading = ref.read(loadingProvider.notifier);
                      loading.state = true;

                      if (dialogType == DialogType.shopping) {
                        final (shoppingStatus, shoppingMessage) = await shoppingListNotifier.createShoppingList(
                          controller.text.trim(),
                        );

                        if (shoppingStatus == ShoppingListStatus.error) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                shoppingMessage ?? 'Unknown error',
                                style: context.theme.textTheme.labelMedium!.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              backgroundColor: Theme.of(context).primaryColor,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }

                        if (shoppingStatus == ShoppingListStatus.success) {
                          shoppingNotifier.fetchShoppingLists(context: context);
                          ref.read(isShoppingListCreatedProvider.notifier).state = true;
                        }

                      } else if (dialogType == DialogType.pantry) {
                        final (pantryStatus, pantryMessage) = await pantryListNotifier.createPantryList(
                          controller.text.trim(),
                        );

                        if (pantryStatus == PantryListStatus.error) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                pantryMessage ?? 'Unknown error',
                                style: context.theme.textTheme.labelMedium!.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              backgroundColor: Theme.of(context).primaryColor,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }

                        if (pantryStatus == PantryListStatus.success) {
                          ref.read(isShoppingListCreatedProvider.notifier).state = true;
                        }
                      }
                      loading.state = false;
                    }
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessContent(
    BuildContext context,
    WidgetRef ref,
    String successResponse,
    String buttonText,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _closeButton(context, ref),
        SvgPicture.asset(
          AssetsManager.success,
          height: 80.h,
          width: 80.w,
        ),
        SizedBox(height: 40.h),
        Text(
          successResponse,
          textAlign: TextAlign.center, // Center text within the widget
          style: context.theme.textTheme.displaySmall!.copyWith(
            fontWeight: FontWeight.w400,
            color: AppColors.primaryGreyColor,
            fontSize: 32.sp,
          ),
        ),
        SizedBox(height: 50.h),
        CustomButton(
          width: 300.w,
          fontSize: 22.sp,
          text: buttonText,
          onPressed: () {
            Navigator.of(context).pop();
            ref.read(isShoppingListCreatedProvider.notifier).state = false;
            ref.read(shoppingItemProvider.notifier).state = '';

            // Reset notifier states for both create and update operations
            if (dialogType == DialogType.shopping) {
              ref.read(shoppingNotifierProvider.notifier).resetToIdle();
            } else {
              ref.read(pantryNotifierProvider.notifier).resetToIdle();
            }
          },
        ),
      ],
    );
  }

  Widget _closeButton(BuildContext context, WidgetRef ref) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () {
          Navigator.of(context).pop();
          // Reset the provider state when closing
          ref.read(isShoppingListCreatedProvider.notifier).state = false;
          ref.read(shoppingItemProvider.notifier).state = '';

          // Reset notifier states for both create and update operations
          if (dialogType == DialogType.shopping) {
            ref.read(shoppingNotifierProvider.notifier).resetToIdle();
          } else {
            ref.read(pantryNotifierProvider.notifier).resetToIdle();
          }
        },
      ),
    );
  }
}
