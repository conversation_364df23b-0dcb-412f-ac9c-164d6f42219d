import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../app/imports/core_imports.dart';

class DropdownFilter extends StatefulWidget {
  final String filterName;
  final Map<String, String> selectedFilters;
  final Map<String, List<String>> filterOptions;

  const DropdownFilter({
    Key? key,
    required this.filterName,
    required this.selectedFilters,
    required this.filterOptions,
  }) : super(key: key);

  @override
  _DropdownFilterState createState() => _DropdownFilterState();
}

class _DropdownFilterState extends State<DropdownFilter> {
  @override
  Widget build(BuildContext context) {
    final selected = widget.selectedFilters[widget.filterName]!;
    return PopupMenuButton<String>(
      color: Colors.white,
      onSelected: (value) {
        setState(() {
          widget.selectedFilters[widget.filterName] = value;
        });
      },
      itemBuilder: (context) {
        return widget.filterOptions[widget.filterName]!.map((option) {
          return PopupMenuItem<String>(
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              decoration: option == selected
                  ? BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(6),
              )
                  : null,
              child: Text(
                option,
                style: TextStyle(
                  fontSize: context.theme.textTheme.titleMedium!.fontSize,
                  fontWeight: option == selected ? FontWeight.w400 : FontWeight.w400,
                  color: option == selected
                      ? Colors.white
                      : AppColors.primaryGreyColor,
                ),
              ),
            ),
          );
        }).toList();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        margin: EdgeInsets.only(left: 20.w, right: 20.w),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black38.withOpacity(.2)),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Text(
              widget.filterName,
              style: TextStyle(
                fontSize: context.theme.textTheme.titleMedium!.fontSize,
                fontWeight: FontWeight.w400,
                color: AppColors.primaryGreyColor,
              ),
            ),
            SizedBox(width: 5.w),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 5.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black38.withOpacity(.2)),
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(2),
              ),
              child: Icon(
                Icons.keyboard_arrow_down,
                size: 20.w,
                color: AppColors.secondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}