import 'dart:ui';

import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/widgets/custom_button.dart';

class ImportCreateCard extends StatelessWidget {
  final VoidCallback onImport;
  final VoidCallback onCreate;
  final bool isRecipe;
  final String title;
  final String importText;

  const ImportCreateCard(
      {super.key,
      required this.onImport,
      required this.onCreate,
      required this.isRecipe,
      required this.title,
      required this.importText});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Card(
        elevation: 0,
        color: context.theme.cardColor,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 30.w, right: 30.w,top: 30.h,bottom: 30.h),
          child: CustomPaint(
            painter: DottedBorderPainter(),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Import Button
                GestureDetector(
                  onTap: onImport,
                  child: Text(importText,
                      style: context.theme.textTheme.labelMedium!.copyWith(
                        //  color: context.theme.cardColor,
                        //fontSize: 8.sp,
                        fontSize: 22.sp,
                        fontWeight: FontWeight.normal,
                        decoration: TextDecoration.underline,
                        decorationColor: AppColors.primaryGreyColor,
                        decorationThickness: 1.0,
                      )),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  'or',
                  style: Theme.of(context).textTheme.displaySmall?.copyWith(
                    decorationColor: AppColors.textGreyColor,
                    fontWeight: FontWeight.normal,
                    fontSize: 22.sp,

                  ),
                ),

                SizedBox(
                  height: 30.h,
                ),
                Container(
                  margin: EdgeInsets.only(left: 40.w, right: 40.w),
                  child: CustomButton(
                      text: title, fontSize: 20.sp, onPressed: onCreate),
                ),
                // Create Button
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DottedBorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.backgroudInActiveColor
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final dashWidth = 5.0;
    final dashSpace = 5.0;

    final radius = Radius.circular(12);
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final rRect = RRect.fromRectAndRadius(rect, radius);

    final path = Path()..addRRect(rRect);
    final dashedPath = _createDashedPath(path, dashWidth, dashSpace);

    canvas.drawPath(dashedPath, paint);
  }

  Path _createDashedPath(Path source, double dashWidth, double dashSpace) {
    final Path dashedPath = Path();
    for (final PathMetric pathMetric in source.computeMetrics()) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final double length = dashWidth;
        dashedPath.addPath(
          pathMetric.extractPath(distance, distance + length),
          Offset.zero,
        );
        distance += dashWidth + dashSpace;
      }
    }
    return dashedPath;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

