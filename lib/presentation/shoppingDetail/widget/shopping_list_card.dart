import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_network_image.dart';
import '../../../../core/data/models/pantry.dart';
import '../../../../core/data/models/shopping.dart';
import '../../cookbook/widgets/custom_desc_text.dart';

class ShoppingListDetailCard extends StatelessWidget {
  final dynamic listItem; // Can be either ShoppingList, PantryList, or Pantry (old static)
  final bool isSelected;
  final bool isPantry;

  const ShoppingListDetailCard({
    super.key,
    required this.listItem,
    this.isSelected = false,
    this.isPantry = false,
  });

  String get _name {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return (listItem as PantryList).name;
      } else {
        return (listItem as Pantry).title;
      }
    } else {
      return (listItem as ShoppingList).name;
    }
  }

  String get _imageUrl {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return (listItem as PantryList).coverImageUrl ?? "";
      } else {
        return (listItem as Pantry).imageUrl;
      }
    } else {
      return (listItem as ShoppingList).coverImageUrl ?? "";
    }
  }

  int get _itemCount {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return (listItem as PantryList).pantryItemCount;
      } else {
        return (listItem as Pantry).recipeCount;
      }
    } else {
      return (listItem as ShoppingList).shoppingItemsCount;
    }
  }

  String get _creationDate {
    if (isPantry) {
      // Handle both PantryList (new API) and Pantry (old static)
      if (listItem is PantryList) {
        return _formatDate((listItem as PantryList).dateCreated);
      } else {
        return (listItem as Pantry).createdDate;
      }
    } else {
      return _formatDate((listItem as ShoppingList).dateCreated);
    }
  }

  String _formatDate(DateTime date) {
    // Format the date as you prefer, e.g., "5 days ago"
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else {
      return 'Just now';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: Colors.blue, width: 3)
            : BorderSide(color: AppColors.greyBorderColor, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.sp),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
               child: (_imageUrl.isEmpty)
                    ? Image.asset(
                        isPantry
                    ? AssetsManager.pantryDummy
                    : AssetsManager.shoppingDummy,
                  width: 150.w,
                  height: 150.h,
                  fit: BoxFit.cover,
                  )
                    : CustomNetworkImage(
                      imageUrl: _imageUrl,
                      width: 150.w,
                      height: 150.h,
                      errorWidget: Image.asset(
                          isPantry
                        ? AssetsManager.pantryDummy
                        : AssetsManager.shoppingDummy,
                      width: 150.w,
                      height: 150.h,
                      fit: BoxFit.cover,
                  )
                ),
              ),
            SizedBox(width: 12.w),
            // Text and Action Section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          _name,
                          style: context.theme.textTheme.bodyMedium!.copyWith(
                            color: context.theme.hintColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 30.sp,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Row(
                        children: [
                          // Temporarily replaced corrupted SVG with Icon to prevent crash
                          SvgPicture.asset(
                            AssetsManager.share,
                            height: 30.h,
                            width: 30.w,
                          ),
                          // SizedBox(width: 14.w),
                          // Icon(
                          //   Icons.more_vert,
                          //   color: AppColors.backgroudInActiveColor,
                          //   size: 30.h,
                          // ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 30.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomDescText(
                        desc: "$_itemCount Products",
                        size: 22.sp,
                        textColor: AppColors.textGreyColor,
                      ),
                      SizedBox(width: 40.w),
                      CustomDescText(
                        desc: "Created: $_creationDate",
                        size: 18.sp,
                        textColor: AppColors.textGreyColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
