import 'dart:async';
import 'dart:convert';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/core/helpers/customAutocomplete.dart';

import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../core/data/models/pantry.dart';
import '../../core/data/models/shopping.dart';
import '../../core/data/request_query/shopping_item_request.dart' as shopping_request;
import '../../core/data/request_query/pantry_item_request.dart' as pantry_request;

// Commented out - using MasterIngredients.json instead
// import '../../core/providers/search/search_items_notifier.dart';
import '../../core/providers/shopping/controllers/pantry_list_notifier.dart';
import '../../core/providers/shopping/controllers/pantry_notifier.dart';
import '../../core/providers/shopping/controllers/shopping_list_notifier.dart';
import '../../core/providers/profile/user_profile_notifier.dart';

import '../../core/providers/shopping/models/pantry_list_state.dart';
import '../../core/providers/shopping/models/shopping_list_state.dart';
import '../../core/providers/shopping/shopping_provider.dart';
import '../../core/providers/shopping/pantry_provider.dart';
import '../../core/utils/screen_sizer.dart';
import '../../core/widgets/custom_appbar.dart';
import '../shimer/shopping_pantry_list_shimmer.dart';
import '../shopping/sub_view/custom_tabview.dart';
import 'widget/shopping_list_card.dart';
import 'widget/add_shopping_list_card.dart'; // Import for add new list card
import '../cookbook/widgets/shopping_dialog.dart'; // Import for dialog functionality
import 'package:intl/intl.dart'; // For date formatting

class ShoppingListDetailScreen extends ConsumerStatefulWidget {
  const ShoppingListDetailScreen({super.key});

  @override
  ConsumerState<ShoppingListDetailScreen> createState() => _ShoppingListDetailScreenState();
}

class _ShoppingListDetailScreenState extends ConsumerState<ShoppingListDetailScreen> {
  final TextEditingController searchController = TextEditingController();
  final TextEditingController topSearchController = TextEditingController();
  int _selectedIndex = 0; // Track selected cookbook index
  String selectedTab = 'Shopping list';
  final ScrollController _scrollController = ScrollController();

  // Search functionality variables
  Timer? _debounceTimer;
  Timer? _itemSearchDebounceTimer;
  String _currentSearchQuery = '';
  String _currentItemSearchQuery = '';

  // Cache for MasterIngredients.json to avoid reloading
  static List<dynamic>? _masterIngredientsCache;

  // Lists to store new editable rows
  List<Map<String, TextEditingController>> newShoppingRows = [];
  List<Map<String, TextEditingController>> newPantryRows = [];

  // Selection state for pantry items
  Set<int> selectedPantryItemIds = {};
  bool isAllPantryItemsSelected = false;

  // Selection state for shopping items
  Set<int> selectedShoppingItemIds = {};
  bool isAllShoppingItemsSelected = false;

  // Editable pantry items state
  Map<int, Map<String, TextEditingController>> editablePantryItems = {};
  Set<int> modifiedPantryItemIds = {};

  // Editable shopping items state
  Map<int, Map<String, TextEditingController>> editableShoppingItems = {};
  Set<int> modifiedShoppingItemIds = {};


  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 100), () {
        _loadInitialData();
      });
    });
  }

  /// Load initial data and fetch items for the first shopping list and pantry
  Future<void> _loadInitialData() async {
    // Fetch shopping lists and pantries
    await Future.wait([
      ref.read(shoppingListNotifierProvider.notifier).fetchShoppingLists(),
      ref.read(pantryListNotifierProvider.notifier).fetchPantryLists(),
    ]);

    // After lists are loaded, fetch items for the first list/pantry
    final shoppingState = ref.read(shoppingListNotifierProvider);
    final pantryState = ref.read(pantryListNotifierProvider);

    if (shoppingState.shoppingLists.isNotEmpty) {
      final firstShoppingListId = shoppingState.shoppingLists.first.id;
      ref.read(shoppingListItemProvider.notifier).fetchShoppingListsItems(
        id: firstShoppingListId,
      );
    }

    if (pantryState.pantryLists.isNotEmpty) {
      final firstPantryId = pantryState.pantryLists.first.id;
      ref.read(pantryListItemProvider.notifier).fetchPantryListsItems(
        id: firstPantryId,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    searchController.dispose();
    topSearchController.dispose();
    _debounceTimer?.cancel();
    _itemSearchDebounceTimer?.cancel();

    // Dispose shopping row controllers
    for (var row in newShoppingRows) {
      for (var controller in row.values) {
        controller.dispose();
      }
    }

    // Dispose pantry row controllers
    for (var row in newPantryRows) {
      for (var controller in row.values) {
        controller.dispose();
      }
    }

    // Dispose editable pantry item controllers
    for (var controllers in editablePantryItems.values) {
      for (var controller in controllers.values) {
        controller.dispose();
      }
    }

    // Dispose editable shopping item controllers
    for (var controllers in editableShoppingItems.values) {
      for (var controller in controllers.values) {
        controller.dispose();
      }
    }

    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      final shoppingState = ref.read(shoppingListNotifierProvider);
      final pantryState = ref.read(pantryListNotifierProvider);

      if (selectedTab == 'Shopping list' && shoppingState.hasMore) {
        ref.read(shoppingListNotifierProvider.notifier).fetchShoppingLists(
          loadMore: true,
          search: _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
        );
      } else if (selectedTab == 'Pantry' && pantryState.hasMore) {
        ref.read(pantryListNotifierProvider.notifier).fetchPantryLists(
          loadMore: true,
          search: _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
        );
      }
    }
  }

  void _onSearchChanged(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set new timer for debouncing
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentSearchQuery) {
        setState(() {
          _currentSearchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    if (selectedTab == 'Shopping list') {
      // Search in shopping lists
      ref.read(shoppingListNotifierProvider.notifier).fetchShoppingLists(
        search: query.isEmpty ? null : query,
      );
    } else {
      // Search in pantry lists
      ref.read(pantryListNotifierProvider.notifier).fetchPantryLists(
        search: query.isEmpty ? null : query,
      );
    }
  }

  void _onItemSearchChanged(String query) {
    // Cancel previous timer
    _itemSearchDebounceTimer?.cancel();

    // Set new timer for debouncing
    _itemSearchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentItemSearchQuery) {
        setState(() {
          _currentItemSearchQuery = query;
        });
        _performItemSearch(query);
      }
    });
  }

  void _performItemSearch(String query) {
    // Get the currently selected list ID
    final shoppingState = ref.read(shoppingListNotifierProvider);
    final pantryState = ref.read(pantryListNotifierProvider);

    if (selectedTab == 'Shopping list') {
      // Search in shopping list items
      if (shoppingState.shoppingLists.isNotEmpty && _selectedIndex < shoppingState.shoppingLists.length) {
        final selectedShoppingListId = shoppingState.shoppingLists[_selectedIndex].id;
        ref.read(shoppingListItemProvider.notifier).fetchShoppingListsItems(
          id: selectedShoppingListId,
          search: query.isEmpty ? null : query,
        );
      }
    } else {
      // Search in pantry list items
      if (pantryState.pantryLists.isNotEmpty && _selectedIndex < pantryState.pantryLists.length) {
        final selectedPantryListId = pantryState.pantryLists[_selectedIndex].id;
        ref.read(pantryListItemProvider.notifier).fetchPantryListsItems(
          id: selectedPantryListId,
          search: query.isEmpty ? null : query,
        );
      }
    }
  }

  // Search MasterIngredients.json file for autocomplete suggestions
  Future<List<String>> _searchMasterIngredients(String query) async {
    if (query.isEmpty) return [];

    try {
      // Load ingredients from cache or file
      if (_masterIngredientsCache == null) {
        final String jsonString = await rootBundle.loadString('assets/MasterIngredients.json');
        _masterIngredientsCache = json.decode(jsonString);
        print('MasterIngredients.json loaded and cached. Total ingredients: ${_masterIngredientsCache!.length}');
      }

      final List<dynamic> ingredients = _masterIngredientsCache!;

      // Filter ingredients based on query
      final List<String> suggestions = [];
      final String lowerQuery = query.toLowerCase();
      int matchCount = 0;

      // Use a Set to track unique suggestions for better performance
      final Set<String> uniqueSuggestions = {};

      for (final ingredient in ingredients) {
        final String ingredientName = ingredient['IngredientName']?.toString() ?? '';
        final String purchaseAs = ingredient['PurchaseAs']?.toString() ?? '';

        // Search in both IngredientName and PurchaseAs fields
        if (ingredientName.toLowerCase().contains(lowerQuery) ||
            purchaseAs.toLowerCase().contains(lowerQuery)) {

          // Prefer PurchaseAs if available, otherwise use IngredientName
          final String suggestion = purchaseAs.isNotEmpty ? purchaseAs : ingredientName;

          // Add to unique set and suggestions list if not duplicate
          if (suggestion.isNotEmpty && uniqueSuggestions.add(suggestion)) {
            suggestions.add(suggestion);
            matchCount++;

            // Limit results to 15 for better performance
            if (matchCount >= 15) break;
          }
        }
      }

      print('Search for "$query" returned ${suggestions.length} suggestions');
      return suggestions;
    } catch (e) {
      print('Error loading MasterIngredients.json: $e');
      return [];
    }
  }

  Widget _buildListContent(GetShoppingListState shoppingState, GetPantryListState pantryState, List<dynamic> currentList) {
    // Check if we're in initial loading state
    final isShoppingLoading = selectedTab == 'Shopping list' &&
        shoppingState.status == ShoppingListStatus.loading &&
        shoppingState.shoppingLists.isEmpty;

    final isPantryLoading = selectedTab == 'Pantry' &&
        pantryState.status == PantryListStatus.loading &&
        pantryState.pantryLists.isEmpty;

    // Show shimmer during initial loading
    if (isShoppingLoading || isPantryLoading) {
      return const ShoppingPantryListShimmer(itemCount: 5);
    }

    // Temporary: Always show shimmer for testing (remove this after testing)
    // return const ShoppingPantryListShimmer(itemCount: 5);

    // Show the actual list content
    return NotificationListener<ScrollNotification>(
      onNotification: (scrollNotification) {
        if (scrollNotification is ScrollEndNotification &&
            scrollNotification.metrics.pixels ==
                scrollNotification.metrics.maxScrollExtent) {
          final shoppingState = ref.read(shoppingListNotifierProvider);
          final pantryState = ref.read(pantryListNotifierProvider);

          if (selectedTab == 'Shopping list' && shoppingState.hasMore) {
            ref.read(shoppingListNotifierProvider.notifier)
                .fetchShoppingLists(
                  loadMore: true,
                  search: _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                );
          } else if (selectedTab == 'Pantry' && pantryState.hasMore) {
            ref.read(pantryListNotifierProvider.notifier)
                .fetchPantryLists(
                  loadMore: true,
                  search: _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                );
          }
        }
        return false;
      },
      child: ListView.separated(
        controller: _scrollController,
        padding: EdgeInsets.only(left: 12.w, right: 12.w),
        itemCount: currentList.length +
            (((selectedTab == 'Shopping list' && shoppingState.hasMore) ||
                (selectedTab == 'Pantry' && pantryState.hasMore)) ? 1 : 0) +
            1, // Add 1 for the "Add New" card at the top
        separatorBuilder: (_, __) => SizedBox(height: 12.h),
        itemBuilder: (context, index) {
          // Show "Add New" card as the first item
          if (index == 0) {
            return AddShoppingListDetailCard(
              title: selectedTab == 'Shopping list'
                  ? 'Add New Shopping List'
                  : 'Add New Pantry',
              isSelected: false,
              onTap: () {
                if (selectedTab == 'Shopping list') {
                  showShoppingDialog(context, ref);
                } else {
                  showPantryDialog(context, ref);
                }
              },
            );
          }

          // Adjust index for actual list items (subtract 1 for the "Add New" card)
          final listIndex = index - 1;

          // Show loading indicator at the bottom for pagination
          if (listIndex >= currentList.length) {
            return Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16.h),
                child: LoadingAnimationWidget.fallingDot(
                  color: Colors.black54,
                  size: 50.0,
                ),
              ),
            );
          }

          final selectedListId = selectedTab == 'Shopping list'
              ? shoppingState.shoppingLists[listIndex].id
              : pantryState.pantryLists[listIndex].id;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedIndex = listIndex;
              });
              if (selectedTab == 'Shopping list') {
                // Clear selections when switching shopping lists
                selectedShoppingItemIds.clear();
                isAllShoppingItemsSelected = false;

                ref.read(shoppingListItemProvider.notifier)
                    .fetchShoppingListsItems(
                      id: selectedListId,
                      search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
                    );
              } else if (selectedTab == 'Pantry') {
                // Clear selections when switching pantry lists
                selectedPantryItemIds.clear();
                isAllPantryItemsSelected = false;

                ref.read(pantryListItemProvider.notifier)
                    .fetchPantryListsItems(
                      id: selectedListId,
                      search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
                    );
              }
            },
            child: ShoppingListDetailCard(
              listItem: currentList[listIndex],
              isSelected: _selectedIndex == listIndex,
              isPantry: selectedTab == 'Pantry',
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final shoppingState = ref.watch(shoppingListNotifierProvider);
    final pantryState = ref.watch(pantryListNotifierProvider);

    final currentList = selectedTab == 'Shopping list'
        ? shoppingState.shoppingLists
        : pantryState.pantryLists;

    return Scaffold(
      appBar: CustomAppBar(
        title: "Shopping List",
        actions: [
          CustomSearchBar(
            width: 440.w,
            height: 60.h,
            controller: topSearchController,
            onChanged: _onItemSearchChanged,
          )
        ],
      ),
      body: Row(
        children: [
          // Left side: vertical list of shopping/pantry items
          Expanded(
            flex: 2,
            child: Column(
              children: [
                SizedBox(height: 20.h),
                CustomTabView(
                  width: 210.w,
                  fontSize: 20.sp,
                  selectedTabColor: Colors.white,
                  tabs: ['Shopping list', 'Pantry'],
                  selected: selectedTab,
                  bgTabColor: AppColors.greyBorderColor,
                  onChanged: (tab) {
                    setState(() {
                      selectedTab = tab;
                      _selectedIndex = 0;

                      // Clear selections when switching tabs
                      selectedPantryItemIds.clear();
                      isAllPantryItemsSelected = false;
                      selectedShoppingItemIds.clear();
                      isAllShoppingItemsSelected = false;

                      if (selectedTab == 'Shopping list' && shoppingState.shoppingLists.isNotEmpty) {
                        ref.read(shoppingListItemProvider.notifier).fetchShoppingListsItems(
                          id: shoppingState.shoppingLists[0].id,
                          search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
                        );
                      }else if (selectedTab == 'Pantry' && pantryState.pantryLists.isNotEmpty) {
                        ref.read(pantryListItemProvider.notifier).fetchPantryListsItems(
                          id: pantryState.pantryLists[0].id,
                          search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
                        );
                      }
                    });

                    // Trigger search for the new tab if there's a search query
                    if (_currentSearchQuery.isNotEmpty) {
                      _performSearch(_currentSearchQuery);
                    }

                    // Trigger item search for the new tab if there's an item search query
                    if (_currentItemSearchQuery.isNotEmpty) {
                      _performItemSearch(_currentItemSearchQuery);
                    }
                  },
                ),
                SizedBox(height: 32.h),
                CustomSearchBar(
                  controller: searchController,
                  onChanged: _onSearchChanged,
                ),
                SizedBox(height: 10.h),
                Expanded(
                  child: _buildListContent(shoppingState, pantryState, currentList),
                ),
              ],
            ),
          ),

          // Right side: details view
          Expanded(
            flex: 6,
            child: Stack(
              fit: StackFit.expand,
              children: [
                Image.asset(
                  AssetsManager.background_img,
                  fit: BoxFit.cover,
                ),
                _buildRightContentArea(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build right content area with error handling outside the white container
  Widget _buildRightContentArea() {
    final shoppingState = ref.watch(shoppingListNotifierProvider);
    final pantryState = ref.watch(pantryListNotifierProvider);
    final screenSize = MediaQuery.of(context).size;

    // Check for error states first - show in center of screen exactly like "No shopping list found"
    if (selectedTab == 'Shopping list') {
      // Check shopping list errors first
      if (shoppingState.status == ShoppingListStatus.error) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64.sp,
                color: Colors.grey.shade400,
              ),
              SizedBox(height: 16.h),
              Text(
                shoppingState.errorMessage ?? 'Failed to load shopping lists',
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: 18.sp,
                ),
              ),
            ],
          ),
        );
      }

      // Check shopping item errors
      final itemState = ref.watch(shoppingListItemProvider);
      if (itemState.status == ShoppingListStatus.error) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64.sp,
                color: Colors.grey.shade400,
              ),
              SizedBox(height: 16.h),
              Text(
                itemState.errorMessage ?? 'Failed to load shopping items',
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: 18.sp,
                ),
              ),
            ],
          ),
        );
      }
    } else {
      // Check pantry list errors first
      if (pantryState.status == PantryListStatus.error) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64.sp,
                color: Colors.grey.shade400,
              ),
              SizedBox(height: 16.h),
              Text(
                pantryState.errorMessage ?? 'Failed to load pantry lists',
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: 18.sp,
                ),
              ),
            ],
          ),
        );
      }

      // Check pantry item errors
      final pantryItemState = ref.watch(pantryListItemProvider);
      if (pantryItemState.status == PantryListStatus.error) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64.sp,
                color: Colors.grey.shade400,
              ),
              SizedBox(height: 16.h),
              Text(
                pantryItemState.errorMessage ?? 'Failed to load pantry items',
                style: context.theme.textTheme.titleMedium?.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: 18.sp,
                ),
              ),
            ],
          ),
        );
      }
    }

    // Normal content layout
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 26.h),
        // Action buttons outside the container - show only when needed
        if (_shouldShowActionButtons()) _buildActionButtonsRow(),
        // White container with table
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: screenSize.width * 0.03,
            vertical: screenSize.width * 0.01,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: _buildMainContent(),
          ),
        ),
      ],
    );
  }

  // Build main content - shows table/footer or "No list found" message
  Widget _buildMainContent() {
    final shoppingState = ref.watch(shoppingListNotifierProvider);
    final pantryState = ref.watch(pantryListNotifierProvider);

    // Check if we're in loading state
    final isShoppingLoading = selectedTab == 'Shopping list' &&
        shoppingState.status == ShoppingListStatus.loading;
    final isPantryLoading = selectedTab == 'Pantry' &&
        pantryState.status == PantryListStatus.loading;

    // Show loading indicator during API calls
    if (isShoppingLoading || isPantryLoading) {
      return Center(
        child: LoadingAnimationWidget.fallingDot(
          color: Colors.black54,
          size: 50.0,
        ),
      );
    }

    // Error handling is done in _buildRightContentArea() to show on blurred background

    // Check if lists exist after API call completes
    final hasShoppingLists = selectedTab == 'Shopping list' &&
        shoppingState.status != ShoppingListStatus.loading &&
        shoppingState.shoppingLists.isNotEmpty;
    final hasPantryLists = selectedTab == 'Pantry' &&
        pantryState.status != PantryListStatus.loading &&
        pantryState.pantryLists.isNotEmpty;

    // Show "No item found" message if no lists exist after API call
    if (selectedTab == 'Shopping list' &&
        shoppingState.status != ShoppingListStatus.loading &&
        shoppingState.shoppingLists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64.sp,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: 16.h),
            Text(
              'No shopping item found',
              style: context.theme.textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
                fontSize: 18.sp,
              ),
            ),
          ],
        ),
      );
    }

    if (selectedTab == 'Pantry' &&
        pantryState.status != PantryListStatus.loading &&
        pantryState.pantryLists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.kitchen_outlined,
              size: 64.sp,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: 16.h),
            Text(
              'No pantry item found',
              style: context.theme.textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
                fontSize: 18.sp,
              ),
            ),
          ],
        ),
      );
    }

    // Show table and footer if lists exist
    if (hasShoppingLists || hasPantryLists) {
      final screenSize = MediaQuery.of(context).size;

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
         _buildTable(),
          _buildFooter(),

              ],
            );

    }

    // Fallback - should not reach here
    return const SizedBox.shrink();
  }

  Widget _buildTable() {
    if (selectedTab == 'Shopping list') {
      return _buildShoppingTable();
    } else {
      return _buildPantryTable();
    }
  }

  // Helper method to determine if action buttons should be shown
  bool _shouldShowActionButtons() {
    print('_shouldShowActionButtons called for tab: $selectedTab');

    // First check if lists exist - if no lists, never show action buttons
    if (selectedTab == 'Shopping list') {
      final shoppingListState = ref.read(shoppingListNotifierProvider);
      if (shoppingListState.shoppingLists.isEmpty) {
        print('Action buttons hidden: no shopping lists exist');
        return false;
      }
    } else if (selectedTab == 'Pantry') {
      final pantryListState = ref.read(pantryListNotifierProvider);
      if (pantryListState.pantryLists.isEmpty) {
        print('Action buttons hidden: no pantry lists exist');
        return false;
      }
    }

    // Check if there are actual items in the list first
    bool hasActualItems = false;
    if (selectedTab == 'Shopping list') {
      final shoppingState = ref.read(shoppingListItemProvider);
      hasActualItems = shoppingState.shoppingItems.isNotEmpty;
    } else if (selectedTab == 'Pantry') {
      final pantryState = ref.read(pantryListItemProvider);
      hasActualItems = pantryState.pantryItems.isNotEmpty;
    }

    // Show if new rows are added (even when there are no actual items in the list)
    if (selectedTab == 'Shopping list' && newShoppingRows.isNotEmpty) {
      print('Action buttons shown: new shopping rows added');
      return true;
    }

    if (selectedTab == 'Pantry' && newPantryRows.isNotEmpty) {
      print('Action buttons shown: new pantry rows added');
      return true;
    }

    // If list count is 0 (no actual items) and no new rows, hide action buttons
    if (!hasActualItems) {
      print('Action buttons hidden: no actual items in list and no new rows (count = 0)');
      return false;
    }

    // Show if at least any row is editable (user is editing any item)
    if (editableShoppingItems.isNotEmpty || editablePantryItems.isNotEmpty) {
      print('Action buttons shown: editable items exist');
      return true;
    }

    // Show if user clicks on any checkbox (any item is selected)
    if (selectedShoppingItemIds.isNotEmpty || selectedPantryItemIds.isNotEmpty) {
      print('Action buttons shown: items selected');
      return true;
    }

    // Hidden in all other cases
    print('Action buttons hidden: no conditions met');
    return false;
  }

  Widget _buildActionButtonsRow() {
    final screenSize = MediaQuery.of(context).size;
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: screenSize.width * 0.03,
        //vertical: 8.h,
      ),
      child: Row(
        children: [
          // Mark as Purchased button - only show for shopping items
          if (selectedTab == 'Shopping list') ...[
            GestureDetector(
              onTap: () {
                _markSelectedShoppingItemsAsPurchased();
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                decoration: BoxDecoration(
                // color: Colors.brown.shade800,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.local_offer, color: Colors.white, size: 20.sp),
                    SizedBox(width: 6.w),
                    Text(
                      'Mark as Purchased',
                      style: context.theme.textTheme.labelSmall!.copyWith(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(width: 12.w),
          ],
          // Delete button
          GestureDetector(
            onTap: () {
              if (selectedTab == 'Pantry') {
                _deleteSelectedPantryItems();
              } else if (selectedTab == 'Shopping list') {
                _deleteSelectedShoppingItems();
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                //color: Colors.red,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.delete_outline, color: Colors.white, size: 24.sp),
                  SizedBox(width: 6.w),
                  Text(
                    'Delete',
                    style: context.theme.textTheme.labelSmall!.copyWith(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Spacer(),
          // Save button - handles both new items and updates
          GestureDetector(
            onTap: () {
              if (selectedTab == 'Shopping list') {
                // For shopping items, handle both new items and updates
                if (modifiedShoppingItemIds.isNotEmpty) {
                  _updateShoppingItems();
                } else {
                  _saveShoppingItems();
                }
              } else {
                // For pantry items, handle both new items and updates
                if (modifiedPantryItemIds.isNotEmpty) {
                  _updatePantryItems();
                } else {
                  _savePantryItems();
                }
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                (selectedTab == 'Shopping list' && modifiedShoppingItemIds.isNotEmpty)
                  ? 'Update items (${modifiedShoppingItemIds.length})'
                  : (selectedTab == 'Pantry' && modifiedPantryItemIds.isNotEmpty)
                    ? 'Update items (${modifiedPantryItemIds.length})'
                    : ' Save ',
                style: context.theme.textTheme.labelSmall!.copyWith(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShoppingTable() {
    final itemState = ref.watch(shoppingListItemProvider);
    final shoppingState = ref.watch(shoppingListNotifierProvider);

    if (itemState.status == ShoppingListStatus.loading) {
      return Center(
        child: Center(
          child: LoadingAnimationWidget.fallingDot(
            color: Colors.black54,
            size: 50.0,
          ),
        ),
      );
    }

    // Error handling is now done in _buildMainContent()
    final items = itemState.shoppingItems;

    // Only add default row if shopping lists exist and no items found
    if (items.isEmpty && newShoppingRows.isEmpty && shoppingState.shoppingLists.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _addNewShoppingRow();
      });
    }

    // Calculate dynamic height based on item count
    final totalItems = items.length + newShoppingRows.length;
    final headerHeight = 50.0;
    final rowHeight = 60.0;
    final minHeight = headerHeight + (2 * rowHeight); // Minimum height for 2 rows
    final maxHeight = 400.0; // Maximum height before scrolling
    final calculatedHeight = headerHeight + (totalItems * rowHeight);
    final tableHeight = calculatedHeight.clamp(minHeight, maxHeight);

    return SizedBox(
      height: tableHeight,
      child: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: 30.h), // Add 30px bottom padding
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(1),
            1: FixedColumnWidth(50),
            2: FlexColumnWidth(3.2),
            3: FlexColumnWidth(1.5),
            4: FlexColumnWidth(2),
            5: FlexColumnWidth(2.5),
            6: FlexColumnWidth(2.5),
            7: FlexColumnWidth(1.5),
          },
          border: TableBorder.symmetric(
            inside: BorderSide(color: Colors.grey.shade300),
          ),
          children: [
            _buildShoppingHeaderRow(),
            ...List.generate(items.length, (index) {
              final item = items[index];
              return _buildShoppingDataRow(index + 1, item);
            }),
            ...List.generate(newShoppingRows.length, (index) {
              return _buildEditableShoppingRow(items.length + index + 1, newShoppingRows[index], index);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildPantryTable() {
    final pantryItemState = ref.watch(pantryListItemProvider);
    final pantryState = ref.watch(pantryListNotifierProvider);

    if (pantryItemState.status == PantryListStatus.loading) {
      return Center(
        child: Center(
          child: LoadingAnimationWidget.fallingDot(
            color: Colors.black54,
            size: 50.0,
          ),
        ),
      );
    }

    // Error handling is now done in _buildMainContent()
    final items = pantryItemState.pantryItems;

    // Only add default row if pantry lists exist and no items found
    if (items.isEmpty && newPantryRows.isEmpty && pantryState.pantryLists.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _addNewPantryRow();
      });
    }

    // Calculate dynamic height based on item count
    final totalItems = items.length + newPantryRows.length;
    final headerHeight = 50.0;
    final rowHeight = 60.0;
    final minHeight = headerHeight + (2 * rowHeight); // Minimum height for 2 rows
    final maxHeight = 400.0; // Maximum height before scrolling
    final calculatedHeight = headerHeight + (totalItems * rowHeight);
    final tableHeight = calculatedHeight.clamp(minHeight, maxHeight);

    return SizedBox(
      height:totalItems>=10? ScreenSizer().getVisibleScreenHeight(context)- 200:tableHeight, // Use the visible screen height minus 200px
      child: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: 30.h), // Add 30px bottom padding
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(1),
            1: FixedColumnWidth(50),
            2: FlexColumnWidth(3),
            3: FlexColumnWidth(1.4),
            4: FlexColumnWidth(1.4),
            5: FlexColumnWidth(1),
            6: FlexColumnWidth(1.5),
          },
          border: TableBorder.symmetric(
            inside: BorderSide(color: Colors.grey.shade300),
          ),
          children: [
            _buildPantryHeaderRow(),
            ...List.generate(items.length, (index) {
              final item = items[index];
              return _buildPantryDataRow(index + 1, item);
            }),
            ...List.generate(newPantryRows.length, (index) {
              return _buildEditablePantryRow(items.length + index + 1, newPantryRows[index], index);
            }),
          ],
        ),
      ),
    );
  }


  TableRow _buildShoppingDataRow(int count, ShoppingItem item) {
    final isEditable = editableShoppingItems.containsKey(item.id);

    return TableRow(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
        color: isEditable ? Colors.blue.shade50 : null, // Highlight editable rows
      ),
      children: [
        _buildTableCell(Center(child: _buildRowText("$count"))),
        _buildTableCell(
          Checkbox(
            value: selectedShoppingItemIds.contains(item.id),
            onChanged: (bool? value) {
              setState(() {
                if (value == true) {
                  selectedShoppingItemIds.add(item.id);
                } else {
                  selectedShoppingItemIds.remove(item.id);
                  isAllShoppingItemsSelected = false; // Uncheck "select all" if individual item is unchecked
                }

                // Check if all items are selected to update "select all" checkbox
                final shoppingItemState = ref.read(shoppingListItemProvider);
                if (selectedShoppingItemIds.length == shoppingItemState.shoppingItems.length) {
                  isAllShoppingItemsSelected = true;
                }
              });
            },
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1), // Red border
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2), // Adjust corner roundness
            ),
            checkColor: Colors.white,
          ),
        ),
        // Item name - clickable to make editable
        _buildTableCell(
          isEditable
            ? _buildItemAutocompleteField(
                editableShoppingItems[item.id]!['item']!,
                placeholder: "Item name",
              )
            : GestureDetector(
                onTap: () => _makeShoppingItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.item),
                ),
              ),
        ),
        // Amount - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildAmountTextField(
                editableShoppingItems[item.id]!['amount']!,
                placeholder: "Amount",
              )
            : GestureDetector(
                onTap: () => _makeShoppingItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.amount.toString()),
                ),
              ),
        ),
        // Unit - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildUnitAutocompleteField(
                editableShoppingItems[item.id]!['unit']!,
                placeholder: "Unit",
              )
            : GestureDetector(
                onTap: () => _makeShoppingItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.unit),
                ),
              ),
        ),
        // Store location - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildEditableTextField(
                editableShoppingItems[item.id]!['storeLocation']!,
                placeholder: "Store location",
              )
            : GestureDetector(
                onTap: () => _makeShoppingItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.storeLocation),
                ),
              ),
        ),
        // Recipe - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildRecipeTextField(
                editableShoppingItems[item.id]!['recipe']!,
                placeholder: "Recipe",
              )
            : GestureDetector(
                onTap: () => _makeShoppingItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.recipe),
                ),
              ),
        ),
        // Cost - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildEditableTextField(
                editableShoppingItems[item.id]!['cost']!,
                placeholder: "Cost",
              )
            : GestureDetector(
                onTap: () => _makeShoppingItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.cost.toString()),
                ),
              ),
        ),
      ],
    );
  }

  TableRow _buildShoppingHeaderRow() {
    return TableRow(
      decoration: BoxDecoration(color: Colors.grey.shade200 , borderRadius: BorderRadius.only(topLeft: Radius.circular(8) , topRight: Radius.circular(8) )),
      children: [
        _buildTableCell(_buildTitle("Count")),
        _buildTableCell(
          Checkbox(
            value: isAllShoppingItemsSelected,
            onChanged: (bool? value) {
              setState(() {
                isAllShoppingItemsSelected = value ?? false;
                if (isAllShoppingItemsSelected) {
                  // Select all shopping items
                  final shoppingItemState = ref.read(shoppingListItemProvider);
                  selectedShoppingItemIds = shoppingItemState.shoppingItems.map((item) => item.id).toSet();
                } else {
                  // Deselect all
                  selectedShoppingItemIds.clear();
                }
              });
            },
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1), // Red border
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2), // Adjust corner roundness
            ),
            checkColor: Colors.white,
          ),
        ),
        _buildTableCell(_buildTitle("Item")),
        _buildTableCell(_buildTitle("Amount")),
        _buildTableCell(_buildTitle("Unit")),
        _buildTableCell(_buildTitle("Store location")),
        _buildTableCell(_buildTitle("Recipe")),
        _buildTableCell(_buildTitle("Cost")),

      ],
    );
  }

  TableRow _buildPantryDataRow(int count, PantryItem item) {
    final isEditable = editablePantryItems.containsKey(item.id);

    return TableRow(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
        color: isEditable ? Colors.blue.shade50 : null, // Highlight editable rows
      ),
      children: [
        _buildTableCell(Center(child: _buildRowText("$count"))),
        _buildTableCell(
          Checkbox(
            value: selectedPantryItemIds.contains(item.id),
            onChanged: (bool? value) {
              setState(() {
                if (value == true) {
                  selectedPantryItemIds.add(item.id);
                } else {
                  selectedPantryItemIds.remove(item.id);
                  isAllPantryItemsSelected = false; // Uncheck "select all" if individual item is unchecked
                }

                // Check if all items are selected to update "select all" checkbox
                final pantryItemState = ref.read(pantryListItemProvider);
                if (selectedPantryItemIds.length == pantryItemState.pantryItems.length) {
                  isAllPantryItemsSelected = true;
                }
              });
            },
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1), // Red border
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2), // Adjust corner roundness
            ),
            checkColor: Colors.white,
          ),
        ),
        // Item name - clickable to make editable
        _buildTableCell(
          isEditable
            ? _buildItemAutocompleteField(
                editablePantryItems[item.id]!['item']!,
                placeholder: "Item name",
              )
            : GestureDetector(
                onTap: () => _makePantryItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.item),
                ),
              ),
        ),
        // Purchased date - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildDatePickerField(
                editablePantryItems[item.id]!['purchasedDate']!,
                placeholder: "MM/DD/YYYY",
              )
            : GestureDetector(
                onTap: () => _makePantryItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(DateFormat('MM/dd/yyyy').format(item.purchasedDate)),
                ),
              ),
        ),
        // Use by date - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildDatePickerField(
                editablePantryItems[item.id]!['useByDate']!,
                placeholder: "MM/DD/YYYY",
              )
            : GestureDetector(
                onTap: () => _makePantryItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(DateFormat('MM/dd/yyyy').format(item.useByDate)),
                ),
              ),
        ),
        // Amount - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildAmountTextField(
                editablePantryItems[item.id]!['amount']!,
                placeholder: "Amount",
              )
            : GestureDetector(
                onTap: () => _makePantryItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.amount.toString()),
                ),
              ),
        ),
        // Unit - editable when in edit mode
        _buildTableCell(
          isEditable
            ? _buildUnitAutocompleteField(
                editablePantryItems[item.id]!['unit']!,
                placeholder: "Unit",
              )
            : GestureDetector(
                onTap: () => _makePantryItemEditable(item),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: _buildRowText(item.unit),
                ),
              ),
        ),
      ],
    );
  }

  TableRow _buildPantryHeaderRow() {
    return TableRow(
      decoration: BoxDecoration(color: Colors.grey.shade200 , borderRadius: BorderRadius.only(topLeft: Radius.circular(8) , topRight: Radius.circular(8) )),
      children: [
        _buildTableCell(_buildTitle("Count")),
        _buildTableCell(
          Checkbox(
            value: isAllPantryItemsSelected,
            onChanged: (bool? value) {
              setState(() {
                isAllPantryItemsSelected = value ?? false;
                if (isAllPantryItemsSelected) {
                  // Select all pantry items
                  final pantryItemState = ref.read(pantryListItemProvider);
                  selectedPantryItemIds = pantryItemState.pantryItems.map((item) => item.id).toSet();
                } else {
                  // Deselect all
                  selectedPantryItemIds.clear();
                }
              });
            },
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1), // Red border
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2), // Adjust corner roundness
            ),
            checkColor: Colors.white,
          ),
        ),
        _buildTableCell(_buildTitle("Item")),
        _buildTableCell(_buildTitle("Purchase Date")),
        _buildTableCell(_buildTitle("Use By Date")),
        _buildTableCell(_buildTitle("Amount")),
        _buildTableCell(_buildTitle("Unit")),
      ],
    );
  }

  // Method to add new shopping row
  void _addNewShoppingRow() {
    setState(() {
      final controllers = {
        'item': TextEditingController(),
        'amount': TextEditingController(),
        'unit': TextEditingController(),
        'storeLocation': TextEditingController(),
        'recipe': TextEditingController(),
        'cost': TextEditingController(),
      };

      // Add listeners to trigger rebuild when user types
      for (var controller in controllers.values) {
        controller.addListener(() {
          print('Shopping row text changed, triggering rebuild...');
          setState(() {}); // Trigger rebuild to update action buttons
        });
      }

      newShoppingRows.add(controllers);
    });
  }

  // Method to add new pantry row
  void _addNewPantryRow() {
    setState(() {
      final controllers = {
        'item': TextEditingController(),
        'purchaseDate': TextEditingController(),
        'useByDate': TextEditingController(),
        'amount': TextEditingController(),
        'unit': TextEditingController(),
      };

      // Add listeners to trigger rebuild when user types
      for (var controller in controllers.values) {
        controller.addListener(() {
          setState(() {}); // Trigger rebuild to update action buttons
        });
      }

      newPantryRows.add(controllers);
    });
  }

  // Build editable shopping row
  TableRow _buildEditableShoppingRow(int count, Map<String, TextEditingController> controllers, int rowIndex) {
    return TableRow(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      children: [
        _buildTableCell(Center(child: _buildRowText("$count"))),
        _buildTableCell(
          Checkbox(
            value: false,
            onChanged: (_){},
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2),
            ),
            checkColor: Colors.white,
          ),
        ),
        _buildTableCell(_buildItemAutocompleteField(controllers['item']!, placeholder: "Item name")),
        _buildTableCell(_buildAmountTextField(controllers['amount']!, placeholder: "Amount")),
        _buildTableCell(_buildUnitAutocompleteField(controllers['unit']!, placeholder: "Unit")),
        _buildTableCell(_buildEditableTextField(controllers['storeLocation']!, placeholder: "Store location")),
        _buildTableCell(_buildRecipeTextField(controllers['recipe']!, placeholder: "Recipe")),
        _buildTableCell(_buildEditableTextField(controllers['cost']!, placeholder: "Cost")),
      ],
    );
  }

  // Build editable pantry row
  TableRow _buildEditablePantryRow(int count, Map<String, TextEditingController> controllers, int rowIndex) {
    return TableRow(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      children: [
        _buildTableCell(Center(child: _buildRowText("$count"))),
        _buildTableCell(
          Checkbox(
            value: false,
            onChanged: (_){},
            activeColor: Colors.red,
            side: const BorderSide(color: Colors.red, width: 1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2),
            ),
            checkColor: Colors.white,
          ),
        ),
        _buildTableCell(_buildItemAutocompleteField(controllers['item']!, placeholder: "Item name")),
        _buildTableCell(_buildDatePickerField(controllers['purchaseDate']!, placeholder: "MM/DD/YYYY")),
        _buildTableCell(_buildDatePickerField(controllers['useByDate']!, placeholder: "MM/DD/YYYY")),
        _buildTableCell(_buildAmountTextField(controllers['amount']!, placeholder: "Amount")),
        _buildTableCell(_buildUnitAutocompleteField(controllers['unit']!, placeholder: "Unit")),
      ],
    );
  }

  // Build editable text field
  Widget _buildEditableTextField(TextEditingController controller, {String? placeholder}) {
    return Padding(
      padding: EdgeInsets.all(6.w),
      child: SizedBox(
        height: 48.h, // Fixed height for consistency
        child: TextField(
          controller: controller,
          style: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.primaryGreyColor,
          ),
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: context.theme.textTheme.labelSmall!.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey.shade400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            isDense: true,
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ),
    );
  }

  // Build amount text field with 14 digit limit
  Widget _buildAmountTextField(TextEditingController controller, {String? placeholder}) {
    return Padding(
      padding: EdgeInsets.all(6.w),
      child: SizedBox(
        height: 48.h, // Fixed height for consistency
        child: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          maxLength: 14, // Maximum 14 digits
          style: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.primaryGreyColor,
          ),
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: context.theme.textTheme.labelSmall!.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey.shade400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            isDense: true,
            filled: true,
            fillColor: Colors.white,
            counterText: "", // Hide the character counter
          ),
        ),
      ),
    );
  }

  // Build recipe text field with 50 character limit
  Widget _buildRecipeTextField(TextEditingController controller, {String? placeholder}) {
    return Padding(
      padding: EdgeInsets.all(6.w),
      child: SizedBox(
        height: 48.h, // Fixed height for consistency
        child: TextField(
          controller: controller,
          maxLength: 50, // Maximum 50 characters for recipe
          style: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.primaryGreyColor,
          ),
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: context.theme.textTheme.labelSmall!.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey.shade400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            isDense: true,
            filled: true,
            fillColor: Colors.white,
            counterText: "", // Hide the character counter
          ),
        ),
      ),
    );
  }

  // Build item autocomplete field with search functionality
  Widget _buildItemAutocompleteField(TextEditingController controller, {String? placeholder}) {
    return Padding(
      padding: EdgeInsets.all(6.w),
      child: SizedBox(
        height: 48.h, // Fixed height for consistency
        child: CustomAutocomplete<String>(
          controller: controller,
          suggestionsHeight: 350.0, // Further increased height for better visibility
          dropdownWidthMultiplier: 2, // Wide dropdown for item names
          fetchSuggestions: (query) async {
            // Comment out searchItems API code - using MasterIngredients.json instead
            // final searchNotifier = ref.read(searchItemsNotifierProvider.notifier);
            // await searchNotifier.searchItems(query);
            // final searchState = ref.read(searchItemsNotifierProvider);
            // return searchState.data ?? [];

            // Use MasterIngredients.json for local search
            return await _searchMasterIngredients(query);
          },
          itemBuilder: (context, item) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.08),
                    spreadRadius: 0.5,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
                border: Border.all(color: Colors.grey.shade100, width: 0.5),
              ),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 10.h),
                child: Row(
                  children: [
                    // Bubble pin indicator
                    Container(
                      width: 6.w,
                      height: 6.h,
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    // Item text
                    Expanded(
                      child: Text(
                        item,
                        style: context.theme.textTheme.labelSmall!.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.primaryGreyColor,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
          itemToString: (item) => item,
          onSelected: (item) {
            controller.text = item;
          },
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: context.theme.textTheme.labelSmall!.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey.shade400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            isDense: true,
            filled: true,
            fillColor: Colors.white,
          ),
          // Custom text style for typing
          style: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.primaryGreyColor, // Same color as other text fields
          ),
        ),
      ),
    );
  }

  // Build unit autocomplete field with instruction words
  Widget _buildUnitAutocompleteField(TextEditingController controller, {String? placeholder}) {
    return Padding(
      padding: EdgeInsets.all(6.w),
      child: SizedBox(
        height: 48.h, // Fixed height for consistency
        child: CustomAutocomplete<String>(
          controller: controller,
          suggestionsHeight: 350.0, // Height for better visibility
          dropdownWidthMultiplier: 1.0, // Narrow dropdown for unit names
          fetchSuggestions: (query) async {
            // Get instruction words from user profile
            final userProfileNotifier = ref.read(userProfileNotifierProvider.notifier);

            // Get instruction words from the user profile data
            List<String> instructionWords = userProfileNotifier.instructionWords;

            // If no instruction words from API, use fallback units
            if (instructionWords.isEmpty) {
              instructionWords = [
                'kg', 'g', 'lb', 'oz', 'cup', 'cups', 'tbsp', 'tsp', 'ml', 'l', 'liter', 'liters',
                'piece', 'pieces', 'slice', 'slices', 'can', 'cans', 'bottle', 'bottles',
                'pack', 'packs', 'box', 'boxes', 'bag', 'bags', 'jar', 'jars'
              ];
            }

            // Filter instruction words based on query
            if (query.trim().isEmpty) {
              return instructionWords;
            } else {
              return instructionWords
                  .where((word) => word.toLowerCase().contains(query.toLowerCase()))
                  .toList();
            }
          },
          itemBuilder: (context, item) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.08),
                    spreadRadius: 0.5,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
                border: Border.all(color: Colors.grey.shade100, width: 0.5),
              ),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 10.h),
                child: Row(
                  children: [
                    // Bubble pin indicator
                    Container(
                      width: 6.w,
                      height: 6.h,
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    // Unit text
                    Expanded(
                      child: Text(
                        item,
                        style: context.theme.textTheme.labelSmall!.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.primaryGreyColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
          itemToString: (item) => item,
          onSelected: (item) {
            controller.text = item;
          },
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: context.theme.textTheme.labelSmall!.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey.shade400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            isDense: true,
            filled: true,
            fillColor: Colors.white,
          ),
          // Custom text style for typing
          style: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.primaryGreyColor, // Same color as other text fields
          ),
        ),
      ),
    );
  }

  // Build date picker field
  Widget _buildDatePickerField(TextEditingController controller, {String? placeholder}) {
    return Padding(
      padding: EdgeInsets.all(6.w),
      child: SizedBox(
        height: 48.h, // Fixed height for consistency
        child: TextField(
          controller: controller,
          readOnly: true, // Make it read-only so users can't type
          onTap: () async {
            // Show date picker when tapped
            final DateTime? pickedDate = await showDatePicker(
              context: context,
              initialDate: DateTime.now(),
              firstDate: DateTime(2000),
              lastDate: DateTime(2100),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ColorScheme.light(
                      primary: AppColors.primaryColor,
                      onPrimary: Colors.white,
                      surface: Colors.white,
                      onSurface: Colors.black,
                    ),
                  ),
                  child: child!,
                );
              },
            );

            if (pickedDate != null) {
              // Format the date as MM/DD/YYYY
              final formattedDate = "${pickedDate.month.toString().padLeft(2, '0')}/${pickedDate.day.toString().padLeft(2, '0')}/${pickedDate.year}";
              controller.text = formattedDate;
            }
          },
          style: context.theme.textTheme.labelSmall!.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.primaryGreyColor,
          ),
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: context.theme.textTheme.labelSmall!.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey.shade400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: AppColors.primaryColor, width: 1),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            isDense: true,
            filled: true,
            fillColor: Colors.white,
            suffixIcon: Icon(
              Icons.calendar_today,
              color: AppColors.primaryColor,
              size: 20.sp,
            ),
          ),
        ),
      ),
    );
  }





  Widget _buildTitle(String value){
    return Padding(
      padding: EdgeInsets.only(left: 3.w, top: 10.h),
      child: Text(value , style: context.theme.textTheme.labelSmall!.copyWith(fontSize: 23.sp , fontWeight: FontWeight.w400)),
    );

  }

  Widget _buildRowText(String value){
    return Padding(
      padding: EdgeInsets.only(left: 3.w, top: 10.h),
      child: Text(value , style: context.theme.textTheme.labelSmall!.copyWith(fontSize: 22.sp,
          fontWeight: FontWeight.w400 , color: AppColors.primaryGreyColor)),
    );

  }

  Widget _buildTableCell(Widget child) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border(right: BorderSide(color: Colors.grey.shade300)),
      ),
      child: child,
    );
  }





  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightestGreyColor , width: 1),
                borderRadius: BorderRadius.all(Radius.circular(10.sp))
            ),
            child: CustomButton(
              width: 230.w,
              text: "Add more rows",
              fontSize: 20.sp,
              color: Colors.white, textColor: AppColors.primaryLightTextColor,
              onPressed: () {
                if (selectedTab == 'Shopping list') {
                  _addNewShoppingRow();
                } else {
                  _addNewPantryRow();
                }
              },
            ),
          ),
          // Only show total bill for shopping items, not pantry
          if (selectedTab == 'Shopping list')
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: AppColors.lightestGreyColor),
                color: Colors.white,
              ),
              child: Consumer(
                builder: (context, ref, child) {
                  final itemState = ref.watch(shoppingListItemProvider);
                  final totalCost = itemState.totalCost;

                  return Row(
                    children: [
                      Text(
                        '\$${totalCost.toStringAsFixed(2)} ',
                        style: context.theme.textTheme.displaySmall!.copyWith(
                            color: AppColors.primaryLightTextColor,
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w400
                        ),
                      ),
                      Text(
                        '(Total bill)',
                        style: context.theme.textTheme.displaySmall!.copyWith(
                            color: AppColors.textGreyColor,
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w400
                        ),
                      ),
                    ],
                  );
                },
              ),
            )
        ],
      ),
    );
  }

  // Method to save shopping items
  Future<void> _saveShoppingItems() async {
    if (selectedTab != 'Shopping list') {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select a shopping list to save items', isError: true);
      }
      return;
    }

    final shoppingState = ref.read(shoppingListNotifierProvider);
    if (shoppingState.shoppingLists.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No shopping list available', isError: true);
      }
      return;
    }

    if (newShoppingRows.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No new items to save', isError: true);
      }
      return;
    }

    // Get the selected shopping list ID
    final selectedShoppingListId = shoppingState.shoppingLists[_selectedIndex].id;

    // Convert new rows to ShoppingItem objects
    List<shopping_request.ShoppingItem> shoppingItems = [];

    for (var row in newShoppingRows) {
      final item = row['item']?.text.trim() ?? '';
      final amountText = row['amount']?.text.trim() ?? '0';
      final unit = row['unit']?.text.trim() ?? '';
      final storeLocation = row['storeLocation']?.text.trim() ?? '';
      final recipe = row['recipe']?.text.trim() ?? '';
      final costText = row['cost']?.text.trim() ?? '0';

      // Skip empty rows
      if (item.isEmpty) continue;

      final amount = int.tryParse(amountText) ?? 0;
      final cost = double.tryParse(costText) ?? 0.0;

      shoppingItems.add(shopping_request.ShoppingItem(
        item: item,
        amount: amount,
        unit: unit,
        storeLocation: storeLocation,
        recipe: recipe,
        cost: cost,
      ));
    }

    if (shoppingItems.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please fill in at least one item', isError: true);
      }
      return;
    }

    // Create the request
    final shoppingItemRequest = shopping_request.ShoppingItemRequest(
      shoppingItems: shoppingItems,
    );

    try {
      // Show loading
      if (mounted) {
        Utils().showFlushbar(context, message: 'Saving items...', isError: false);
      }

      // Call the API
      final (success, errorMessage) = await ref
          .read(shoppingListItemProvider.notifier)
          .addShoppingItems(
            shoppingListId: selectedShoppingListId,
            request: shoppingItemRequest,
          );

      if (success) {
        // Clear the new rows after successful save
        setState(() {
          newShoppingRows.clear();
        });

        // Refresh the shopping list items to get real database IDs
        final shoppingState = ref.read(shoppingListNotifierProvider);
        if (shoppingState.shoppingLists.isNotEmpty && _selectedIndex < shoppingState.shoppingLists.length) {
          final selectedShoppingListId = shoppingState.shoppingLists[_selectedIndex].id;
          ref.read(shoppingListItemProvider.notifier).fetchShoppingListsItems(
            id: selectedShoppingListId,
            search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
          );
        }

        if (mounted) {
          Utils().showFlushbar(context, message: 'Shopping items saved successfully!', isError: false);
        }
      } else {
        if (mounted) {
          Utils().showFlushbar(context, message: 'Failed to save items: ${errorMessage ?? 'Unknown error'}', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Error saving items: $e', isError: true);
      }
    }
  }

  // Method to save pantry items
  Future<void> _savePantryItems() async {
    if (selectedTab != 'Pantry') {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select a pantry to save items', isError: true);
      }
      return;
    }

    final pantryState = ref.read(pantryListNotifierProvider);
    if (pantryState.pantryLists.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No pantry available', isError: true);
      }
      return;
    }

    if (newPantryRows.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No new items to save', isError: true);
      }
      return;
    }

    // Get the selected pantry list ID
    final selectedPantryListId = pantryState.pantryLists[_selectedIndex].id;

    // Convert new rows to PantryItem objects
    List<pantry_request.PantryItem> pantryItems = [];

    for (var row in newPantryRows) {
      final item = row['item']?.text.trim() ?? '';
      final amountText = row['amount']?.text.trim() ?? '0';
      final unit = row['unit']?.text.trim() ?? '';
      final purchasedDateText = row['purchasedDate']?.text.trim() ?? '';
      final useByDateText = row['useByDate']?.text.trim() ?? '';

      // Skip empty rows
      if (item.isEmpty) continue;

      final amount = int.tryParse(amountText) ?? 0;

      // Parse dates (format MM/DD/YYYY from date picker)
      DateTime? purchasedDate;
      DateTime? useByDate;

      try {
        if (purchasedDateText.isNotEmpty) {
          // Parse MM/DD/YYYY format
          final parts = purchasedDateText.split('/');
          if (parts.length == 3) {
            final month = int.parse(parts[0]);
            final day = int.parse(parts[1]);
            final year = int.parse(parts[2]);
            purchasedDate = DateTime(year, month, day);
          } else {
            purchasedDate = DateTime.now();
          }
        } else {
          purchasedDate = DateTime.now();
        }
      } catch (e) {
        purchasedDate = DateTime.now();
      }

      try {
        if (useByDateText.isNotEmpty) {
          // Parse MM/DD/YYYY format
          final parts = useByDateText.split('/');
          if (parts.length == 3) {
            final month = int.parse(parts[0]);
            final day = int.parse(parts[1]);
            final year = int.parse(parts[2]);
            useByDate = DateTime(year, month, day);
          } else {
            useByDate = DateTime.now().add(const Duration(days: 7));
          }
        } else {
          useByDate = DateTime.now().add(const Duration(days: 7)); // Default to 7 days from now
        }
      } catch (e) {
        useByDate = DateTime.now().add(const Duration(days: 7));
      }

      pantryItems.add(pantry_request.PantryItem(
        item: item,
        amount: amount,
        unit: unit,
        purchasedDate: purchasedDate,
        useByDate: useByDate,
      ));
    }

    if (pantryItems.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please fill in at least one item', isError: true);
      }
      return;
    }

    // Create the request
    final pantryItemRequest = pantry_request.PantryItemsRequest(
      pantryItems: pantryItems,
    );

    try {
      // Show loading
      if (mounted) {
        Utils().showFlushbar(context, message: 'Saving items...', isError: false);
      }

      // Call the API
      final (success, errorMessage) = await ref
          .read(pantryListItemProvider.notifier)
          .addPantryItems(
            pantryListId: selectedPantryListId,
            request: pantryItemRequest,
          );

      if (success) {
        // Clear the new rows after successful save
        setState(() {
          newPantryRows.clear();
        });

        // Refresh the pantry list items to get real database IDs
        final pantryState = ref.read(pantryListNotifierProvider);
        if (pantryState.pantryLists.isNotEmpty && _selectedIndex < pantryState.pantryLists.length) {
          final selectedPantryListId = pantryState.pantryLists[_selectedIndex].id;
          ref.read(pantryListItemProvider.notifier).fetchPantryListsItems(
            id: selectedPantryListId,
            search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
          );
        }

        if (mounted) {
          Utils().showFlushbar(context, message: 'Pantry items saved successfully!', isError: false);
        }
      } else {
        if (mounted) {
          Utils().showFlushbar(context, message: 'Failed to save items: ${errorMessage ?? 'Unknown error'}', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Error saving items: $e', isError: true);
      }
    }
  }

  // Method to delete selected pantry items
  Future<void> _deleteSelectedPantryItems() async {
    if (selectedTab != 'Pantry') {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select pantry tab to delete items', isError: true);
      }
      return;
    }

    if (selectedPantryItemIds.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select items to delete', isError: true);
      }
      return;
    }

    // Determine the confirmation message based on selection
    final pantryItemState = ref.read(pantryListItemProvider);
    final allPantryItems = pantryItemState.pantryItems;
    final isAllSelected = isAllPantryItemsSelected || (allPantryItems.isNotEmpty && selectedPantryItemIds.length == allPantryItems.length);

    String confirmationMessage;
    if (isAllSelected) {
      confirmationMessage = 'Are you sure you want to delete ALL pantry items?';
    } else if (selectedPantryItemIds.length == 1) {
      confirmationMessage = 'Are you sure you want to delete this pantry item?';
    } else {
      confirmationMessage = 'Are you sure you want to delete ${selectedPantryItemIds.length} pantry items?';
    }

    // Show confirmation dialog
    final bool? confirmed = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Delete Pantry Items',
      subtitle: confirmationMessage,
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );

    if (confirmed != true) {
      return; // User cancelled deletion
    }

    final pantryState = ref.read(pantryListNotifierProvider);
    if (pantryState.pantryLists.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No pantry lists available', isError: true);
      }
      return;
    }

    // Get the selected pantry list ID
    final selectedPantryListId = pantryState.pantryLists[_selectedIndex].id;

    // Determine the type based on selection
    String deleteType;
    if (isAllPantryItemsSelected || (allPantryItems.isNotEmpty && selectedPantryItemIds.length == allPantryItems.length)) {
      deleteType = "All";
    } else {
      deleteType = "CUSTOM";
    }

    // Create the delete request
    final deleteRequest = pantry_request.DeletePantryItemsRequest(
      type: deleteType,
      pantryItemIds: deleteType == "All" ? null : selectedPantryItemIds.toList(),
    );

    try {
      // Show loading
      if (mounted) {
        Utils().showFlushbar(context, message: 'Deleting items...', isError: false);
      }

      // Call the delete API
      final (success, errorMessage) = await ref
          .read(pantryNotifierProvider.notifier)
          .deletePantryItems(
            pantryListId: selectedPantryListId,
            request: deleteRequest,
          );

      if (success) {
        // Update local state instead of refetching
        if (deleteType == "All") {
          ref.read(pantryListItemProvider.notifier).clearLocalPantryItems();
        } else {
          ref.read(pantryListItemProvider.notifier).removeLocalPantryItems(selectedPantryItemIds.toList());
        }

        // Clear selections after successful delete
        setState(() {
          selectedPantryItemIds.clear();
          isAllPantryItemsSelected = false;
        });

        if (mounted) {
          Utils().showFlushbar(context, message: 'Items deleted successfully!', isError: false);
        }
      } else {
        if (mounted) {
          Utils().showFlushbar(context, message: 'Failed to delete items: ${errorMessage ?? 'Unknown error'}', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Error deleting items: $e', isError: true);
      }
    }
  }

  // Method to delete selected shopping items
  Future<void> _deleteSelectedShoppingItems() async {
    if (selectedTab != 'Shopping list') {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select shopping list tab to delete items', isError: true);
      }
      return;
    }

    if (selectedShoppingItemIds.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select items to delete', isError: true);
      }
      return;
    }

    // Determine the confirmation message based on selection
    final shoppingItemState = ref.read(shoppingListItemProvider);
    final allShoppingItems = shoppingItemState.shoppingItems;
    final isAllSelected = isAllShoppingItemsSelected || (allShoppingItems.isNotEmpty && selectedShoppingItemIds.length == allShoppingItems.length);

    String confirmationMessage;
    if (isAllSelected) {
      confirmationMessage = 'Are you sure you want to delete ALL shopping items?';
    } else if (selectedShoppingItemIds.length == 1) {
      confirmationMessage = 'Are you sure you want to delete this shopping item?';
    } else {
      confirmationMessage = 'Are you sure you want to delete ${selectedShoppingItemIds.length} shopping items?';
    }

    // Show confirmation dialog
    final bool? confirmed = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Delete Shopping Items',
      subtitle: confirmationMessage,
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );

    if (confirmed != true) {
      return; // User cancelled
    }

    // Get the shopping state to access shopping lists
    final shoppingState = ref.read(shoppingListNotifierProvider);
    if (shoppingState.shoppingLists.isEmpty || _selectedIndex >= shoppingState.shoppingLists.length) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No shopping list selected', isError: true);
      }
      return;
    }

    // Get the selected shopping list ID
    final selectedShoppingListId = shoppingState.shoppingLists[_selectedIndex].id;

    // Determine the type based on selection
    String deleteType;
    if (isAllShoppingItemsSelected || (allShoppingItems.isNotEmpty && selectedShoppingItemIds.length == allShoppingItems.length)) {
      deleteType = "All";
    } else {
      deleteType = "CUSTOM";
    }

    // Create the delete request
    final deleteRequest = shopping_request.DeleteShoppingItemsRequest(
      type: deleteType,
      shoppingItemIds: deleteType == "All" ? null : selectedShoppingItemIds.toList(),
      status: "DELETE",
    );

    try {
      // Call the API using shopping list item notifier
      final (success, errorMessage) = await ref
          .read(shoppingListItemProvider.notifier)
          .deleteShoppingItems(
            shoppingListId: selectedShoppingListId,
            request: deleteRequest,
          );

      if (success) {
        // Clear selections after successful delete (local state already updated in notifier)
        setState(() {
          selectedShoppingItemIds.clear();
          isAllShoppingItemsSelected = false;
        });

        if (mounted) {
          Utils().showFlushbar(context, message: 'Items deleted successfully!', isError: false);
        }
      } else {
        if (mounted) {
          Utils().showFlushbar(context, message: 'Failed to delete items: ${errorMessage ?? 'Unknown error'}', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Error deleting items: $e', isError: true);
      }
    }
  }

  // Method to mark selected shopping items as purchased
  Future<void> _markSelectedShoppingItemsAsPurchased() async {
    if (selectedTab != 'Shopping list') {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select shopping list tab to mark items as purchased', isError: true);
      }
      return;
    }

    if (selectedShoppingItemIds.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select items to mark as purchased', isError: true);
      }
      return;
    }

    // Determine the type based on selection
    final shoppingItemState = ref.read(shoppingListItemProvider);
    final allShoppingItems = shoppingItemState.shoppingItems;
    final isAllSelected = isAllShoppingItemsSelected || (allShoppingItems.isNotEmpty && selectedShoppingItemIds.length == allShoppingItems.length);

    // Get the shopping state to access shopping lists
    final shoppingState = ref.read(shoppingListNotifierProvider);
    if (shoppingState.shoppingLists.isEmpty || _selectedIndex >= shoppingState.shoppingLists.length) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No shopping list selected', isError: true);
      }
      return;
    }

    // Get the selected shopping list ID
    final selectedShoppingListId = shoppingState.shoppingLists[_selectedIndex].id;

    // Determine the type based on selection
    String purchaseType;
    if (isAllSelected) {
      purchaseType = "All";
    } else {
      purchaseType = "CUSTOM";
    }

    // Create the purchase request
    final purchaseRequest = shopping_request.DeleteShoppingItemsRequest(
      type: purchaseType,
      shoppingItemIds: purchaseType == "All" ? null : selectedShoppingItemIds.toList(),
      status: "PURCHASE",
    );

    try {
      // Call the API using shopping list item notifier
      final (success, errorMessage) = await ref
          .read(shoppingListItemProvider.notifier)
          .deleteShoppingItems(
            shoppingListId: selectedShoppingListId,
            request: purchaseRequest,
          );

      if (success) {
        // Clear selections after successful purchase (local state already updated in notifier)
        setState(() {
          selectedShoppingItemIds.clear();
          isAllShoppingItemsSelected = false;
        });

        if (mounted) {
          Utils().showFlushbar(context, message: 'Items marked as purchased successfully!', isError: false);
        }
      } else {
        if (mounted) {
          Utils().showFlushbar(context, message: 'Failed to mark items as purchased: ${errorMessage ?? 'Unknown error'}', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Error marking items as purchased: $e', isError: true);
      }
    }
  }

  // Method to make a pantry item editable
  void _makePantryItemEditable(PantryItem item) {
    setState(() {
      if (!editablePantryItems.containsKey(item.id)) {
        // Format dates to MM/DD/YYYY for display
        final purchasedDateStr = "${item.purchasedDate.month.toString().padLeft(2, '0')}/${item.purchasedDate.day.toString().padLeft(2, '0')}/${item.purchasedDate.year}";
        final useByDateStr = "${item.useByDate.month.toString().padLeft(2, '0')}/${item.useByDate.day.toString().padLeft(2, '0')}/${item.useByDate.year}";

        // Create controllers with change listeners
        final itemController = TextEditingController(text: item.item);
        final amountController = TextEditingController(text: item.amount.toString());
        final unitController = TextEditingController(text: item.unit);
        final purchasedDateController = TextEditingController(text: purchasedDateStr);
        final useByDateController = TextEditingController(text: useByDateStr);

        // Add listeners to track changes
        itemController.addListener(() => _markPantryItemAsModified(item.id));
        amountController.addListener(() => _markPantryItemAsModified(item.id));
        unitController.addListener(() => _markPantryItemAsModified(item.id));
        purchasedDateController.addListener(() => _markPantryItemAsModified(item.id));
        useByDateController.addListener(() => _markPantryItemAsModified(item.id));

        editablePantryItems[item.id] = {
          'item': itemController,
          'amount': amountController,
          'unit': unitController,
          'purchasedDate': purchasedDateController,
          'useByDate': useByDateController,
        };
      }
    });
  }

  // Method to track modifications
  void _markPantryItemAsModified(int itemId) {
    setState(() {
      modifiedPantryItemIds.add(itemId);
    });
  }

  // Method to cancel editing for a specific item
  void _cancelEditingPantryItem(int itemId) {
    setState(() {
      if (editablePantryItems.containsKey(itemId)) {
        // Dispose controllers
        editablePantryItems[itemId]?.forEach((key, controller) {
          controller.dispose();
        });
        // Remove from editable items
        editablePantryItems.remove(itemId);
        modifiedPantryItemIds.remove(itemId);
      }
    });
  }

  // Method to cancel all editing
  void _cancelAllEditing() {
    setState(() {
      // Dispose all controllers
      for (var controllers in editablePantryItems.values) {
        controllers.forEach((key, controller) {
          controller.dispose();
        });
      }
      // Clear all editable items and modifications
      editablePantryItems.clear();
      modifiedPantryItemIds.clear();
    });
  }

  // Method to update existing pantry items
  Future<void> _updatePantryItems() async {
    if (selectedTab != 'Pantry') {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select a pantry to update items', isError: true);
      }
      return;
    }

    final pantryState = ref.read(pantryListNotifierProvider);
    if (pantryState.pantryLists.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No pantry available', isError: true);
      }
      return;
    }

    if (modifiedPantryItemIds.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No items to update', isError: true);
      }
      return;
    }

    // Get the selected pantry list ID
    final selectedPantryListId = pantryState.pantryLists[_selectedIndex].id;

    // Prepare pantry items for update
    List<pantry_request.PantryItem> pantryItems = [];

    for (int itemId in modifiedPantryItemIds) {
      final controllers = editablePantryItems[itemId];
      if (controllers != null) {
        final itemName = controllers['item']?.text.trim();
        final amountText = controllers['amount']?.text.trim();
        final unit = controllers['unit']?.text.trim();
        final purchasedDateText = controllers['purchasedDate']?.text.trim();
        final useByDateText = controllers['useByDate']?.text.trim();

        if (itemName?.isNotEmpty == true) {
          // Parse dates from MM/DD/YYYY format
          DateTime purchasedDate = DateTime.now();
          DateTime useByDate = DateTime.now().add(const Duration(days: 7));

          try {
            if (purchasedDateText?.isNotEmpty == true) {
              final parts = purchasedDateText!.split('/');
              if (parts.length == 3) {
                final month = int.parse(parts[0]);
                final day = int.parse(parts[1]);
                final year = int.parse(parts[2]);
                purchasedDate = DateTime(year, month, day);
              }
            }
          } catch (e) {
            purchasedDate = DateTime.now();
          }

          try {
            if (useByDateText?.isNotEmpty == true) {
              final parts = useByDateText!.split('/');
              if (parts.length == 3) {
                final month = int.parse(parts[0]);
                final day = int.parse(parts[1]);
                final year = int.parse(parts[2]);
                useByDate = DateTime(year, month, day);
              }
            }
          } catch (e) {
            useByDate = DateTime.now().add(const Duration(days: 7));
          }

          pantryItems.add(pantry_request.PantryItem(
            id: itemId, // Include the item ID for updates
            item: itemName!,
            amount: int.tryParse(amountText ?? '') ?? 0,
            unit: unit ?? '',
            purchasedDate: purchasedDate,
            useByDate: useByDate,
          ));
        }
      }
    }

    if (pantryItems.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please fill in at least one item', isError: true);
      }
      return;
    }

    // Create the request
    final pantryItemRequest = pantry_request.PantryItemsRequest(
      pantryItems: pantryItems,
    );

    try {
      // Show loading
      if (mounted) {
        Utils().showFlushbar(context, message: 'Updating items...', isError: false);
      }

      // Call the API (reusing addPantryItems for updates)
      final (success, errorMessage) = await ref
          .read(pantryListItemProvider.notifier)
          .addPantryItems(
            pantryListId: selectedPantryListId,
            request: pantryItemRequest,
          );

      if (success) {
        // Clear the editable items and modifications after successful update
        setState(() {
          for (int itemId in modifiedPantryItemIds) {
            editablePantryItems[itemId]?.forEach((key, controller) {
              controller.dispose();
            });
          }
          editablePantryItems.removeWhere((key, value) => modifiedPantryItemIds.contains(key));
          modifiedPantryItemIds.clear();
        });

        // Refresh the pantry list items to get updated data
        final pantryState = ref.read(pantryListNotifierProvider);
        if (pantryState.pantryLists.isNotEmpty && _selectedIndex < pantryState.pantryLists.length) {
          final selectedPantryListId = pantryState.pantryLists[_selectedIndex].id;
          ref.read(pantryListItemProvider.notifier).fetchPantryListsItems(
            id: selectedPantryListId,
            search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
          );
        }

        if (mounted) {
          Utils().showFlushbar(context, message: 'Pantry items updated successfully!', isError: false);
        }
      } else {
        if (mounted) {
          Utils().showFlushbar(context, message: 'Failed to update items: ${errorMessage ?? 'Unknown error'}', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Error updating items: $e', isError: true);
      }
    }
  }

  // ========== SHOPPING ITEM EDITING METHODS ==========

  // Method to make a shopping item editable
  void _makeShoppingItemEditable(ShoppingItem item) {
    setState(() {
      if (!editableShoppingItems.containsKey(item.id)) {
        // Create controllers with change listeners
        final itemController = TextEditingController(text: item.item);
        final amountController = TextEditingController(text: item.amount.toString());
        final unitController = TextEditingController(text: item.unit);
        final storeLocationController = TextEditingController(text: item.storeLocation);
        final recipeController = TextEditingController(text: item.recipe);
        final costController = TextEditingController(text: item.cost.toString());

        // Add listeners to track changes
        itemController.addListener(() => _markShoppingItemAsModified(item.id));
        amountController.addListener(() => _markShoppingItemAsModified(item.id));
        unitController.addListener(() => _markShoppingItemAsModified(item.id));
        storeLocationController.addListener(() => _markShoppingItemAsModified(item.id));
        recipeController.addListener(() => _markShoppingItemAsModified(item.id));
        costController.addListener(() => _markShoppingItemAsModified(item.id));

        editableShoppingItems[item.id] = {
          'item': itemController,
          'amount': amountController,
          'unit': unitController,
          'storeLocation': storeLocationController,
          'recipe': recipeController,
          'cost': costController,
        };
      }
    });
  }

  // Method to track shopping item modifications
  void _markShoppingItemAsModified(int itemId) {
    setState(() {
      modifiedShoppingItemIds.add(itemId);
    });
  }

  // Method to update existing shopping items
  Future<void> _updateShoppingItems() async {
    if (selectedTab != 'Shopping list') {
      if (mounted) {
        Utils().showFlushbar(context, message: 'Please select a shopping list to update items', isError: true);
      }
      return;
    }

    final shoppingState = ref.read(shoppingListNotifierProvider);
    if (shoppingState.shoppingLists.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No shopping list available', isError: true);
      }
      return;
    }

    if (modifiedShoppingItemIds.isEmpty) {
      if (mounted) {
        Utils().showFlushbar(context, message: 'No items to update', isError: true);
      }
      return;
    }

    // Get the selected shopping list ID
    final selectedShoppingListId = shoppingState.shoppingLists[_selectedIndex].id;

    // Prepare shopping items for update
    List<shopping_request.ShoppingItem> shoppingItems = [];

    for (int itemId in modifiedShoppingItemIds) {
      final controllers = editableShoppingItems[itemId];
      if (controllers != null) {
        final itemName = controllers['item']?.text.trim();
        final amountText = controllers['amount']?.text.trim();
        final unit = controllers['unit']?.text.trim();
        final storeLocation = controllers['storeLocation']?.text.trim();
        final recipe = controllers['recipe']?.text.trim();
        final costText = controllers['cost']?.text.trim();

        if (itemName?.isNotEmpty == true) {
          shoppingItems.add(shopping_request.ShoppingItem(
            id: itemId, // Include the item ID for updates
            item: itemName!,
            amount: int.tryParse(amountText ?? '') ?? 0,
            unit: unit ?? '',
            storeLocation: storeLocation ?? '',
            recipe: recipe ?? '',
            cost: double.tryParse(costText ?? '') ?? 0.0,
          ));
        }
      }
    }

    if (shoppingItems.isEmpty) {
      if (mounted) {
         Utils().showFlushbar(context, message: 'Please fill in at least one item', isError: true);
      }
      return;
    }

    // Create the request
    final shoppingItemRequest = shopping_request.ShoppingItemRequest(
      shoppingItems: shoppingItems,
    );

    try {
      // Show loading
      if (mounted) {
        Utils().showFlushbar(context, message: 'Updating items...', isError: false);
      }

      // Call the API (reusing addShoppingItems for updates)
      final (success, errorMessage) = await ref
          .read(shoppingListItemProvider.notifier)
          .addShoppingItems(
            shoppingListId: selectedShoppingListId,
            request: shoppingItemRequest,
          );

      if (success) {
        // Clear the editable items and modifications after successful update
        setState(() {
          for (int itemId in modifiedShoppingItemIds) {
            editableShoppingItems[itemId]?.forEach((key, controller) {
              controller.dispose();
            });
          }
          editableShoppingItems.removeWhere((key, value) => modifiedShoppingItemIds.contains(key));
          modifiedShoppingItemIds.clear();
        });

        // Refresh the shopping list items to get updated data
        final shoppingState = ref.read(shoppingListNotifierProvider);
        if (shoppingState.shoppingLists.isNotEmpty && _selectedIndex < shoppingState.shoppingLists.length) {
          final selectedShoppingListId = shoppingState.shoppingLists[_selectedIndex].id;
          ref.read(shoppingListItemProvider.notifier).fetchShoppingListsItems(
            id: selectedShoppingListId,
            search: _currentItemSearchQuery.isEmpty ? null : _currentItemSearchQuery,
          );
        }

        if (mounted) {
         Utils().showFlushbar(context, message: 'Shopping items updated successfully!', isError: false);
        }
      } else {
        if (mounted) {
          Utils().showFlushbar(context, message: 'Failed to update items: ${errorMessage ?? 'Unknown error'}', isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
         Utils().showFlushbar(context, message: 'Error updating items: $e', isError: true);
      }
    }
  }

  // Dialog functions for creating new shopping and pantry lists
  void showShoppingDialog(BuildContext context, WidgetRef ref) {
    ref.read(isShoppingListCreatedProvider.notifier).state = false;
    ref.read(shoppingItemProvider.notifier).state = '';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => ShoppingDialog(
        dialogType: DialogType.shopping,
        title: 'Create Shopping List',
        hintText: 'Enter shopping item name',
        successText: 'Shopping list created\nsuccessfully!',
        buttonText: 'Create now',
        successButtonText: 'Go to Shopping list',
      ),
    );
  }

  void showPantryDialog(BuildContext context, WidgetRef ref) {
    ref.read(isShoppingListCreatedProvider.notifier).state = false;
    ref.read(shoppingItemProvider.notifier).state = '';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => ShoppingDialog(
        dialogType: DialogType.pantry,
        title: 'Create Pantry List',
        hintText: 'Enter Pantry name',
        successText: 'Pantry list created\nsuccessfully!',
        buttonText: 'Create now',
        successButtonText: 'Go to Pantry list',
      ),
    );
  }
}