import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/categories_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/recipe/subview/add_ingredent.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_add_media_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_author_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_directions_widegts.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_ingredent_textfield.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_notes_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_serving_ideas_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_update_info.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_wine_widgets.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../core/data/models/cookbook.dart';
import '../../core/data/request_query/create_recipe_request.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_loading.dart';
import '../../core/providers/cookboor_notifier.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/providers/recipe/author_provider.dart';
import '../../core/providers/recipe/directions_provider.dart';
import '../../core/providers/recipe/ingrident_provider.dart';
import '../../core/providers/recipe/media_provider.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe/notes_provider.dart';
import '../cookbook/widgets/cookbook_details_card.dart';
import '../cookbook/widgets/custom_title_text.dart';

class AddRecipeScreen extends ConsumerStatefulWidget {
  const AddRecipeScreen({super.key, this.selectedCookbook});

  final Cookbook? selectedCookbook;

  @override
  ConsumerState<AddRecipeScreen> createState() => _AddRecipeScreenState();
}

class _AddRecipeScreenState extends ConsumerState<AddRecipeScreen> {
  late Cookbook? selectedCookbook;
  TextEditingController recipeNameController = TextEditingController();
  TextEditingController recipeDescController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    selectedCookbook = widget.selectedCookbook;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Reset loading state
      ref.read(loadingProvider.notifier).state = false;

      ref.read(ingredientsProvider.notifier).resetWithDefaults();
      ref.read(directionStepsProvider.notifier).resetWithDefaults();
      ref.read(recipeMetadataProvider.notifier).reset();
    });
    // Show error if no cookbook was selected
    if (selectedCookbook == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(ingredientsProvider.notifier).clearIngredients();
        Utils().showFlushbar(
          context,
          message: 'No cookbook selected',
          isError: true,
        );
      });
    }
  }

  @override
  void dispose() {
    recipeNameController.dispose();
    recipeDescController.dispose();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedCookbook = widget.selectedCookbook;
    final notifier = ref.read(recipeNotifierProvider.notifier);
    final isLoading = ref.watch(loadingProvider);

    // Listen to recipeNotifierProvider changes to handle side effects
    ref.listen<AppState>(recipeNotifierProvider, (previous, next) {
      if (next.status == AppStatus.loading) {
        ref.read(loadingProvider.notifier).state = true;
      } else if (next.status == AppStatus.success) {
        ref.read(loadingProvider.notifier).state = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          Utils().showFlushbar(
            context,
            message: 'Recipe saved successfully!',
            isError: false,
            onDismissed: () {
              // if (selectedCookbook != null) {
              //   ref.read(recipeNotifierProvider.notifier).fetchRecipes(
              //         context: context,
              //         cookbookId: selectedCookbook.id,
              //         reset: true,
              //       );
              // }
              // Add short delay before popping
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  Navigator.of(context).pop();
                }
              });
            },
          );
        });
      } else if (next.status == AppStatus.error) {
        ref.read(loadingProvider.notifier).state = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          Utils().showFlushbar(
            context,
            message: next.errorMessage ?? 'Failed to save recipe',
            isError: true,
          );
        });
      }
    });

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Add Recipe',
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomButton(
              text: "Save Changes",
              onPressed: () => onSavePressed(context, ref),
              fontSize: 20.sp,
              width: 230.w,
            ),
          )
        ],
      ),
      body: Stack(
        children: [
          buildUI(notifier),
          if (isLoading)
            const Positioned.fill(child: Center(child: CustomLoading())),
        ],
      ),
    );
  }

  Widget buildUI(RecipeNotifier notifier) {
    if (selectedCookbook == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        Image.asset(
          AssetsManager.background_img,
          fit: BoxFit.cover,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 20.h),
            Expanded(
              flex: 3,
              child: Container(
                color: AppColors.secondaryColor,
                child: Column(
                  children: [
                    SizedBox(height: 20.h),
                    CustomSearchBar(controller: searchController),
                    Expanded(
                      child: ListView(
                        padding: const EdgeInsets.all(12),
                        children: [
                          if (selectedCookbook != null)
                            CookbookDetailCard(
                              cookbook: selectedCookbook!,
                              isSelected: true,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 6,
              child: ScrollConfiguration(
                behavior: const ScrollBehavior().copyWith(scrollbars: false),
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 20.w, right: 20.w, top: 30.h, bottom: 30.h),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.secondaryColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding:
                            EdgeInsets.only(right: 30.w, left: 30.h, top: 30.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildRecipeHeader(context),
                            SizedBox(height: 30.h),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                      border:
                                          Border.all(color: Colors.grey[200]!),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.05),
                                          blurRadius: 10,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: MediaPickerGrid(),
                                  ),
                                ),
                                SizedBox(width: 30.w),
                                Expanded(
                                  flex: 2,
                                  child: _buildRecipeMetadata(),
                                ),
                              ],
                            ),
                            SizedBox(height: 40.h),
                            CustomDirectionsWidgets(),
                            SizedBox(height: 40.h),
                            CustomServingWidget(),
                            SizedBox(height: 40.h),
                            CustomWineWidget(),
                            SizedBox(height: 40.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: SingleChildScrollView(
                hitTestBehavior: HitTestBehavior.translucent,
                child: Padding(
                  padding:
                      EdgeInsets.only(right: 20.w, top: 30.h, bottom: 30.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildIngredientsSection(context),
                      SizedBox(height: 30.h),
                      _buildAuthorSection(),
                      SizedBox(height: 30.h),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void onSavePressed(BuildContext context, WidgetRef ref) async {
    if (selectedCookbook == null) {
      Utils().showFlushbar(context,
          message: 'No cookbook selected', isError: true);
      return;
    }

    final metadata = ref.read(recipeMetadataProvider);
    final authorData = ref.read(authorProvider);
    final ingredients = ref.read(ingredientsProvider);
    final mediaFiles = ref.read(mediaFilesProvider);
    final directionNotifier = ref.read(directionStepsProvider.notifier);
    final directionJson = directionNotifier.directionsJson;
    final directionMediaFiles = directionNotifier.mediaFiles;

    // Convert to base64 strings
    final mediaBase64List = await Future.wait(
      mediaFiles
          .map((file) async => await convertFileToBase64(file.mediaFile))
          .toList(),
    );

    final selectedMediaFiles =
        mediaFiles.map((media) => media.mediaFile).whereType<File>().toList();
    final notes = ref.read(notesProvider);
    if (recipeNameController.text.trimRight().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please enter a recipe name', isError: true);
      return;
    }
    if (metadata.categoryId == null) {
      Utils().showFlushbar(context,
          message: 'Please select a category', isError: true);
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showFlushbar(context,
          message: 'Please select a cuisine', isError: true);
      return;
    }

    print('Ingredients: ${ingredients.length}');

    final request = CreateRecipeRequest(
      type: '',
      name: recipeNameController.text,
      description: recipeDescController.text ?? '',
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      yieldValue: metadata.yieldValue ?? '',
      servings: metadata.servings,
      prepTime: metadata.prepTime ?? '',
      cookTime: metadata.cookTime ?? '',
      totalTime: metadata.totalTime ?? '',
      recipeMediaFiles: selectedMediaFiles.isEmpty ? null : selectedMediaFiles,
      directionsJson: directionJson ?? '',
      directionMediaFiles:
          directionMediaFiles.isEmpty ? null : directionMediaFiles,
      servingIdeas: metadata.servingIdeas ?? '',
      wine: metadata.wineDesc ?? '',
      ingredients: ingredients.isEmpty ? null : ingredients,
      authorName:
          authorData.authorName?.isEmpty ?? true ? null : authorData.authorName,
      authorSource:
          authorData.source?.isEmpty ?? true ? null : authorData.source,
      authorCopyright:
          authorData.copyright?.isEmpty ?? true ? null : authorData.copyright,
      authorProfileFile: authorData.image,
      notes: notes ?? '',
    );

    final cookbookId = selectedCookbook!.id;

    final result = await ref.read(recipeNotifierProvider.notifier).createRecipe(
          context: context,
          request: request,
          cookbookId: cookbookId,
        );

    if (result) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        ref.read(loadingProvider.notifier).state = false;
        await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              context: context,
              loadMore: false,
            );
        ref.read(recipeNotifierProvider.notifier).fetchRecipes(
              cookbookId: cookbookId,
              cookbookName: recipeNameController.text,
              reset: true,
              context: context,
            );
      });
    }
  }


  Widget _buildIngredientsSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomeTitleText(title: 'Ingredients'),
          SizedBox(height: 8.h),
          AddIngredientScreen(),
        ],
      ),
    );
  }

  Widget _buildAuthorSection() {
    return Container(
      width: 600.w,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          const CustomeTitleText(title: 'Author info'),
          const SizedBox(height: 20),
          CustomAuthorWidget(),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8),
          CustomeTitleText(title: 'Notes'),
          CustomNotesWidget(),
        ],
      ),
    );
  }

  Widget _buildRecipeHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        IngredientTextField(
          hintText: 'Recipe Name',
          controller: recipeNameController,
          maxLines: 1,
          height: 70.h,
        ),
        SizedBox(height: 16.h),
        IngredientTextField(
          hintText: 'Recipe Description',
          controller: recipeDescController,
          maxLines: 7,
          height: 200.h,
        ),
      ],
    );
  }

  Widget _buildRecipeMetadata() {
    final categoriesState = ref.read(categoriesNotifierProvider);
    final cuisinesState = ref.read(cuisinesNotifierProvider);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: UpdateInfoScreen(
        categoriesState.data ?? [],
        cuisinesState.data ?? [],
        'Select',
        'Select',
        callFromUpdate: false,
      ),
    );
  }
}
