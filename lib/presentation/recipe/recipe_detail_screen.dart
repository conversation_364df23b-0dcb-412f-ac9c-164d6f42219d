import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_left_right_arrow.dart';
import 'package:mastercookai/presentation/recipe/subview/recipe_cards.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_doted_lines.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../core/data/models/recipe_response.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_network_image.dart';
import '../../core/data/models/category_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/helpers/app_constant.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/single_recipe_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/utils/Utils.dart';
import '../../core/widgets/common_image.dart';
import '../../core/widgets/video_player_widget.dart';
import '../cookbook/widgets/custom_desc_text.dart';
import '../cookbook/widgets/custom_title_text.dart';
import '../cookbook/widgets/drop_down_filter.dart';
import '../cookbook/widgets/edit_button.dart';
import '../shimer/cookbook_list_shimmer.dart';
import '../shimer/recipe_detail_shimmer.dart';

class RecipeDetailScreen extends ConsumerStatefulWidget {
  final int recipeId;
  final String SelectedRecipeName;
  final int cookbookId;
  final List<Recipe> recipesList;

  const RecipeDetailScreen(
      {super.key,
        required this.recipeId,
        required this.recipesList,
        required this.SelectedRecipeName,
        required this.cookbookId});

  @override
  ConsumerState<RecipeDetailScreen> createState() => _RecipeDetailScreenState();
}

final Map<String, List<String>> _filterOptions = {
  "Cuisine": [],
  "Category": [],
};


final Map<String, String> _selectedFilters = {
  "Cuisine": "",
  "Category": "",
};


class _RecipeDetailScreenState extends ConsumerState<RecipeDetailScreen> {
  final TextEditingController searchController = TextEditingController();
  final TextEditingController topSearchController = TextEditingController();
  String? selectedImageUrl;
  bool imageInitialized = false;

  final ScrollController _scrollController = ScrollController();
  final double itemWidth = 120.w + 20.w; // Item + separator
  int? _selectedRecipeId;
  String? selectedRecipeName;
  int? _selectedCookbookId;
  int _selectedRecipeIndex = 0;

  int cuisine=0;
  int category=0;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    selectedRecipeName = widget.SelectedRecipeName;
    _selectedRecipeId = widget.recipeId;
    _selectedCookbookId = widget.cookbookId;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final recipeListState = ref.read(recipeNotifierProvider);
      final extras = GoRouterState.of(context).extra as Map<String, dynamic>?;
      final Recipe? passedRecipe = extras?['recipe'] as Recipe?;
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
        cookbookId: _selectedCookbookId!,
        cuisineId: cuisine,
        categoryId: category,
        // cookbookName: name,
        reset: true,
        context: context,
      );

      if (recipeListState.data != null && recipeListState.data!.isNotEmpty) {
        if (recipeListState != null) {
          final index = recipeListState.data!
              .indexWhere((recipe) => recipe.id == passedRecipe?.id);
          if (index != -1) {
            setState(() {
              _selectedRecipeIndex = index;
            });
          }
        }
      }

      ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
          context: context,
          cookbookId: _selectedCookbookId ?? 0,
          recipeId: widget.recipeId);
      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);
      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);

      final cuisines = ref.read(cuisinesNotifierProvider).data ?? [];
      _filterOptions['Cuisine'] = cuisines.map((c) => c.name ?? '').toList();

      final categories = ref.read(categoriesNotifierProvider).data ?? [];
      _filterOptions['Category'] = categories.map((c) => c.name ?? '').toList();

    });
  }

  @override
  Widget build(BuildContext context) {
    final recipeState = ref.watch(singleRecipeNotifierProvider);
    final categoriesState = ref.watch(categoriesNotifierProvider);
    final cuisinesState = ref.watch(cuisinesNotifierProvider);
    final recipeListState = ref.watch(recipeNotifierProvider);


    // Listen for recipe state changes to update selectedImageUrl
    ref.listen(singleRecipeNotifierProvider, (previous, next) {
      if (next.status == AppStatus.success && next.data != null) {
        final mediaList = next.data!.recipeMedia;
        if (mediaList != null &&
            mediaList.isNotEmpty &&
            mediaList.first.mediaUrl != null) {
          if (selectedImageUrl != mediaList.first.mediaUrl) {
            setState(() {
              selectedImageUrl = mediaList.first.mediaUrl;
              print("Updated selectedImageUrl: $selectedImageUrl");
            });
          }
        } else {
          print("No valid mediaUrl found in recipeMedia: $mediaList");
        }
      }
    });

    return Scaffold(
      appBar: CustomAppBar(
        title: selectedRecipeName ?? '',
        onPressed: () {
          Navigator.of(context).pop();
        },
        actions: [
          Wrap(
            spacing: 8,
            children: _filterOptions.keys.map((filterName) {
              return DropdownFilter(
                filterName: filterName,
                selectedFilters: _selectedFilters,
                filterOptions: _filterOptions,
                onFilterChanged: (filterName, selectedValue) {
                  setState(() {
                    _selectedFilters[filterName] = selectedValue;
                    print('Selected $filterName: ${_selectedFilters[filterName]}');

                    switch (filterName) {
                      case 'Cuisine':
                        final cuisineModel = ref
                            .read(cuisinesNotifierProvider)
                            .data
                            ?.firstWhere((c) => c.name == selectedValue);
                        if (cuisineModel != null) {
                          cuisine=cuisineModel.id!;
                          ref.read(recipeMetadataProvider.notifier).updateCuisineId(cuisineModel.id!);

                          ref.read(recipeNotifierProvider.notifier)
                              .fetchRecipes(
                              cookbookId: _selectedCookbookId!,
                              cuisineId: cuisine,
                              categoryId: category,
                              reset: true,
                              context: context);
                        }
                        break;
                        // case 'Ratings':
                        // // Handle rating filter change
                        break;
                      case 'Category':
                        final categoryModel = ref
                            .read(categoriesNotifierProvider)
                            .data
                            ?.firstWhere((c) => c.name == selectedValue);
                        if (categoryModel != null) {
                          category=categoryModel.id!;
                          ref.read(recipeMetadataProvider.notifier).updateCategoryId(categoryModel.id);

                          ref.read(recipeNotifierProvider.notifier)
                              .fetchRecipes(
                              cookbookId:_selectedCookbookId!,
                              cuisineId: cuisine,
                              categoryId: category,
                              reset: true,
                              context: context);
                        }
                        break;
                    // case 'Preference':
                    // // Handle rating filter change
                    //   break;
                    }
                  });

                },
              );
            }).toList(),
            // children: _filterOptions.keys.map((filterName) {
            //   return _buildDropdownFilter(filterName);
            // }).toList(),
          ),
          SizedBox(width: 12.w),
          // CustomSearchBar(
          //   controller: topSearchController,
          //   width: 400.w,
          //   height: 60.h,
          // )
        ],
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: Container(
                  color: AppColors.secondaryColor,
                  child: Column(
                    children: [
                      SizedBox(
                        height: 30.h,
                      ),
                      CustomSearchBar(
                        width: 600.w,
                        height: 60.h,
                        controller: searchController,
                      ),
                      Expanded(
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (scrollNotification) {
                            if (scrollNotification is ScrollEndNotification &&
                                scrollNotification.metrics.pixels ==
                                    scrollNotification
                                        .metrics.maxScrollExtent) {
                              final recipeListState =
                              ref.read(recipeNotifierProvider);
                              if (recipeListState.hasMore &&
                                  recipeListState.status !=
                                      AppStatus.loadingMore) {

                                ref.read(recipeNotifierProvider.notifier).fetchRecipes(
                                  cookbookId: _selectedCookbookId!,
                                  cuisineId: cuisine,
                                  categoryId: category,
                                  // cookbookName: name,
                                  reset: true,
                                  context: context,
                                );
                              }
                            }
                            return false;
                          },
                          child: Builder(
                            builder: (context) {
                              if (recipeListState.status == AppStatus.loading ||
                                  recipeListState.status ==
                                      AppStatus.loadingMore) {
                                return CookbookListShimmer(
                                  itemCount: 20,
                                );
                              } else if (recipeListState.status ==
                                  AppStatus.error) {
                                return Center(
                                    child: Text(
                                        'Error: ${recipeListState.errorMessage}'));
                              }
                              else if (recipeListState.data == null ||
                                  recipeListState.data!.isEmpty) {
                                return const Center(
                                    child: Text('No Recipes found'));
                              }

                              return ListView.separated(
                                controller: _scrollController,
                                padding: const EdgeInsets.all(12),
                                itemCount: recipeListState.data!.length +
                                    (recipeListState.hasMore ? 1 : 0),
                                separatorBuilder: (_, __) =>
                                const SizedBox(height: 12),
                                itemBuilder: (context, index) {
                                  if (index >= recipeListState.data!.length) {
                                    return CookbookListShimmer(
                                      itemCount: 8,
                                    );
                                  }

                                  final recipe = recipeListState.data![index];
                                  final isSelected = _selectedRecipeId == recipe.id;

                                  return GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _selectedRecipeIndex = index;
                                        _currentPage = 0;
                                      });
                                      // cookbookId=cookbook.id;
                                      ref
                                          .read(recipeNotifierProvider.notifier)
                                          .fetchRecipes(
                                          cookbookId: _selectedCookbookId!,
                                          cuisineId: cuisine,
                                          categoryId: category,
                                          // cookbookName: cookbook.name,
                                          reset: true,
                                          context: context);
                                    },
                                    child: RecipeCardItem(
                                      recipes: widget.recipesList[index],
                                      isSelected: isSelected,
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ),

                    ],
                  ),
                ),
              ),
              // Left Part (60% of screen)
              Expanded(
                flex: 8,
                child: Consumer(
                  builder: (context, ref, _) {
                    if (recipeState.status == AppStatus.loading) {
                      return RecipeDetailsShimmer();
                    }

                    if (recipeState.status == AppStatus.error) {
                      return Center(
                        child: Text(
                          "No data found",
                          style: TextStyle(color: Colors.red, fontSize: 18),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }

                    if (recipeState.status == AppStatus.success) {
                      final recipeDetail = recipeState.data;
                      if (recipeDetail == null) {
                        return Center(
                          child: Text(
                            "Recipe data is null",
                            style: TextStyle(color: Colors.red, fontSize: 18),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }

                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 6,
                            child: SingleChildScrollView(
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20.w,
                                    right: 20.w,
                                    top: 30.h,
                                    bottom: 30.h),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: AppColors.secondaryColor,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        right: 30.w,
                                        left: 30.w,
                                        top: 30.h,
                                        bottom: 30.h),
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: [
                                        _buildRecipeHeader(
                                            context, recipeDetail),
                                        SizedBox(height: 30.h),
                                        Row(
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              flex: 3,
                                              child: Column(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius:
                                                    BorderRadius.circular(
                                                        12),
                                                    child: selectedImageUrl !=
                                                        null &&
                                                        (selectedImageUrl!
                                                            .endsWith(
                                                            '.mp4') ||
                                                            selectedImageUrl!
                                                                .endsWith(
                                                                '.mov'))
                                                        ? SizedBox(
                                                      height: 400.w,
                                                      width:
                                                      MediaQuery.of(
                                                          context)
                                                          .size
                                                          .width,
                                                      child: AspectRatio(
                                                          aspectRatio:
                                                          16 / 9,
                                                          child:
                                                          VideoPlayerWidget(
                                                            mediaUrl:
                                                            selectedImageUrl!,
                                                            headers: {
                                                              'Authorization':
                                                              'Bearer ${bearerToken}'
                                                            },
                                                            height: 400,
                                                          )),
                                                    )
                                                        : CommonImage(
                                                      imageSource:
                                                      selectedImageUrl??'',
                                                      height: 400.w,
                                                      width:
                                                      MediaQuery.of(
                                                          context)
                                                          .size
                                                          .width,
                                                      fit: BoxFit.cover,
                                                      placeholder:
                                                      AssetsManager
                                                          .recipe_place_holder,
                                                    ),
                                                  ),
                                                  SizedBox(height: 30.h),
                                                  if (recipeDetail
                                                      .recipeMedia !=
                                                      null &&
                                                      recipeDetail.recipeMedia!
                                                          .isNotEmpty)
                                                    _buildSimilarRecipesList(
                                                        recipeDetail
                                                            .recipeMedia!,
                                                        recipeDetail
                                                            .recipeThumbnailFileUrl ??
                                                            ''),
                                                ],
                                              ),
                                            ),
                                            SizedBox(width: 30.w),
                                            Expanded(
                                              flex: 2,
                                              child: _buildRecipeMetadata(
                                                  recipeDetail,
                                                  recipeDetail,
                                                  categoriesState.data,
                                                  cuisinesState.data),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 40.h),
                                        if (recipeDetail.directions != null)
                                          _buildDirectionsList(
                                              recipeDetail.directions!,
                                              recipeDetail,
                                              categoriesState.data,
                                              cuisinesState.data),
                                        SizedBox(height: 40.h),
                                        _buildServingIdeasSection(
                                            recipeDetail.servingIdeas ?? ''),
                                        SizedBox(height: 40.h),
                                        _buildWinePairingSection(
                                            recipeDetail.wine ?? ''),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: SingleChildScrollView(
                              physics: AlwaysScrollableScrollPhysics(),
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20.w,
                                    right: 20.w,
                                    top: 30.h,
                                    bottom: 30.h),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    if (recipeDetail.ingredients != null)
                                      _buildIngredientsSection(
                                          context,
                                          widget.recipesList,
                                          _selectedCookbookId ?? 0,
                                          _selectedRecipeId ?? 0,
                                          recipeDetail.ingredients!,
                                          recipeDetail,
                                          categoriesState.data,
                                          cuisinesState.data),
                                    SizedBox(height: 30.h),
                                    if (recipeDetail.nutritionFacts != null)
                                      _buildNutritionSection(
                                          recipeDetail.nutritionFacts!),
                                    SizedBox(height: 30.h),
                                    _buildAuthorSection(
                                        recipeDetail.author ?? '',
                                        recipeDetail.authorMediaUrl ?? '',
                                        recipeDetail.copyright ?? '',
                                        recipeDetail.source ?? '',
                                        recipeDetail,
                                        categoriesState.data,
                                        cuisinesState.data),
                                    SizedBox(height: 30.h),
                                    _buildNotesSection(
                                        recipeDetail.notes ?? '',
                                        recipeDetail,
                                        categoriesState.data,
                                        cuisinesState.data),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    }

                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }


  Widget _buildIngredientsSection(
      BuildContext context,
      List<Recipe> recipesList,
      int cookbookId,
      int recipeId,
      List<String>? ingredients,
      RecipeDetails recipeDetail,
      List<Categories>? categoriesList,
      List<Cuisines>? cuisinesList) {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topRight,
            child: EditButton(onPressed: () async {
              goToEditRecipeScreen(context, recipeId, cookbookId, recipesList,
                  recipeDetail, categoriesList!, cuisinesList!);
            }),
          ),
          CustomeTitleText(title: 'Ingredients'),
          SizedBox(height: 20.h),
          ingredients!.isNotEmpty
              ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: ingredients.map((ingredient) {
              return Padding(
                padding: EdgeInsets.only(bottom: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(AssetsManager.dot,
                        height: 8.sp, width: 8.sp),
                    SizedBox(width: 20.w),
                    Expanded(
                        child: CustomDescText(
                          desc: ingredient,
                          size: responsiveFont(21).sp,
                        ))
                  ],
                ),
              );
            }).toList(),
          )
              : SizedBox(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildNutritionSection(List<NutritionFacts>? nutritionFacts) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomeTitleText(title: 'Nutrition facts'),
          const SizedBox(height: 20),
          if (nutritionFacts != null && nutritionFacts.isNotEmpty)
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 2,
              crossAxisSpacing: 15,
              mainAxisSpacing: 10,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 2,
              children: nutritionFacts.map((fact) {
                return _buildNutritionCard(fact.value ?? '', fact.label ?? '');
              }).toList(),
            )
          else
            Center(
              child: Text(
                'No nutrition facts available',
                style: context.theme.textTheme.bodySmall!.copyWith(
                  color: AppColors.blackColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 20.sp,
                ),
              ),
            ),
          const SizedBox(height: 20),
          Center(
            child: TextButton(
              onPressed: () {
                // TODO: Open detailed nutrition page
              },
              child: Text(
                'View detailed nutrition facts',
                style: context.theme.textTheme.displaySmall!.copyWith(
                  color: AppColors.primaryBorderColor,
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.underline,
                  decorationColor: AppColors.primaryBorderColor,
                  decorationThickness: 1.0,
                  fontSize: 20.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionCard(String value, String label) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.greyCardColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            value,
            style: context.theme.textTheme.displaySmall!.copyWith(
              color: AppColors.primaryGreyColor,
              fontWeight: FontWeight.w700,
              fontSize: headingSixFontSize,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          SizedBox(height: 5.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 2),
            child: Text(
              label,
              style: context.theme.textTheme.bodySmall!.copyWith(
                color: AppColors.textGreyColor,
                fontWeight: FontWeight.w400,
                fontSize: headingSixFontSize,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuthorSection(
      String authorName,
      String authorUrl,
      String copyRight,
      String source,
      RecipeDetails recipeDetail,
      List<Categories>? categoriesList,
      List<Cuisines>? cuisinesList) {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topRight,
            child: EditButton(onPressed: () {
              goToEditRecipeScreen(
                  context,
                  widget.recipeId,
                  widget.cookbookId,
                  widget.recipesList,
                  recipeDetail,
                  categoriesList!,
                  cuisinesList!);
            }),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomeTitleText(title: "Author info"),
              SizedBox(height: 10.h),
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: authorUrl.isNotEmpty
                        ? SizedBox(
                      height: 160,
                      width: 160,
                      child: ClipOval(
                        child: CustomNetworkImage(
                          fit: BoxFit.cover,
                          imageUrl: authorUrl,
                          errorWidget: Image.asset(
                            AssetsManager.recipe_place_holder,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    )
                        : const CircleAvatar(
                      radius: 80,
                      child: Icon(Icons.person),
                    ),
                  ),
                  SizedBox(height: 10),
                  Center(child: CustomeTitleText(title: authorName ?? '')),
                  SizedBox(height: 8.h),
                  GestureDetector(
                    onTap: () {
                      // Handle URL navigation
                    },
                    child: Text(
                      source ?? '',
                      textAlign: TextAlign.center,
                      style: context.theme.textTheme.displaySmall!.copyWith(
                        color: AppColors.primaryBorderColor,
                        fontWeight: FontWeight.w400,
                        fontSize: 18.sp,
                      ),
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    copyRight ?? '',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.primaryLightTextColor,
                    ),
                  ),
                  SizedBox(height: 20.h),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(String notes, RecipeDetails recipeDetail,
      List<Categories>? categoriesList, List<Cuisines>? cuisinesList) {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topRight,
            child: EditButton(onPressed: () {
              goToEditRecipeScreen(
                  context,
                  widget.recipeId,
                  widget.cookbookId,
                  widget.recipesList,
                  recipeDetail,
                  categoriesList!,
                  cuisinesList!);
            }),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomeTitleText(title: 'Notes'),
              CustomDescText(
                size: 20.sp,
                desc: notes ?? '',
              ),
              SizedBox(height: 20.h),
            ],
          ),
        ],
      ),
    );
  }
  Widget _buildSimilarRecipesList(
      List<RecipeMedia> similarRecipes, String recipeThumbnailFileUrl) {
    return SizedBox(
      height: 120.h,
      child: Row(
        children: [
          CustomRightLeftArrow(
            icon: Icons.arrow_back_ios,
            onPressed: () {
              if (_selectedRecipeIndex > 0) {
                setState(() {
                  _selectedRecipeIndex--;
                  selectedImageUrl = similarRecipes[_selectedRecipeIndex].mediaUrl;
                });
                // Scroll to the new position
                _scrollController.animateTo(
                  _selectedRecipeIndex * itemWidth,
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            },
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: ListView.separated(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              itemCount: similarRecipes.length,
              separatorBuilder: (context, index) => SizedBox(width: 20.w),
              itemBuilder: (context, index) {
                final similarRecipe = similarRecipes[index];
                return MouseRegion(
                  cursor: SystemMouseCursors.basic,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedRecipeIndex = index;
                        selectedImageUrl = similarRecipe.mediaUrl;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _selectedRecipeIndex == index
                              ? Colors.blue
                              : Colors.transparent,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CommonImage(
                          imageSource: Utils().isVideo(similarRecipe.mediaUrl)
                              ? recipeThumbnailFileUrl
                              : similarRecipe.mediaUrl ?? '',
                          placeholder: AssetsManager.recipe_place_holder,
                          width: 120.w,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(width: 10.w),
          CustomRightLeftArrow(
            icon: Icons.arrow_forward_ios,
            onPressed: () {
              if (_selectedRecipeIndex < similarRecipes.length - 1) {
                setState(() {
                  _selectedRecipeIndex++;
                  selectedImageUrl = similarRecipes[_selectedRecipeIndex].mediaUrl;
                });
                // Scroll to the new position
                _scrollController.animateTo(
                  _selectedRecipeIndex * itemWidth,
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            },
          ),
        ],
      ),
    );
  }
  // Widget _buildSimilarRecipesList(
  //     List<RecipeMedia> similarRecipes, String recipeThumbnailFileUrl) {
  //   return SizedBox(
  //     height: 120.h,
  //     child: Row(
  //       children: [
  //         CustomRightLeftArrow(
  //           icon: Icons.arrow_back_ios,
  //           onPressed: () {
  //             final currentOffset = _scrollController.offset;
  //             final newOffset = currentOffset - itemWidth;
  //             _scrollController.animateTo(
  //               newOffset,
  //               duration: Duration(milliseconds: 300),
  //               curve: Curves.easeOut,
  //             );
  //
  //             // Update selected image index based on current index
  //             final newIndex = (_getCurrentIndexFromOffset(newOffset)).clamp(0, similarRecipes.length - 1);
  //             setState(() {
  //               selectedImageUrl = similarRecipes[newIndex].mediaUrl;
  //             });
  //           },
  //           // onPressed: () {
  //           //   _scrollController.animateTo(
  //           //     _scrollController.offset - itemWidth,
  //           //     duration: Duration(milliseconds: 300),
  //           //     curve: Curves.easeOut,
  //           //   );
  //           // },
  //         ),
  //         SizedBox(width: 10.w),
  //         Expanded(
  //           child: ListView.separated(
  //             controller: _scrollController,
  //             scrollDirection: Axis.horizontal,
  //             itemCount: similarRecipes.length,
  //             separatorBuilder: (context, index) => SizedBox(width: 20.w),
  //             itemBuilder: (context, index) {
  //               final similarRecipe = similarRecipes[index];
  //               return MouseRegion(
  //                 cursor: SystemMouseCursors.basic,
  //                 child: InkWell(
  //                   onTap: () {
  //                     setState(() {
  //                       selectedImageUrl = similarRecipe.mediaUrl;
  //                       print(
  //                           "Tapped similar recipe, selectedImageUrl: $selectedImageUrl");
  //                     });
  //                   },
  //                   child: Container(
  //                     decoration: BoxDecoration(
  //                       border: Border.all(
  //                         color: selectedImageUrl == similarRecipe.mediaUrl
  //                             ? Colors.blue
  //                             : Colors.transparent,
  //                         width: 2,
  //                       ),
  //                       borderRadius: BorderRadius.circular(12),
  //                     ),
  //                     child: ClipRRect(
  //                       borderRadius: BorderRadius.circular(12),
  //                       child: CommonImage(
  //                         imageSource: Utils().isVideo(similarRecipe.mediaUrl)
  //                             ? recipeThumbnailFileUrl
  //                             : similarRecipe.mediaUrl ?? '',
  //                         placeholder: AssetsManager.recipe_place_holder,
  //                         width: 120.w,
  //                         fit: BoxFit.cover,
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               );
  //             },
  //           ),
  //         ),
  //         SizedBox(width: 10.w),
  //         CustomRightLeftArrow(
  //           icon: Icons.arrow_forward_ios,
  //           onPressed: () {
  //             final currentOffset = _scrollController.offset;
  //             final newOffset = currentOffset + itemWidth;
  //             _scrollController.animateTo(
  //               newOffset,
  //               duration: Duration(milliseconds: 300),
  //               curve: Curves.easeOut,
  //             );
  //
  //             final newIndex = (_getCurrentIndexFromOffset(newOffset)).clamp(0, similarRecipes.length - 1);
  //             setState(() {
  //               selectedImageUrl = similarRecipes[newIndex].mediaUrl;
  //             });
  //           },
  //           // onPressed: () {
  //           //   _scrollController.animateTo(
  //           //     _scrollController.offset + itemWidth,
  //           //     duration: Duration(milliseconds: 300),
  //           //     curve: Curves.easeOut,
  //           //   );
  //           // },
  //         ),
  //       ],
  //     ),
  //   );
  // }
  int _getCurrentIndexFromOffset(double offset) {
    return (offset / (itemWidth + 20.w)).round(); // assuming 20.w is your separator
  }
  Widget _buildRecipeHeader(BuildContext context, RecipeDetails recipe) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              recipe.name ?? '',
              style: TextStyle(
                fontSize: 40.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.primaryGreyColor,
              ),
            ),
            Row(
              children: [
                Container(
                  padding:
                  EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: AppColors.primaryGreyColor,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        AssetsManager.share,
                        width: 20.w,
                        height: 20.h,
                        color: AppColors.primaryBorderColor,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Share',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.primaryGreyColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12.w),
                Container(
                  padding:
                  EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: AppColors.primaryGreyColor,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        AssetsManager.print,
                        width: 20.w,
                        height: 20.h,
                        color: AppColors.primaryBorderColor,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Print',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.primaryGreyColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 20.h),
        CustomPaint(
          painter: DottedLinePainter(
            strokeWidth: 1,
            dashWidth: 6,
            color: AppColors.lightestGreyColor,
          ),
          size: Size(double.infinity, 2),
        ),
        SizedBox(height: 20.h),
        CustomDescText(
          desc: recipe.description ?? '',
          textColor: AppColors.primaryLightTextColor,
          size: 20.sp,
        ),
      ],
    );
  }

  Widget _buildRecipeMetadata(RecipeDetails recipe, RecipeDetails recipeDetail,
      List<Categories>? categoriesList, List<Cuisines>? cuisinesList) {
    final metadata = [
      {
        'icon': AssetsManager.time,
        'label': 'Prep Time',
        'value': recipe.prepTime ?? ''
      },
      {
        'icon': AssetsManager.time,
        'label': 'Cook Time',
        'value': recipe.cookTime ?? ''
      },
      {
        'icon': AssetsManager.time,
        'label': 'Total Time',
        'value': recipe.totalTime ?? ''
      },
      {
        'icon': AssetsManager.group,
        'label': 'Category',
        'value': recipe.category ?? ''
      },
      {
        'icon': AssetsManager.cuisine,
        'label': 'Cuisine',
        'value': recipe.cuisine ?? ''
      },
      {
        'icon': AssetsManager.yield,
        'label': 'Yield',
        'value': recipe.yieldUnit != null && recipe.yield != null
            ? '${recipe.yield.toString()} ${recipe.yieldUnit.toString()}'
            : ""
      },
      {
        'icon': AssetsManager.serving,
        'label': 'Servings',
        'value': recipe.servings != null ? recipe.servings.toString() : ''
      },
    ];

    return Container(
      padding: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.greyBorderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Align(
              alignment: Alignment.topRight,
              child: EditButton(
                onPressed: () {
                  goToEditRecipeScreen(
                      context,
                      widget.recipeId,
                      widget.cookbookId,
                      widget.recipesList,
                      recipeDetail,
                      categoriesList!,
                      cuisinesList!);
                },
              )),
          SizedBox(height: 15.h),
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 2,
            crossAxisSpacing: 15,
            mainAxisSpacing: 10,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 2,
            children: metadata.map((item) {
              return _buildMetadataCard(
                icon: item['icon'] as String,
                label: item['label'] as String,
                value: item['value'] as String,
                iconColor: item['color'] as Color?,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMetadataCard({
    required String icon,
    required String label,
    required String value,
    Color? iconColor,
  }) {
    return Container(
      padding: EdgeInsets.only(left: 20, right: 10),
      decoration: BoxDecoration(
        color: AppColors.greyCardColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(icon),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: context.theme.textTheme.displaySmall!.copyWith(
                      color: AppColors.primaryGreyColor,
                      fontWeight: FontWeight.w700,
                      fontSize: headingSixFontSize,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    label,
                    style: context.theme.textTheme.bodySmall!.copyWith(
                      color: AppColors.textGreyColor,
                      fontWeight: FontWeight.w400,
                      fontSize: headingSixFontSize,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDirectionsList(
      List<Directions> directions,
      RecipeDetails recipeDetail,
      List<Categories>? categoriesList,
      List<Cuisines>? cuisinesList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomeTitleText(title: 'Directions'),
            CustomDescText(
              desc: "(${directions.length} Steps)",
              size: 20.sp,
              textColor: AppColors.textGreyColor,
            )
          ],
        ),
        SizedBox(height: 20.h),
        if (directions.isNotEmpty)
          ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: directions.length,
            itemBuilder: (context, index) {
              final step = directions[index];
              return Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(bottom: 20.h),
                    padding: EdgeInsets.only(
                        bottom: 15.h, left: 15.w, right: 15.w, top: 15.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                          color: AppColors.greyBorderColor, width: 1),
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Stack(
                          children: [
                            ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: CommonImage(
                                  imageSource: step.mediaUrl,
                                  placeholder:
                                  AssetsManager.directions_place_holder,
                                  height: 230.h,
                                  width: 230.w,
                                  fit: BoxFit.cover,
                                )),
                          ],
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: CustomDescText(
                              desc: step.description ?? '',
                              textColor: AppColors.primaryLightTextColor,
                              size: 20.sp,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    top: 12,
                    left: 0,
                    child: SizedBox(
                      width: 80.sp,
                      height: 80.sp,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          SvgPicture.asset(
                            AssetsManager.steps,
                            width: 80.sp,
                            height: 80.sp,
                            fit: BoxFit.contain,
                          ),
                          Text(
                            "Step ${index + 1}",
                            textAlign: TextAlign.center,
                            style:
                            context.theme.textTheme.displaySmall!.copyWith(
                              color: context.theme.scaffoldBackgroundColor,
                              fontSize: 18.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Row(
                      children: [
                        SizedBox(width: 10.w),
                        EditButton(
                          onPressed: () {
                            goToEditRecipeScreen(
                                context,
                                widget.recipeId,
                                widget.cookbookId,
                                widget.recipesList,
                                recipeDetail,
                                categoriesList!,
                                cuisinesList!);
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          )
        else
          SizedBox()
      ],
    );
  }

  Widget _buildServingIdeasSection(String servingIdea) {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomeTitleText(title: 'Serving Ideas'),
          SizedBox(height: 20.h),
          CustomPaint(
            painter: DottedLinePainter(
              strokeWidth: 1,
              dashWidth: 6,
              color: AppColors.lightestGreyColor,
            ),
            size: Size(double.infinity, 2),
          ),
          SizedBox(height: 16.h),
          CustomDescText(
            desc: servingIdea ?? '',
            size: 24.sp,
            textColor: AppColors.primaryLightTextColor,
          ),
        ],
      ),
    );
  }

  Widget _buildWinePairingSection(String wine) {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomeTitleText(title: 'Wine'),
          SizedBox(height: 20.h),
          CustomPaint(
            painter: DottedLinePainter(
              strokeWidth: 1,
              dashWidth: 6,
              color: AppColors.lightestGreyColor,
            ),
            size: Size(double.infinity, 2),
          ),
          SizedBox(height: 16.h),
          CustomDescText(
            desc: wine ?? '',
            size: 24.sp,
            textColor: AppColors.primaryLightTextColor,
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Future<void> goToEditRecipeScreen(
      BuildContext context,
      int recipeId,
      int cookbookId,
      List<Recipe> recipesList,
      RecipeDetails recipeDetails,
      List<Categories> categories,
      List<Cuisines> cuisines) async {
    await context.push(
      '/cookbook/cookbookDetail/recipeDetail/$recipeId/EditRecipeScreen',
      extra: {
        'recipeId': recipeId,
        'cookbookId': cookbookId,
        'recipesList': recipesList,
        'recipeDetails': recipeDetails,
        'categories': categories,
        'cuisines': cuisines,
      },
    );
  }
}
