import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/recipe/subview/add_ingredent.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_add_media_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_author_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_directions_widegts.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_notes_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_serving_ideas_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_update_info.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_wine_widgets.dart';
import 'package:mastercookai/presentation/recipe/subview/recipe_cards.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_searchbar.dart';
import '../../core/data/models/category_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/data/models/recipe_detail_response.dart';
import '../../core/data/models/recipe_response.dart';
import '../../core/data/request_query/create_recipe_request.dart';
import '../../core/data/request_query/recipe_meta_data.dart';
import '../../core/providers/recipe/directions_provider.dart';
import '../../core/providers/recipe/ingrident_provider.dart';
import '../../core/providers/recipe/media_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/recipe/author_provider.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe/notes_provider.dart';
import '../../core/providers/single_recipe_notifier.dart';
import '../../core/utils/Utils.dart';
import '../cookbook/widgets/custom_title_text.dart';
import 'edit_recipe_subview/buildAuthorSection.dart';
import 'edit_recipe_subview/buildIngredientsSection.dart';
import 'edit_recipe_subview/buildNotesSection.dart';
import 'edit_recipe_subview/buildNutritionSection.dart';
import 'edit_recipe_subview/buildRecipeHeader.dart';
import 'edit_recipe_subview/buildRecipeMetadata.dart';

class EditRecipeScreen extends ConsumerStatefulWidget {
  final int recipeId;
  final int cookbookId;
  List<Recipe> recipesList;
  final RecipeDetails recipeDetails;
  final List<Categories> categories;
  final List<Cuisines> cuisines;

  EditRecipeScreen({
    super.key,
    required this.recipeId,
    required this.cookbookId,
    required this.recipesList,
    required this.recipeDetails,
    required this.categories,
    required this.cuisines,
  });

  @override
  ConsumerState<EditRecipeScreen> createState() => _EditRecipeScreenState();
}

class _EditRecipeScreenState extends ConsumerState<EditRecipeScreen> {
  final TextEditingController searchController = TextEditingController();
  int _selectedCookbookIndex = 0;
  late TextEditingController recipeNameController;
  late TextEditingController recipeDescController;
  late List<String> ingredients;
  late List<NutritionFacts> nutritionFacts;
  late List<Directions> directions;
  late List<RecipeMedia> recipeMedia;
  late String servingIdeas;
  late String wine;
  late String author;
  late String authorMediaUrl;
  late String copyright;
  late String source;
  late String notes;
  String? _category;
  String? _cuisine;
  bool _isLoading = true; // Add loading state
  bool isUpdate = false;

  // Add loading state

  @override
  void initState() {
    super.initState();
    // Initialize controllers and state from passed recipeDetails
    recipeNameController =
        TextEditingController(text: widget.recipeDetails.name ?? '');
    recipeDescController =
        TextEditingController(text: widget.recipeDetails.description ?? '');
    ingredients = widget.recipeDetails.ingredients ?? [];
    nutritionFacts = List.from(widget.recipeDetails.nutritionFacts ?? []);

    directions = List.from(widget.recipeDetails.directions ?? []);
    recipeMedia = List.from(widget.recipeDetails.recipeMedia ?? []);

    servingIdeas = widget.recipeDetails.servingIdeas ?? '';
    wine = widget.recipeDetails.wine ?? '';
    author = widget.recipeDetails.author ?? '';
    authorMediaUrl = widget.recipeDetails.authorMediaUrl ?? '';
    copyright = widget.recipeDetails.copyright ?? '';
    source = widget.recipeDetails.source ?? '';
    notes = widget.recipeDetails.notes ?? '';
    _category = widget.recipeDetails.category ??
        (widget.categories.isNotEmpty ? widget.categories.first.name : null);
    _cuisine = widget.recipeDetails.cuisine ??
        (widget.cuisines.isNotEmpty ? widget.cuisines.first.name : null);
    // Initialize providers with recipeDetails data after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final categoryId = widget.categories
          .firstWhere(
            (c) => c.name == _category,
        orElse: () => widget.categories.isNotEmpty
            ? widget.categories.first
            : Categories(id: 0, name: 'Unknown'),
      )
          .id;
      final cuisineId = widget.cuisines
          .firstWhere(
            (c) => c.name == _cuisine,
        orElse: () => widget.cuisines.isNotEmpty
            ? widget.cuisines.first
            : Cuisines(id: 0, name: 'Unknown'),
      )
          .id;

      ref.read(directionStepsProvider.notifier).setDirections(directions);
      // Reset ingredientsProvider to avoid stale state
      ref.read(ingredientsProvider.notifier).clearIngredients();
      // Update ingredientsProvider with backend data
      ref.read(ingredientsProvider.notifier).updateIngredients(ingredients);

      // Update recipeMetadataProvider with all fields
      ref.read(recipeMetadataProvider.notifier).updateAll(
        recipeId: widget.recipeId,
        cookbookId: widget.cookbookId,
        recipesList: widget.recipesList,
        recipeDetails: widget.recipeDetails,
        categoryId: categoryId,
        cuisineId: cuisineId,
      );

      ref.read(recipeMetadataProvider.notifier)
        ..updateYield(widget.recipeDetails.yield)
        ..updateServings(widget.recipeDetails.servings)
        ..updatePrepTime(widget.recipeDetails.prepTime)
        ..updateCookTime(widget.recipeDetails.cookTime)
        ..updateTotalTime(widget.recipeDetails.totalTime)
        ..updateAuthor(widget.recipeDetails.author)
        ..updateAuthorMediaUrl(widget.recipeDetails.authorMediaUrl)
        ..updateCopyright(widget.recipeDetails.copyright)
        ..updateSource(widget.recipeDetails.source)
        ..updateServingIdeas(widget.recipeDetails.servingIdeas)
        ..updateDirections(directions)
        ..updateWine(widget.recipeDetails.wine);

      // Update other providers
      ref
          .read(notesProvider.notifier)
          .setNote(widget.recipeDetails.notes ?? '');
      ref.read(authorProvider.notifier)
        ..updateAuthorName(widget.recipeDetails.author ?? '')
        ..updateSource(widget.recipeDetails.source ?? '')
        ..updateCopyright(widget.recipeDetails.copyright ?? '');

      // Set loading to false after updates
      setState(() {
        _isLoading = false;
      });
    });
  }

  @override
  void dispose() {
    recipeNameController.dispose();
    recipeDescController.dispose();
    searchController.dispose();
    // Clear ingredientsProvider to avoid stale state
    //  ref.read(ingredientsProvider.notifier).clearIngredients();
    super.dispose();
  }

  void updateData() {
    // Validate required fields
    if (recipeNameController.text.trimRight().isEmpty) {
      Utils().showSnackBar(context, "Please enter a recipe name");
      return;
    }

    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    // Retrieve data from providers
    final authorData = ref.read(authorProvider);
    final notes = ref.read(notesProvider);
    ingredients = ref.read(ingredientsProvider);
    // Update recipeMetadataProvider with consolidated data
    final updatedMetadata = RecipeMetadata(
      recipeId: widget.recipeId,
      cookbookId: widget.cookbookId,
      recipesList: widget.recipesList,
      name: recipeNameController.text,
      description: recipeDescController.text,
      ingredients: ingredients,
      nutritionFacts: nutritionFacts,
      author: authorData.authorName ?? author,
      authorMediaUrl: authorData.image?.path ?? authorMediaUrl,
      copyright: authorData.copyright ?? copyright,
      source: authorData.source ?? source,
      directions: directions,
      servingIdeas: metadata.servingIdeas ?? servingIdeas,
      wine: metadata.wine ?? wine,
      recipeMedia: recipeMedia,
      categoryId: metadata.categoryId,
      cuisineId: metadata.cuisineId,
      yieldValue: metadata.yieldValue,
      servings: metadata.servings,
      prepTime: metadata.prepTime,
      cookTime: metadata.cookTime,
      totalTime: metadata.totalTime,
      wineDesc: metadata.wineDesc,
    );

    ref.read(recipeMetadataProvider.notifier).updateAll(
      recipeId: updatedMetadata.recipeId!,
      cookbookId: updatedMetadata.cookbookId!,
      recipesList: updatedMetadata.recipesList!,
      recipeDetails: RecipeDetails(
        name: updatedMetadata.name,
        description: updatedMetadata.description,
        ingredients: updatedMetadata.ingredients,
        nutritionFacts: updatedMetadata.nutritionFacts,
        author: updatedMetadata.author,
        authorMediaUrl: updatedMetadata.authorMediaUrl,
        copyright: updatedMetadata.copyright,
        source: updatedMetadata.source,
        directions: updatedMetadata.directions,
        servingIdeas: updatedMetadata.servingIdeas,
        wine: updatedMetadata.wine,
        recipeMedia: updatedMetadata.recipeMedia,
        category: _category,
        cuisine: _cuisine,
        servings: updatedMetadata.servings,
        prepTime: updatedMetadata.prepTime,
        cookTime: updatedMetadata.cookTime,
        totalTime: updatedMetadata.totalTime,
      ),
      categoryId: updatedMetadata.categoryId,
      cuisineId: updatedMetadata.cuisineId,
    );

    // Update other providers
    ref.read(notesProvider.notifier).setNote(notes ?? '');
    ref.read(authorProvider.notifier)
      ..updateAuthorName(authorData.authorName ?? '')
      ..updateSource(authorData.source ?? '')
      ..updateCopyright(authorData.copyright ?? '');
    final directionJson =
        ref.read(directionStepsProvider.notifier).directionsJson;
    final directionMediaFiles =
        ref.read(directionStepsProvider.notifier).mediaFiles;
    final mediaFiles = ref.watch(mediaFilesProvider);
    final selectedMediaFiles =
    mediaFiles.map((media) => media.mediaFile).whereType<File>().toList();

    final recipeMetadata = ref.watch(recipeMetadataProvider);
    final wineDesc = recipeMetadata.wineDesc;
  }

  void _saveIngrdient(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.INGREDIENT.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      ingredients: ingredients.isEmpty ? null : ingredients,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveAuthor(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final authorData = ref.read(authorProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)

    final request = CreateRecipeRequest(
      type: RecipeSection.AUTHOR.name,
      categoryId: metadata.categoryId!,
      existingAuthorMediaFileId: widget.recipeDetails.authorMediaFileId,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      authorName:
      authorData.authorName?.isEmpty ?? true ? null : authorData.authorName,
      authorSource:
      authorData.source?.isEmpty ?? true ? null : authorData.source,
      authorCopyright:
      authorData.copyright?.isEmpty ?? true ? null : authorData.copyright,
      authorProfileFile: authorData.image,
    );

    updateRecipe(request);
  }

  void _saveNotes(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final notes = ref.read(notesProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.NOTES.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      notes: notes ?? '',
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveBasicInfo(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.BASIC.name,
      name: recipeNameController.text,
      description: recipeDescController.text ?? '',
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      yieldValue: metadata.yieldValue ?? '',
      servings: metadata.servings,
      prepTime: metadata.prepTime ?? '',
      cookTime: metadata.cookTime ?? '',
      totalTime: metadata.totalTime ?? '',
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveDirections(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final directionJson =
        ref.read(directionStepsProvider.notifier).directionsJson;
    final directionMediaFiles =
        ref.read(directionStepsProvider.notifier).mediaFiles;

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.DIRECTION.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      directionsJson: directionJson ?? '',
      directionMediaFiles:
      directionMediaFiles.isEmpty ? null : directionMediaFiles,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveOthers(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final recipeMetadata = ref.watch(recipeMetadataProvider);
    final wineDesc = recipeMetadata.wineDesc;

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.OTHER.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      servingIdeas: metadata.servingIdeas ?? '',
      wine: wineDesc,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveMediaFiles(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final mediaFiles = ref.watch(mediaFilesProvider);
    final selectedMediaFiles =
    mediaFiles.map((media) => media.mediaFile).whereType<File>().toList();
    final existingMedia =
    mediaFiles.map((media) => media.mediaFileId).whereType<int>().toList();

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.RECIPE_MEDIA.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      existingRecipeMediaFileIds: existingMedia,
      recipeMediaFiles: selectedMediaFiles.isEmpty ? null : selectedMediaFiles,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  Future<void> updateRecipe(CreateRecipeRequest request) async {
    final recipeState = ref.watch(recipeNotifierProvider);
    // //  Call updateRecipe API
    final result = await ref.read(recipeNotifierProvider.notifier).updateRecipe(
        context: context,
        request: request,
        cookbookId: widget.cookbookId,
        recipeId: widget.recipeId);

    if (result) {
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
        cookbookId: widget.cookbookId,
        cookbookName: recipeNameController.text,
        reset: true,
        context: context,
      );

      ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
          context: context,
          cookbookId: widget.cookbookId ?? 0,
          recipeId: widget.recipeId);

      setState(() {
        isUpdate = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Edit Recipe',
        onPressed: () async {
          print("isUpdate=== ${isUpdate}");
          if (isUpdate) {
            final recipeState = ref.watch(recipeNotifierProvider);
            widget.recipesList = recipeState.data ?? [];
            context.go(
              '/cookbook/cookbookDetail/recipeDetail/${widget.recipeId}',
              extra: {
                'id': widget.recipeId,
                'recipeList': widget.recipesList,
                'recipeName': recipeNameController.text,
                'cookbookId': widget.cookbookId,
              },
            );
          } else {
            Navigator.pop(context);
          }
        },
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left Sidebar (30% of screen)
              Expanded(
                flex: 3,
                child: Container(
                  color: AppColors.secondaryColor,
                  child: Column(
                    children: [
                      SizedBox(height: 20.h),
                      CustomSearchBar(controller: searchController),
                      Expanded(
                        child: ListView.separated(
                          padding: const EdgeInsets.all(12),
                          itemCount: widget.recipesList.length,
                          separatorBuilder: (_, __) =>
                          const SizedBox(height: 12),
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () {
                                // setState(() {
                                //   _selectedCookbookIndex = index;
                                // });
                              },
                              child: RecipeCardItem(
                                recipes: widget.recipesList[index],
                                isSelected: _selectedCookbookIndex == index,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Main Content (60% of screen)
              Expanded(
                flex: 6,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 20.w, right: 20.w, top: 30.h, bottom: 30.h),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.secondaryColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding:
                        EdgeInsets.only(right: 30.w, left: 30.w, top: 30.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            buildRecipeHeader(
                              context,
                              ref,
                              recipeNameController: recipeNameController,
                              recipeDescController: recipeDescController,
                            ),
                            SizedBox(height: 30.h),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                      border:
                                      Border.all(color: Colors.grey[200]!),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.05),
                                          blurRadius: 10,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      children: [
                                        MediaPickerGrid(
                                          recipeMedia: recipeMedia,
                                          isCallFromEdit: true,
                                          onUpdateData: () {
                                            _saveMediaFiles(context);
                                          },
                                        ),
                                        // if (recipeMedia.isNotEmpty)
                                        //   _buildSimilarRecipesList(recipeMedia),
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(width: 30.w),
                                Expanded(
                                  flex: 2,
                                  child: buildRecipeMetadata(
                                      widget.categories,
                                      widget.cuisines,
                                      _category,
                                      _cuisine, onUpdateData: () {
                                    _saveBasicInfo(context);
                                  }),
                                ),
                              ],
                            ),
                            SizedBox(height: 40.h),
                            CustomDirectionsWidgets(
                              // directions: directions,
                              isCallFromEdit: true,
                              onUpdateData: () {
                                _saveDirections(context);
                              },
                            ),
                            SizedBox(height: 40.h),
                            CustomServingWidget(
                              servingIdeas: servingIdeas,
                            ),
                            SizedBox(height: 40.h),
                            CustomWineWidget(
                              wine: wine,
                            ),
                            SizedBox(height: 40.h),
                            Center(
                              child: CustomButton(
                                text: "Save Changes",
                                fontSize: 16,
                                width: 240,
                                onPressed: () {
                                  // Ensure ingredients are updated before notifying parent
                                  _saveOthers(context);
                                },
                              ),
                            ),
                            SizedBox(height: 40.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // Right Sidebar (40% of screen)
              Expanded(
                flex: 2,
                child: SingleChildScrollView(
                  child: Padding(
                    padding:
                    EdgeInsets.only(right: 20.w, top: 30.h, bottom: 30.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        buildIngredientsSection(context, onSaveIngredient: () {
                          _saveIngrdient(context);
                        }),
                        SizedBox(height: 30.h),
                        buildNutritionSection(context, nutritionFacts,
                            onSaveNutrition: () {}),
                        SizedBox(height: 30.h),
                        buildAuthorSection(
                            author, authorMediaUrl, copyright, source,
                            onSaveAuthor: () {
                              _saveAuthor(context);
                            }),
                        SizedBox(height: 30.h),
                        buildNotesSection(notes, onUpdateData: () {
                          _saveNotes(context);
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}