import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../app/imports/core_imports.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../subview/custom_author_widget.dart';

Widget buildAuthorSection(String author, String authorMediaUrl, String copyright, String source, {required Null Function() onSaveAuthor}) {
  return Container(
    width: 600.w,
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const CustomeTitleText(title: "Author info"),
        <PERSON><PERSON><PERSON><PERSON>(height: 20.h),
        CustomAuthorWidget(
          author: author,
          authorMediaUrl: authorMediaUrl,
          copyright: copyright,
          source: source,
          isCallFromEdit: true,
          onUpdateData: onSaveAuthor
        ),
      ],
    ),
  );
}