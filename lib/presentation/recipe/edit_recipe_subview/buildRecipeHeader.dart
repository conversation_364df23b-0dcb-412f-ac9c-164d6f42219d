import 'package:flutter_riverpod/src/consumer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../subview/custom_ingredent_textfield.dart';

Widget buildRecipeHeader(BuildContext context, WidgetRef ref,
    {required TextEditingController recipeNameController,
    required TextEditingController recipeDescController}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      IngredientTextField(
        hintText: "Recipe Name",
        controller: recipeNameController,
        maxLines: 1,
        height: 70.h,
        onChanged: (value) {
          ref.read(recipeMetadataProvider.notifier).updateName(value);
        },
      ),
      Si<PERSON><PERSON><PERSON>(height: 16.h),
      IngredientTextField(
        hintText: "Recipe Description",
        controller: recipeDescController,
        maxLines: 7,
        height: 200.h,
        onChanged: (value) {
          ref.read(recipeMetadataProvider.notifier).updateDescription(value);
        },
      ),
    ],
  );
}
