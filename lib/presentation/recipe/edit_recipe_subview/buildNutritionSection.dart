import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../cookbook/widgets/custom_title_text.dart';

Widget buildNutritionSection(BuildContext context, List<NutritionFacts> nutritionFacts, {required VoidCallback onSaveNutrition,}) {
  return Container(
    padding: EdgeInsets.all(16.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const CustomeTitleText(title: 'Nutrition facts'),
        SizedBox(height: 20.h),
        nutritionFacts.isNotEmpty
            ? GridView.count(
          shrinkWrap: true,
          crossAxisCount: 2,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 2,
          children: nutritionFacts.map((fact) {
            return _buildNutritionCard(context,
                fact.value ?? '', fact.label ?? '');
          }).toList(),
        )
            : Center(
          child: TextButton(
            onPressed: () {
              // Navigate to detailed nutrition page
            },
            child: Text(
              'View detailed nutrition facts',
              style: context.theme.textTheme.bodySmall!.copyWith(
                color: AppColors.blackColor,
                fontWeight: FontWeight.w400,
                fontSize: 20.sp,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

Widget _buildNutritionCard(BuildContext context,String value, String label) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.grey[100],
      borderRadius: BorderRadius.circular(10),
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          value,
          style: context.theme.textTheme.displaySmall!.copyWith(
            color: context.theme.hintColor,
            fontSize: context.theme.textTheme.displaySmall!.fontSize,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          maxLines: 1,
          style: context.theme.textTheme.displaySmall!.copyWith(
            color: AppColors.backgroudInActiveColor,
            fontWeight: FontWeight.w400,
            fontSize: context.theme.textTheme.displaySmall!.fontSize,
          ),
        ),
      ],
    ),
  );
}
