import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import '../../../../core/helpers/media_picker_service.dart';

import '../../../core/providers/recipe/directions_provider.dart';
import '../../../core/widgets/common_image.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'custom_ingredent_textfield.dart';
import 'custom_sub_media_widget.dart';

class CustomDirectionsWidgets extends ConsumerStatefulWidget {

  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

    CustomDirectionsWidgets({super.key, List<Directions>? directions, this.isCallFromEdit = false,
      this.onUpdateData,});

  @override
  ConsumerState<CustomDirectionsWidgets> createState() =>
      _CustomDirectionsWidgetState();
}

class _CustomDirectionsWidgetState
    extends ConsumerState<CustomDirectionsWidgets> {
  final List<TextEditingController> _descriptionControllers = [];
  final List<ImageProvider> _imageList = [];

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final directions = ref.read(directionStepsProvider);
      if (directions.isEmpty) {
        ref.read(directionStepsProvider.notifier).addStep();
        ref.read(directionStepsProvider.notifier).addStep();
        ref.read(directionStepsProvider.notifier).addStep();
      }
    });
  }

  void _addStep() {
    ref.read(directionStepsProvider.notifier).addStep();
  }

  void _deleteStep(int index) {
    if (_descriptionControllers.length <= 2) return;
    setState(() {
      _descriptionControllers.removeAt(index);
      _imageList.removeAt(index);
    });
    ref.read(directionStepsProvider.notifier).removeStep(index);
  }

  @override
  Widget build(BuildContext context) {
    final directions = ref.watch(directionStepsProvider);
    // Sync controllers with direction steps
    while (_descriptionControllers.length < directions.length) {
      _descriptionControllers.add(TextEditingController(
        text: directions[_descriptionControllers.length].description,
      ));
    }
    while (_descriptionControllers.length > directions.length) {
      _descriptionControllers.removeLast();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomeTitleText(
            title: 'Directions(${_descriptionControllers.length})'),
        SizedBox(height: 20.h),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: directions.length,
          itemBuilder: (context, index) {
            final step = directions[index];
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey.shade300),
                color: Colors.white,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Image area with edit/delete buttons
                  step.mediaFile != null || step.imageUrl.isNotEmpty
                      ? Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: CommonImage(
                            imageSource: step.mediaFile ?? step.imageUrl,
                            width: 300.w,
                            height: 250.h,
                            fit: BoxFit.cover,
                            placeholder: AssetsManager.directions_place_holder,
                            borderRadius: 10.0, // Apply rounded corners
                          ),
                      )
                      : Container(
                          margin: const EdgeInsets.all(12),
                          width: 300.w,
                          height: 250.h,
                          child: CustomPaint(
                            painter: DottedBorderPainter(),
                            child: Container(
                              alignment: Alignment.center,
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border:
                                      Border.all(color: Colors.grey.shade300),
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(2.0),
                                  child: TextButton(
                                    onPressed: () async {
                                      final pickedFile =
                                          await MediaPickerService
                                              .pickSingleImage();
                                      if (pickedFile != null) {
                                        ref
                                            .read(
                                                directionStepsProvider.notifier)
                                            .updateStep(
                                              index,
                                              mediaFile: pickedFile,
                                              mediaFileName: pickedFile.path
                                                  .split('/')
                                                  .last,
                                            );
                                      }
                                    },
                                    child: Text(
                                      "Add Image/Video",
                                      style: context.textTheme.titleMedium
                                          ?.copyWith(
                                        color: AppColors.primaryLightTextColor,
                                        fontSize: 13.sp,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                  // TextField
                  Expanded(
                    child: Padding(
                        padding: EdgeInsets.all(10.sp),
                        child: IngredientTextField(
                          controller: _descriptionControllers[index],
                          hintText: 'Description ...',
                          maxLines: 8,
                          height: 250.h,
                          onChanged: (value) {
                            ref.read(directionStepsProvider.notifier).updateStep(index, description: value);
                          },
                        )),
                  ),
                ],
              ),
            );
          },
        ),
        SizedBox(height: 12.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomButton(
              text: "Add more step",
              onPressed: _addStep,
              fontSize: 16,
              width: 240.w,
              color: AppColors.lightestGreyColor,
              textColor: Colors.black,
            ),
            SizedBox(width: 20.h),
            Visibility(
              visible: widget.isCallFromEdit??false,
              child: CustomButton(
                text: "Save Changes",
                width: 240.w,
                fontSize: 16,
                onPressed: () {
                  // Ensure ingredients are updated before notifying parent
                  widget. onUpdateData?.call();
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
      ],
    );
  }


}
