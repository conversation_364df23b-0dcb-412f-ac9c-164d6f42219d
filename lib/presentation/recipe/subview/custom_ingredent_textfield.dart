import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

// Enum to define input format types
enum InputFormatType {
  text,
  number, // For integers
  double, // For decimal numbers
}

class IngredientTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final bool autoFocus;
  final int maxLines;
  final double height;
  final bool  isEnabled; // Default to true, can be overridden if needed
  final InputFormatType formatType; // NEW: Replaces keyboardType and formats
  final Function(String)? onChanged;
  final String? Function(String?)? validator;
  final VoidCallback? onTap;

  const IngredientTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.height = 50,
    this.maxLines = 1,
    this.autoFocus = false,
    this.isEnabled = true, // Allow enabling/disabling the field
    this.formatType = InputFormatType.text, // Default to text
    this.onChanged,
    this.validator,
    this.onTap,
  });

  // Get appropriate keyboard type and input formatters based on formatType
  TextInputType get _keyboardType {
    switch (formatType) {
      case InputFormatType.number:
        return TextInputType.number;
      case InputFormatType.double:
        return TextInputType.numberWithOptions(decimal: true);
      case InputFormatType.text:
      default:
        return TextInputType.text;
    }
  }

  List<TextInputFormatter> get _inputFormatters {
    switch (formatType) {
      case InputFormatType.number:
        return [FilteringTextInputFormatter.digitsOnly];
      case InputFormatType.double:
        return [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')),
        ];
      case InputFormatType.text:
      default:
        return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: SizedBox(
        height: height,
        child: TextFormField(
          maxLines: maxLines,
          controller: controller,
          keyboardType: _keyboardType,
          autofocus: autoFocus,
          enabled: isEnabled,
          inputFormatters: _inputFormatters,
          validator: validator,
          style: context.theme.textTheme.labelMedium!.copyWith(
            color: AppColors.primaryGreyColor,
            fontWeight: FontWeight.w400,
            fontSize: context.theme.textTheme.titleMedium!.fontSize,
          ),
          decoration: InputDecoration(
            filled: context.theme.inputDecorationTheme.filled,
            fillColor: context.theme.inputDecorationTheme.fillColor,
            hintText: hintText,
            hintStyle: context.theme.inputDecorationTheme.hintStyle,
            errorStyle: context.theme.inputDecorationTheme.errorStyle,
            enabledBorder: context.theme.inputDecorationTheme.enabledBorder,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            suffixIcon: controller.text.isNotEmpty
                ? IconButton(
              icon: const Icon(Icons.clear, size: 18),
              onPressed: () {
                controller.clear();
                if (onChanged != null) {
                  onChanged!('');
                }
              },
            )
                : null,
          ),
          onChanged: onChanged,
          onTap: onTap,
        ),
      ),
    );
  }
}