import 'dart:io';
import 'dart:math';
import '../../../../../app/imports/core_imports.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../core/helpers/media_picker_service.dart';
import '../../../core/providers/recipe/author_provider.dart';
import '../../../core/widgets/circular_image.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/dotted_circle_painter.dart';
import 'custom_ingredent_textfield.dart';

class CustomAuthorWidget extends ConsumerStatefulWidget {
  String? author;
  String? authorMediaUrl;
  String? copyright;
  String? source;
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

  CustomAuthorWidget(
      {super.key, this.author, this.authorMediaUrl, this.copyright, this.source,
        this.isCallFromEdit = false,
        this.onUpdateData,
      });
  

  @override
  ConsumerState<CustomAuthorWidget> createState() => _CustomAuthorWidgetState();
}

class _CustomAuthorWidgetState extends ConsumerState<CustomAuthorWidget> {

  late TextEditingController sourceController;
  late TextEditingController copyrightController;
  late TextEditingController authorNameController;

  File? authorImage;

  @override
  void initState() {
    super.initState();
    // Initialize noteController with value from notesProvider or widget.notes
    authorNameController = TextEditingController(text: widget.author ?? '',);
    sourceController = TextEditingController(text: widget.source ?? '',);
    copyrightController = TextEditingController(text: widget.copyright ?? '',);
   }

  void _pickAuthorImage() async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null) {
      ref.read(authorProvider.notifier).updateImage(file);
      setState(() {
        authorImage = file;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authorState = ref.watch(authorProvider);
     return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [

          CircularImage(
            imageFile: authorState.image, // From authorProvider
            imageUrl: widget.authorMediaUrl, // Fallback to network URL if provided
            placeholderAsset: 'assets/images/recipe_place_holder.png', // Replace with your asset path
            radius: 150.0, // Matches your original radius
            onTap: () => _pickAuthorImage(),
            borderColor: Colors.grey.shade300,
            placeholderText: 'Add Image',
          ),
          SizedBox(height: 10.h,),

          IngredientTextField(
            hintText: "Author", controller: authorNameController, height: 70.h,
            onChanged: (value) =>
                ref.read(authorProvider.notifier).updateAuthorName(value),
          ),
          SizedBox(height: 8.h),

          IngredientTextField(
            hintText: "Source", controller: sourceController, height: 70.h,
            onChanged: (value) =>
                ref.read(authorProvider.notifier).updateSource(value),
          ),
          SizedBox(height: 8.h),
          IngredientTextField(hintText: "Copyright",
            controller: copyrightController,
            height: 70.h,
            onChanged: (value) =>
                ref.read(authorProvider.notifier).updateCopyright(value),),


          SizedBox(height: 12),
          // CustomButton(text: "Save changes", onPressed: (){} , width: 230.w, fontSize: 20.sp,),
          Visibility(
            visible: widget.isCallFromEdit??false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 50.0),
              child: CustomButton(
                text: "Save Changes",
                fontSize: 16,

                onPressed: () {
                  // Ensure ingredients are updated before notifying parent
                  widget. onUpdateData?.call();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

