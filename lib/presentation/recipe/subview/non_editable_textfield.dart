import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class NonEditableTextField extends StatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final double height;

  const NonEditableTextField({
    super.key,
    required this.controller,
    this.hintText = '',
    this.height = 50,
  });

  @override
  State<NonEditableTextField> createState() => _NonEditableTextFieldState();
}

class _NonEditableTextFieldState extends State<NonEditableTextField> {
  bool isNotEmpty = false;

  @override
  void initState() {
    super.initState();
    isNotEmpty = widget.controller.text.isNotEmpty;
    widget.controller.addListener(_handleTextChange);
  }

  void _handleTextChange() {
    if (mounted) {
      setState(() {
        isNotEmpty = widget.controller.text.isNotEmpty;
      });
    }
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleTextChange);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: SizedBox(
        height: widget.height,
        child: TextField(
          controller: widget.controller,
          readOnly: true,
          style: context.theme.textTheme.labelMedium!.copyWith(
            color: AppColors.primaryGreyColor,
            fontWeight: FontWeight.w400,
            fontSize: context.theme.textTheme.titleMedium!.fontSize,
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.white,
            hintText: widget.hintText,
            hintStyle: context.theme.inputDecorationTheme.hintStyle,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide.none,
            ),
            enabledBorder: context.theme.inputDecorationTheme.enabledBorder,
            suffixIcon: isNotEmpty
                ? IconButton(
              icon: const Icon(Icons.clear, size: 18),
              onPressed: () {
                widget.controller.clear();
                setState(() {
                  isNotEmpty = false;
                });
              },
            )
                : null,
          ),
        ),
      ),
    );
  }
}