import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/cuisines_notifier.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_loading.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'custom_drop_down.dart';
import 'custom_ingredent_textfield.dart';
import 'ingredient_timefield.dart';

class UpdateInfoScreen extends ConsumerStatefulWidget {
  List<Categories> categories;
  List<Cuisines> cuisines;
  String? category;
  String? cuisine;
  bool callFromUpdate;
  VoidCallback? onUpdateData;


  UpdateInfoScreen(this.categories, this.cuisines, this.category, this.cuisine,

      {super.key,required this.callFromUpdate,this.onUpdateData,});

  @override
  ConsumerState<UpdateInfoScreen> createState() => _UpdateInfoScreenState();
}

class _UpdateInfoScreenState extends ConsumerState<UpdateInfoScreen> {
  // Text controllers
  final _yieldController = TextEditingController();
  final _servingsController = TextEditingController();
  final _prepTimeController = TextEditingController();
  final _cookTimeController = TextEditingController();
  final _totalTimeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize text controllers from recipeMetadataProvider
   if(widget.callFromUpdate){
     setDefaultData();
   }else{
     reset();
   }
  }

  void setDefaultData(){
    final metadata = ref.read(recipeMetadataProvider);
    _yieldController.text = metadata.yieldValue ?? '';
    _servingsController.text = metadata.servings?.toString() ?? '';
    _prepTimeController.text = metadata.prepTime ?? '';
    _cookTimeController.text = metadata.cookTime ?? '';
    _totalTimeController.text = metadata.totalTime ?? '';
  }


  void reset(){
    _yieldController.text =   '';
    _servingsController.text =  '';
    _prepTimeController.text =   '';
    _cookTimeController.text =   '';
    _totalTimeController.text =   '';
  }

  @override
  void dispose() {
    _yieldController.dispose();
    _servingsController.dispose();
    _prepTimeController.dispose();
    _cookTimeController.dispose();
    _totalTimeController.dispose();
    super.dispose();
  }

  int prepHours = 0, prepMinutes = 0;
  int cookHours = 0, cookMinutes = 0;

  String _calculateTotalTime() {
    int totalMinutes =
        (prepHours + cookHours) * 60 + (prepMinutes + cookMinutes);
    int totalH = totalMinutes ~/ 60;
    int totalM = totalMinutes % 60;

    return "${totalH}h ${totalM}m";
  }

  void _updateTotalTimeIfPossible() {
    // Check if both prep and cook times are available
    if (prepHours != null &&
        prepMinutes != null &&
        cookHours != null &&
        cookMinutes != null) {
      // Calculate total time (assuming hours and minutes are integers)
      int totalMinutes =
          (prepHours * 60 + prepMinutes) + (cookHours * 60 + cookMinutes);
      int totalHours = totalMinutes ~/ 60;
      int remainingMinutes = totalMinutes % 60;

      // Format total time (e.g., "2h 30m" or as per your _calculateTotalTime logic)
      String totalTime =
          '$totalHours:${remainingMinutes.toString().padLeft(2, '0')}';

      // Update the total time controller and provider
      _totalTimeController.text = totalTime;
      ref.read(recipeMetadataProvider.notifier).updateTotalTime(totalTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomeTitleText(title: 'Update info'),
          SizedBox(height: 20.h),
          CustomDropdown<Categories>(
            label: 'Category',
            value: widget.category,
            options: widget.categories,
            onChanged: (val) {
              setState(() {
                widget.category = val;
              });
              final category = widget.categories.firstWhere(
                (c) => c.name == val,
                orElse: () => widget.categories.isNotEmpty
                    ? widget.categories.first
                    : Categories(id: 0, name: 'Unknown'),
              );
              ref
                  .read(recipeMetadataProvider.notifier)
                  .updateCategoryId(category.id);
            },
            getDisplayString: (category) => category.name ?? 'Unknown',
          ),
          SizedBox(height: 16.h),
          Consumer(
            builder: (context, ref, child) {
              final cuisinesState = ref.watch(cuisinesNotifierProvider);

              if (cuisinesState.status == AppStatus.loading ||
                  cuisinesState.status == AppStatus.loadingMore) {
                return const CustomLoading();
              }

              if (cuisinesState.status == AppStatus.error) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                        'Error: ${cuisinesState.errorMessage ?? 'Failed to load cuisines'}'),
                    TextButton(
                      onPressed: () {
                        ref
                            .read(cuisinesNotifierProvider.notifier)
                            .fetchCuisines(context: context);
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                );
              }

              if (cuisinesState.status == AppStatus.success) {
                final cuisines = cuisinesState.data ?? [];

                if (cuisines.isEmpty) {
                  return const Text('No cuisines available');
                }

                final validCuisine = widget.cuisine != null &&
                        cuisines.any((c) => c.name == widget.cuisine)
                    ? widget.cuisine
                    : cuisines.first.name;

                return CustomDropdown<Cuisines>(
                  label: 'Cuisine',
                  value: validCuisine,
                  options: cuisines,
                  onChanged: (val) {
                    setState(() {
                      widget.cuisine = val;
                    });
                    final cuisine = cuisines.firstWhere(
                      (c) => c.name == val,
                      orElse: () => cuisines.first,
                    );
                    ref
                        .read(recipeMetadataProvider.notifier)
                        .updateCuisineId(cuisine.id!);
                  },
                  getDisplayString: (cuisine) => cuisine.name ?? 'Unknown',
                );
              }

              return const SizedBox.shrink();
            },
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            'Yield',
            'Amount/Unit',
            _yieldController,
            (val) {
              ref.read(recipeMetadataProvider.notifier).updateYield(val);
            },
            InputFormatType.text,
          ),
          SizedBox(height: 16.h),
          _buildTextField(
            'Servings', 'Servings', _servingsController, // Restrict to integers
            (val) {
              final servingValue = int.tryParse(val);
              if (servingValue != null) {
                ref
                    .read(recipeMetadataProvider.notifier)
                    .updateServings(servingValue);
              }
            },
            InputFormatType.number,
          ),
          TimePickerField(
            label: "Prep time",
            controller: _prepTimeController,
            onTimeSelected: (h, m) async {
              // Update prep time variables
              prepHours = h;
              prepMinutes = m;

              // Calculate prep time
              String prepTime = await _calculateTotalTime(); // Assuming this calculates based on h, m

              // Update provider and state
              setState(() {
                ref
                    .read(recipeMetadataProvider.notifier)
                    .updatePrepTime(prepTime);
                // Update total time if both prep and cook times are available
                _updateTotalTimeIfPossible();
              });
            },
          ),
          const SizedBox(height: 16),
          TimePickerField(
            label: "Cook time",
            controller: _cookTimeController,
            onTimeSelected: (h, m) async {
              // Update cook time variables
              cookHours = h;
              cookMinutes = m;

              // Calculate cook time
              String cookTime =
                  await _calculateTotalTime(); // Assuming this calculates based on h, m

              // Update provider and state
              setState(() {
                ref
                    .read(recipeMetadataProvider.notifier)
                    .updateCookTime(cookTime);
                // Update total time if both prep and cook times are available
                _updateTotalTimeIfPossible();
              });
            },
          ),
          const SizedBox(height: 16),
          SizedBox(height: 16.h),
          _buildTimeField('Total time', _totalTimeController, (val) {
            ref.read(recipeMetadataProvider.notifier).updateTotalTime(val);
          }),
          SizedBox(height: 30.h),
          Visibility(
            visible: widget.callFromUpdate??false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 60.0),
              child: CustomButton(
                text: "Save Changes",
                fontSize: 16,
                onPressed: () {
                  // Ensure ingredients are updated before notifying parent
                  widget. onUpdateData?.call();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
      String title,
      String label,
      TextEditingController controller,
      Function(String)? onChanged,
      InputFormatType formatType) {
    return Row(
      children: [
        SizedBox(
          width: 180.w,
          child: Text(
            title,
            style: context.theme.textTheme.displaySmall!.copyWith(
              color: context.theme.hintColor,
              fontSize: context.theme.textTheme.titleMedium!.fontSize,
            ),
          ),
        ),
        SizedBox(width: 10.h),
        Expanded(
          child: IngredientTextField(
              onChanged: onChanged,
              hintText: label,
              controller: controller,
              height: 60.h,
              formatType: formatType),
        ),
      ],
    );
  }

  Widget _buildTimeField(String label, TextEditingController controller,
      Function(String)? onChanged) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          width: 180.w,
          child: Text(
            label,
            style: context.theme.textTheme.displaySmall!.copyWith(
              color: context.theme.hintColor,
              fontSize: context.theme.textTheme.titleMedium!.fontSize,
            ),
          ),
        ),
        SizedBox(width: 10.w),
        Expanded(
          child: IngredientTextField(
            hintText: label,
            controller: controller,
            height: 70.h,
            isEnabled: false,
          ),
        ),
      ],
    );
  }
}
