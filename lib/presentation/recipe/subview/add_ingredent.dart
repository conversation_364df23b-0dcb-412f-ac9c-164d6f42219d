import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/providers/recipe/ingrident_provider.dart';
import '../../../core/widgets/custom_button.dart';
import 'custom_ingredent_textfield.dart';

class AddIngredientScreen extends ConsumerWidget {
  final bool isCallFromEdit;
  final VoidCallback? onUpdateData;

  AddIngredientScreen({
    super.key,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ingredientsNotifier = ref.watch(ingredientsProvider.notifier);
    final controllers = ingredientsNotifier.controllers;

    print("AddIngredientScreen: Controllers count: ${controllers.length}");
    print(
        "AddIngredientScreen: Ingredients state: ${ref.watch(ingredientsProvider)}");

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controllers.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                children: [
                  Expanded(
                    child: IngredientTextField(
                      controller: controllers[index],
                      hintText: 'Add ingredient',
                      autoFocus: true,
                      height: 70.h,
                      onChanged: (value) {
                        // Update provider on text change
                        ingredientsNotifier.setIngredients();
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        SizedBox(height: 10.h),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              flex: 1,
              child: CustomButton(
                text: "Add row",
                fontSize: 16,
                onPressed: () {
                  ingredientsNotifier.addController();
                },
              ),
            ),
            Visibility(
                visible: isCallFromEdit,
                child: SizedBox(width: 10.w)),
            Visibility(
              visible: isCallFromEdit,
              child: Expanded(
                flex: 1,
                child: CustomButton(
                  text: "Save Changes",
                  fontSize: 16,
                  onPressed: () {
                    // Ensure ingredients are updated before notifying parent
                    ingredientsNotifier.setIngredients();
                    onUpdateData?.call();
                  },
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}