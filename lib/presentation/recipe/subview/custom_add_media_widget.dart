import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/providers/recipe/media_provider.dart';
import '../../../core/widgets/custom_button.dart';
import 'custom_sub_media_widget.dart';

class MediaPickerGrid extends ConsumerStatefulWidget {
  final List<RecipeMedia>? recipeMedia;
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

    MediaPickerGrid({super.key, this.recipeMedia,  this.isCallFromEdit = false,
      this.onUpdateData,});

  @override
  ConsumerState<MediaPickerGrid> createState() => _MediaPickerGridState();
}

class _MediaPickerGridState extends ConsumerState<MediaPickerGrid> {
  List<RecipeMedia?> mediaFiles = List.generate(9, (_) => null); // 9 items
  int coverIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {

      // Initialize mediaFiles with backend data if available
      if (widget.recipeMedia != null && widget.recipeMedia!.isNotEmpty) {
        setState(() {
          for (int i = 0; i < widget.recipeMedia!.length && i < 9; i++) {
            mediaFiles[i] = widget.recipeMedia![i];
           }
        });
        ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
      }
     });
  }

  void _removeMedia(int index) {
    setState(() {
      mediaFiles[index] = null;
      if (coverIndex == index) coverIndex = 0;
    });
    ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
  }

  void _setAsCover(int index) {
    setState(() {
      coverIndex = index;
    });
  }

  Widget _buildMediaItem(int index) {
    final media = mediaFiles[index];
    final isCover = index == coverIndex;

    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isCover ? Colors.red : Colors.transparent,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
            image: media != null && (media.mediaFile != null || media.mediaUrl != null)
                ? DecorationImage(
              image: media.mediaFile != null
                  ? FileImage(media.mediaFile!)
                  : NetworkImage(media.mediaUrl!) as ImageProvider,
              fit: BoxFit.cover,

            )
                : DecorationImage(
              image: AssetImage(AssetsManager.cover_img),
              fit: BoxFit.cover,
            ),
          ),
        ),
        if (isCover)
          const Positioned(
            top: 4,
            left: 4,
            child: Text(
              "Cover",
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                backgroundColor: Colors.red,
              ),
            ),
          ),
        Positioned(
          bottom: 8,
          left: 8,
          right: 8,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _circleIcon(AssetsManager.edit_white, () => _removeMedia(index)),
              _circleIcon(AssetsManager.dlt_white, () => addImage(index)),
              _circleIcon(AssetsManager.swipe, () {}),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMediaSubImagesItem(int index) {
    return GestureDetector(
      onTap: () async {
        print('Opening picker for index $index');
        final files = await MediaPickerService.pickImages(allowMultiple: false);
        print('Files picked: ${files.length}');
        if (files.isNotEmpty) {
          setState(() {
            mediaFiles[index] = RecipeMedia(
              mediaFile: files.first,
              mediaType: 'image',
            );
            print("Added local media at index $index: ${mediaFiles[index]!.toJson()}");
          });
          ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
        }
      },
      child: CustomPaint(
        painter: DottedBorderPainter(),
        child: Container(
          height: 120.h,
          alignment: Alignment.center,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade300),
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.all(1.0),
              child: TextButton(
                onPressed: () async {
                  addImage(index);
                },
                child: Text(
                  "Add Image/Video",
                  style: context.textTheme.titleMedium?.copyWith(
                    color: AppColors.primaryLightTextColor,
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _circleIcon(String assetName, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: CircleAvatar(
        backgroundColor: AppColors.lightestGreyColor.withValues(alpha: .4),
        radius: 16,
        child: SvgPicture.asset(assetName),
      ),
    );
  }

  Future<void> addImage(int index) async {

    final files = await MediaPickerService.pickImages(allowMultiple: false);

    if (files.isNotEmpty) {
      setState(() {
        mediaFiles[index] = RecipeMedia(
          mediaFile: files.first,
        );
        ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
      });

    }
  }

  @override
  Widget build(BuildContext context) {
    print("Media files in build: ${mediaFiles.where((m) => m != null).length}");
    return Column(
      children: [
        GridView.builder(
          shrinkWrap: true,
          itemCount: 9,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemBuilder: (context, index) {
            print("Media at index $index: ${mediaFiles[index]?.toJson() ?? 'null'}");
            // Show _buildMediaItem if media exists for that index
            if (mediaFiles[index] != null) {
              return _buildMediaItem(index);
            } else {
              return _buildMediaSubImagesItem(index);
            }
          },
        ),
        SizedBox(height: 20.h),
        Visibility(
            visible: widget.isCallFromEdit??false,
            child: SizedBox(height: 20.h)),
        Visibility(
          visible: widget.isCallFromEdit??false,
          child: CustomButton(
            text: "Save Changes",
            fontSize: 16,
            width: 240,
            onPressed: () {
              // Ensure ingredients are updated before notifying parent
              widget. onUpdateData?.call();
            },
          ),
        ),
      ],
    );
  }
}