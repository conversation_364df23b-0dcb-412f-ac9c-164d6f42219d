import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/providers/recipe/media_provider.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/video_player_widget.dart';
import 'custom_sub_media_widget.dart';

class MediaPickerGrid extends ConsumerStatefulWidget {
  final List<RecipeMedia>? recipeMedia;
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

  MediaPickerGrid({
    super.key,
    this.recipeMedia,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  ConsumerState<MediaPickerGrid> createState() => _MediaPickerGridState();
}

class _MediaPickerGridState extends ConsumerState<MediaPickerGrid> {
  List<RecipeMedia?> mediaFiles = List.generate(9, (_) => null); // 9 items
  int coverIndex = 1; // Default cover to index 2

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.recipeMedia != null && widget.recipeMedia!.isNotEmpty) {
        setState(() {
          for (int i = 0; i < widget.recipeMedia!.length && i < 9; i++) {
            mediaFiles[i] = widget.recipeMedia![i];
          }
          // Ensure coverIndex is 2 if media exists at index 2, else find first non-null non-zero index
          if (mediaFiles[2] == null) {
            coverIndex = mediaFiles
                .asMap()
                .entries
                .firstWhere(
                  (entry) => entry.key != 0 && entry.value != null,
                  orElse: () => MapEntry(2, null),
                )
                .key;
          }
        });
        ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
      }
    });
  }

  void _removeMedia(int index) {
    setState(() {
      mediaFiles[index] = null;
      if (coverIndex == index) {
        // Set coverIndex to 2 if available, else find first non-null non-zero index
        coverIndex = mediaFiles[1] != null
            ? 1
            : mediaFiles
                .asMap()
                .entries
                .firstWhere(
                  (entry) => entry.key != 0 && entry.value != null,
                  orElse: () => MapEntry(2, null),
                )
                .key;
      }
    });
    ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
  }

  void _setAsCover(int index) {
    if (index != 0) {
      // Prevent setting cover to index 0 (video)
      setState(() {
        coverIndex = index;
      });
    }
  }

  Widget _buildMediaItem(int index) {
    final media = mediaFiles[index];
    final isCover = index == coverIndex;

     // For index 0: show video if present, else show add video view
    if (index == 0) {
      if (media != null && media.mediaType == 'VIDEO') {
        return Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: isCover ? Colors.red : Colors.transparent,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
                ),
                height: MediaQuery.of(context).size.height,
                child:ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: VideoPlayerWidget(
                    mediaFile: media.mediaFile,
                    mediaUrl: media.mediaUrl,
                  ),
                ),
              ),


            Visibility(
              visible: media.mediaUrl?.isNotEmpty ??
                  false || (media.mediaFile?.path.isNotEmpty ?? false),
              child: Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _circleIcon(
                        AssetsManager.edit_white, () => _removeMedia(index)),
                    _circleIcon(AssetsManager.dlt_white, () => addImage(index)),
                    _circleIcon(AssetsManager.swipe, () {}),
                  ],
                ),
              ),
            ),
          ],
        );
      } else {
        // Show add video view
        return _buildMediaSubImagesItem(0);
      }
    }

    // For other indices: show image if present, else show add image view
    //  if (media != null && media.mediaType == 'image') {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isCover ? Colors.red : Colors.transparent,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: media!.mediaFile != null
                  ? FileImage(media.mediaFile!)
                  : NetworkImage(media.mediaUrl!) as ImageProvider,
              fit: BoxFit.cover,
            ),
          ),
        ),
        if (isCover)
          const Positioned(
            top: 4,
            left: 4,
            child: Text(
              "Cover",
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                backgroundColor: Colors.red,
              ),
            ),
          ),
        Visibility(
          visible: index > 0,
          child: Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _circleIcon(AssetsManager.edit_white, () => _removeMedia(index)),
                _circleIcon(AssetsManager.dlt_white, () => addImage(index)),
                _circleIcon(AssetsManager.swipe, () => _setAsCover(index)),
                // Enable cover selection
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMediaSubImagesItem(int index) {
    return GestureDetector(
      onTap: () async {
        print('Opening picker for index $index');
        final files = await MediaPickerService.pickImages(
          allowMultiple: false,
          videoOnly: index == 0, // Restrict to videos for index 0
          allowVideo: index != 0, // Allow images and videos for other indices
        );
        print('Files picked: ${files.length}');
        if (files.isNotEmpty) {
          setState(() {
            mediaFiles[index] = RecipeMedia(
              mediaFile: files.first,
              mediaType: Utils().isVideo(files.first.path)?'video' : 'image'
            );
            // If adding media at index 2, set it as cover
            if (index == 1) {
              coverIndex = 1;
            }
          });
          ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
        }
      },
      child: CustomPaint(
        painter: DottedBorderPainter(),
        child: Container(
          height: 120.h,
          alignment: Alignment.center,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade300),
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.all(1.0),
              child: TextButton(
                onPressed: () async {
                  await addImage(index);
                },
                child: Text(
                  index == 0 ? "Add Video" : "Add Image",
                  style: context.textTheme.titleMedium?.copyWith(
                    color: AppColors.primaryLightTextColor,
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _circleIcon(String? assetName, VoidCallback onTap) {
    if (assetName == null || assetName.isEmpty) {
      // Log error and return a fallback widget
      print('Error: Invalid asset path in _circleIcon');
      return InkWell(
        onTap: onTap,
        child: CircleAvatar(
          backgroundColor: AppColors.lightestGreyColor.withValues(alpha: 0.4),
          radius: 16,
          child: Icon(Icons.error, color: Colors.red, size: 16), // Fallback icon
        ),
      );
    }
    return InkWell(
      onTap: onTap,
      child: CircleAvatar(
        backgroundColor: AppColors.lightestGreyColor.withValues(alpha: 0.4),
        radius: 16,
        child: SvgPicture.asset(
          assetName,
          width: 16,
          height: 16,
          placeholderBuilder: (context) => Icon(Icons.error, color: Colors.red, size: 16),
        ),
      ),
    );
  }

  Future<void> addImage(int index) async {
    final files = await MediaPickerService.pickImages(
      allowMultiple: false,
      videoOnly: index == 0, // Restrict to videos for index 0
      allowVideo: false, // Allow images and videos for other indices
    );

    if (files.isNotEmpty) {
      // Check file size for video (index 0)
      if (index == 0) {
        final file = files.first;
        final fileSize = await file.length();
        const maxSize = 150 * 1024 * 1024; // 150 MB in bytes

         if (fileSize > maxSize) {
          Utils().showFlushbar(context, message: 'Video file size should not exceed 150MB', isError: true);
          return;
        }
      }
      setState(() {
        mediaFiles[index] = RecipeMedia(
          mediaFile: files.first,
          mediaType: Utils().isVideo(files.first.path)?'video' : 'image'

        );
        // If adding media at index 1, set it as cover
        if (index == 1) {
          coverIndex = 1;
        }
      });
      ref.read(mediaFilesProvider.notifier).updateMedia(mediaFiles);
    }
  }


  @override
  Widget build(BuildContext context) {
     return Column(
      children: [
        GridView.builder(
          shrinkWrap: true,
          itemCount: 9,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemBuilder: (context, index) {
            print(
                "Media at index $index: ${mediaFiles[index]?.toJson() ?? 'null'}");
            if (mediaFiles[index] != null) {
              return _buildMediaItem(index);
            } else {
              return _buildMediaSubImagesItem(index);
            }
          },
        ),
        SizedBox(height: 20.h),
        Visibility(
          visible: widget.isCallFromEdit ?? false,
          child: SizedBox(height: 20.h),
        ),
        Visibility(
          visible: widget.isCallFromEdit ?? false,
          child: CustomButton(
            text: "Save Changes",
            fontSize: 16,
            width: 240,
            onPressed: () {
              widget.onUpdateData?.call();
            },
          ),
        ),
      ],
    );
  }
}
