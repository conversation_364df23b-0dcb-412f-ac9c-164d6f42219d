import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../core/providers/recipe/notes_provider.dart';
import 'custom_ingredent_textfield.dart';

class CustomNotesWidget extends ConsumerStatefulWidget {
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;
    CustomNotesWidget({super.key, this.title, this.isCallFromEdit = false,
      this.onUpdateData,});

  final String? title;

  @override
  ConsumerState<CustomNotesWidget> createState() => _CustomNotesWidgetState();
}

class _CustomNotesWidgetState extends ConsumerState<CustomNotesWidget> {
  late TextEditingController noteController;

  @override
  void initState() {
    super.initState();
    // Initialize noteController with value from notesProvider or widget.notes
    noteController = TextEditingController(
      text:  widget.title ?? '',
    );
  }

  @override
  void dispose() {
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          IngredientTextField(
            hintText: "Description",
            controller: noteController,
            maxLines: 7,
            height: 200.h,
            onChanged: (val) {
              ref.read(notesProvider.notifier).setNote(val);
            },
          ),
          SizedBox(height: 12.h),
          Visibility(
            visible: widget.isCallFromEdit??false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 50.0),
              child: CustomButton(
                text: "Save Changes",
                fontSize: 16,
                onPressed: () {
                  // Ensure ingredients are updated before notifying parent
                  widget. onUpdateData?.call();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}