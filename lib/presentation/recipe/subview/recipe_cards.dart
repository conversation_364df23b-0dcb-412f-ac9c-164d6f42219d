import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
 import 'package:mastercookai/core/widgets/custom_text_medium.dart';
import 'package:mastercookai/core/widgets/custom_text_title.dart';
 import '../../../../../core/widgets/custom_doted_lines.dart';
import '../../../../core/data/models/recipe_response.dart';
import '../../../../core/helpers/date_formatter.dart';
import '../../../../core/utils/device_utils.dart';
import '../../../../core/widgets/custom_network_image.dart';

class RecipeCardItem extends StatelessWidget {
  final Recipe recipes;
  final bool isSelected;

  const RecipeCardItem({
    super.key,
    required this.recipes,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: context.theme.cardColor,
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? const BorderSide(color: AppColors.selectionColor, width: 3)
            : BorderSide(color: AppColors.greyBorderColor, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 20.sp, horizontal: 20.sp),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // Ensures vertical centering
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Vertically Center the Image
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: (recipes.mediaUrl == null || recipes.mediaUrl!.isEmpty)
                      ? Image.asset(
                    AssetsManager.recipe_place_holder,
                    width: 120.w,
                    height: 130.h,
                    fit: BoxFit.cover,
                  )
                      : CustomNetworkImage(
                    imageUrl: recipes.mediaUrl!,
                    width: 120.w,
                    height: 130.h,
                    errorWidget: Image.asset(
                      AssetsManager.recipe_place_holder,
                      width: 120.w,
                      height: 130.h,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(width: 15.w),

            // Text and Action Section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomTextTitle(title: recipes.name),
                      ),
                      Row(
                        children: [
                          SvgPicture.asset(
                            AssetsManager.share,
                            height: 25.h,
                            width: 25.w,
                          ),
                          SizedBox(width: 14.w),
                          SvgPicture.asset(
                            AssetsManager.more,
                            height: 25.h,
                            width: 25.w,
                          ),
                        ],
                      ),
                    ],
                  ),

                  SizedBox(height: 10.h),

                  CustomTextMedium(
                    title: recipes.description ?? '',
                    maxLines: 2,
                  ),

                  SizedBox(height: 15.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 5.w),
                    child: CustomPaint(
                      painter: DottedLinePainter(
                        strokeWidth: 1,
                        dashWidth: 5,
                        color: AppColors.lightestGreyColor,
                      ),
                      size: Size(double.infinity, 2),
                    ),
                  ),

                  SizedBox(height: 15.h),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            AssetsManager.rating,
                            height: 24.h,
                            width: 24.w,
                          ),
                          SizedBox(width: 4.w),
                          CustomTextMedium(
                            title: "${recipes.reviewsCount} ( reviews)",
                            size: responsiveFont(20).sp,
                          ),
                        ],
                      ),
                      CustomTextMedium(
                        title: DateFormatter.timeAgo(recipes.dateAdded!),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );

  }
}
