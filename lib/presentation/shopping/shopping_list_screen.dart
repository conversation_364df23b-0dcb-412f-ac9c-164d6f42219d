import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/shopping/controllers/shopping_notifier.dart';
import 'package:mastercookai/presentation/shopping/sub_view/custom_tabview.dart';
import 'package:mastercookai/presentation/shopping/sub_view/pantry_view.dart';
import 'package:mastercookai/presentation/shopping/sub_view/shopping_view.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../core/providers/shopping/pantry_provider.dart';
import '../../core/providers/shopping/shopping_provider.dart';
import '../../core/providers/shopping/models/shopping_list_state.dart';
import '../shimer/Recipe_shimmer.dart';

class ShoppingScreen extends ConsumerStatefulWidget {
  const ShoppingScreen({super.key});

  @override
  ConsumerState<ShoppingScreen> createState() => _shoppingListScreenState();
}

class _shoppingListScreenState extends ConsumerState<ShoppingScreen> {
  final PageController _pageController = PageController();
  final TextEditingController searchController = TextEditingController();
  String selectedTab = 'Shopping list';
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 100), () {
        ref
            .read(shoppingNotifierProvider.notifier)
            .fetchShoppingLists(context: context);
        ref.read(pantryListNotifierProvider.notifier).fetchPantryLists();
      });
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    searchController.dispose();
    super.dispose();
  }

  void _handleTabChange(String tab) {
    if (_pageController.hasClients) {
      _pageController.jumpToPage(0);
    }
    setState(() {
      selectedTab = tab;
      _currentPage = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final pantryState = ref.watch(pantryListNotifierProvider);
    final shoppingState = ref.watch(shoppingNotifierProvider);

    // Note: We'll handle refresh directly in the delete operations

    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    final crossAxisCount = isHighRes ? 5 : 4;

    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              SizedBox(height: 40.h),
              CustomSearchBar(
                width: 500.w,
                controller: searchController,
              ),
              SizedBox(height: 60.h),
              CustomTabView(
                fontSize: 26.sp,
                tabs: ['Shopping list', 'Pantry'],
                selected: selectedTab,
                selectedTabColor: Colors.white,
                bgTabColor: AppColors.lightGreyColor,
                onChanged: _handleTabChange,
              ),
              SizedBox(height: 40.h),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: screenSize.width * 0.05,
                  ),
                  child: shoppingState.status == AppStatus.loading
                      ? RecipeShimmer(
                          crossAxisCount: crossAxisCount,
                          itemsPerPage: 8,
                        )
                      : shoppingState.status == AppStatus.error &&
                              shoppingState.data!.isEmpty
                          ? RecipeShimmer(
                              crossAxisCount: crossAxisCount,
                              itemsPerPage: 8,
                            )
                          : selectedTab == "Shopping list"
                              ? ShoppingView(
                                  shoppingState: shoppingState,
                                  screenSize: screenSize,
                                  isHighRes: isHighRes,
                  )
                              : PantryView(
                                  pantryState: pantryState,
                                  screenSize: screenSize,
                                  isHighRes: isHighRes,
                                ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
