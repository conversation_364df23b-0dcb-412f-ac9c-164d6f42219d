import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/controllers/shopping_notifier.dart';
import 'package:mastercookai/core/providers/shopping/page_state_provider.dart';
import 'package:mastercookai/presentation/shopping/sub_view/shopping_card.dart';
import 'package:mastercookai/presentation/shopping/sub_view/update_shopping_dialog.dart';
import '../../../core/data/models/shopping.dart';
import '../../../core/data/models/shopping_response.dart';
import '../../../core/network/app_status.dart';
// Removed unused import
import '../../../core/utils/Utils.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/uploading_dialog.dart';
import '../../../core/widgets/no_data_widget.dart';
import '../../cookbook/widgets/create_cookbook_card.dart';
import '../../cookbook/widgets/shopping_dialog.dart';

class ShoppingView extends ConsumerStatefulWidget {
  final AppState<List<ShoppingLists>> shoppingState;
  final Size screenSize;
  final bool isHighRes;
  final String? searchQuery; // Add search query parameter

  const ShoppingView({
    Key? key,
    required this.shoppingState,
    required this.screenSize,
    required this.isHighRes,
    this.searchQuery,
  }) : super(key: key);

  @override
  ConsumerState<ShoppingView> createState() => _ShoppingViewState();
}

class _ShoppingViewState extends ConsumerState<ShoppingView> {
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    // Initialize page controller with the current page from provider
    final currentPage = ref.read(pageStateProvider).shoppingCurrentPage;
    _pageController = PageController(initialPage: currentPage);

    // Ensure the page controller navigates to the correct page after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients && currentPage > 0) {
        _pageController.animateToPage(
          currentPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shoppingState = widget.shoppingState;
    final screenSize = widget.screenSize;
    final isHighRes = widget.isHighRes;
    final pageState = ref.watch(pageStateProvider);
    final currentPage = pageState.shoppingCurrentPage;

    if (shoppingState.status == AppStatus.error &&
        shoppingState.data!.isEmpty) {
      return Center(child: Text('Error: ${shoppingState.errorMessage}'));
    } else if (shoppingState.data?.isNotEmpty??false) {
      final width = MediaQuery.of(context).size.width;
      final crossAxisCount = ScreenSizer().calculateCrossAxisCount(width);

      final itemsPerPage = crossAxisCount * 2;
      final totalItems = shoppingState.data!.length + 1;
      final totalPages = (totalItems / itemsPerPage).ceil();

      return NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification) {
            final metrics = scrollNotification.metrics;
            if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
                shoppingState.hasMore &&
                shoppingState.status != AppStatus.loadingMore) {
              ref
                  .read(shoppingNotifierProvider.notifier)
                  .fetchShoppingLists(loadMore: true, context: context);
            }
          }
          return false;
        },
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  child: Listener(
                    onPointerSignal: (pointerSignal) {
                      if (pointerSignal is PointerScrollEvent) {
                        if (pointerSignal.scrollDelta.dx != 0 || pointerSignal.scrollDelta.dy != 0) {
                          final delta = pointerSignal.scrollDelta.dx != 0
                              ? pointerSignal.scrollDelta.dx
                              : pointerSignal.scrollDelta.dy;
                          if (delta > 0 && currentPage < (shoppingState.hasMore ? totalPages : totalPages - 1)) {
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          } else if (delta < 0 && currentPage > 0) {
                            _pageController.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        }
                      }
                    },
                    child: PageView.builder(
                      controller: _pageController,
                      onPageChanged: (int page) {
                        // Update the page state in the provider
                        ref.read(pageStateProvider.notifier).updateShoppingPage(page);
                        if (page == totalPages - 1 && shoppingState.hasMore) {
                          ref
                              .read(shoppingNotifierProvider.notifier)
                              .fetchShoppingLists(loadMore: true, context: context);
                        }
                      },
                    itemCount:
                        shoppingState.hasMore ? totalPages + 1 : totalPages,
                    itemBuilder: (context, pageIndex) {
                      if (pageIndex >= totalPages && shoppingState.hasMore) {
                        return Center(
                          child: Center(
                            child: LoadingAnimationWidget.fallingDot(
                              color: Colors.white,
                              size: 50.0,
                            ),
                          ),
                        );
                      }

                      final startIndex = pageIndex * itemsPerPage;
                      var endIndex = startIndex + itemsPerPage;
                      if (endIndex > totalItems) endIndex = totalItems;

                      return LayoutBuilder(
                        builder: (context, constraints) {
                          final size = MediaQuery.of(context).size;
                          final aspectRatio = ScreenSizer()
                              .calculateShoppingSize(size.width, size.height);
                          return GridView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount:
                                  ScreenSizer().calculateCrossAxisCount(width),
                              mainAxisSpacing: screenSize.height * 0.01,
                              crossAxisSpacing: screenSize.width * 0.01,
                              childAspectRatio: aspectRatio,
                            ),
                            itemCount: endIndex - startIndex,
                            itemBuilder: (context, index) {
                              final itemIndex = startIndex + index;

                              if (index == itemsPerPage - 1 &&
                                  shoppingState.status ==
                                      AppStatus.loadingMore) {
                                return Center(
                                  child: Center(
                                    child: LoadingAnimationWidget.fallingDot(
                                      color: Colors.white,
                                      size: 50.0,
                                    ),
                                  ),
                                );
                              }

                              if (itemIndex == 0) {
                                return ImportCreateCard(
                                  importText: "Import Shopping List",
                                  title: "Create Shopping List",
                                  isRecipe: false,
                                  onImport: () =>   showTopRightDialog(context),
                                  onCreate: () =>
                                      showCookbookDialog(context, ref),
                                );
                              }

                              final shoppingIndex = itemIndex - 1;
                              if (shoppingIndex < shoppingState.data!.length) {
                                final shoppingList =
                                    shoppingState.data![shoppingIndex];
                                final shopping = Shopping(
                                  id: shoppingList.id!.toInt(),
                                  title: Utils().capitalizeFirstLetter(
                                      shoppingList.name!),
                                  imageUrl: shoppingList.coverImageUrl ?? '',
                                  recipeCount: shoppingList.shoppingItemsCount
                                      .toString(),
                                  createdDate:
                                      shoppingList.dateCreated.toString(),
                                );
                                return ShoppingCard(
                                  shopping: shopping,
                                  isHighRes: isHighRes,
                                  onTap: () {
                                    ref
                                        .read(shoppingNotifierProvider.notifier)
                                        .resetToIdle();
                                    showUpdateShoppingDialog(
                                      context,
                                      ref,
                                      shoppingId: shoppingState.data![shoppingIndex].id.toString(),
                                      shoppingName: shoppingState.data![shoppingIndex].name,
                                    );
                                  },
                                );
                              }

                              return const SizedBox.shrink();
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
                ),
              ],
            ),
            if (totalPages > 1)
              Positioned(
                left: 0,
                right: 0,
                bottom: screenSize.height * 0.20,
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: List<Widget>.generate(
                      shoppingState.hasMore ? totalPages + 1 : totalPages,
                      (int index) {
                        if (index >= totalPages) {
                          return const SizedBox.shrink();
                        }
                        return Container(
                          width: screenSize.width * 0.008,
                          height: screenSize.width * 0.008,
                          margin: EdgeInsets.symmetric(
                            horizontal: screenSize.width * 0.004,
                          ),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: currentPage == index
                                ? context.theme.scaffoldBackgroundColor
                                : context.theme.scaffoldBackgroundColor
                                    .withValues(alpha: .2),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    } else if ( shoppingState.data?.isEmpty??false) {
      // Check if this is a search result with no data
      if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) {
        // Show animated no data for search results
        return const NoDataWidget(
          title: "No Shopping Lists Found",
          subtitle: "Try adjusting your search terms or create a new shopping list",
          width: 250,
          height: 250,
        );
      } else {
        // Show create card at index 0 when list is empty (no search)
        final width = MediaQuery.of(context).size.width;
        final crossAxisCount = ScreenSizer().calculateCrossAxisCount(width);

        return LayoutBuilder(
          builder: (context, constraints) {
            final size = MediaQuery.of(context).size;
            final aspectRatio = ScreenSizer()
                .calculateShoppingSize(size.width, size.height);
            return GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: screenSize.height * 0.01,
                crossAxisSpacing: screenSize.width * 0.01,
                childAspectRatio: aspectRatio,
              ),
              itemCount: 1, // Only show the create card
              itemBuilder: (context, index) {
                return ImportCreateCard(
                  importText: "Import Shopping List",
                  title: "Create Shopping List",
                  isRecipe: false,
                  onImport: () => showTopRightDialog(context),
                  onCreate: () => showCookbookDialog(context, ref),
                );
              },
            );
          },
        );
      }
    } else {
      // Show a loading indicator or nothing during initial load
      return const SizedBox.shrink();
    }
  }

  void showCookbookDialog(BuildContext context, WidgetRef ref) {
    // Reset the provider state before showing dialog
    ref.read(isShoppingListCreatedProvider.notifier).state = false;
    ref.read(shoppingItemProvider.notifier).state = '';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => ShoppingDialog(
        dialogType: DialogType.shopping,
        title: 'Create Shopping List',
        hintText: 'Enter shopping item name',
        successText: 'Shopping list created\nsuccessfully!',
        buttonText: 'Create now',
        successButtonText: 'Go to Shopping list',
      ),
    );
  }


  //Uploading PopUo open At Right
  void showTopRightDialog(BuildContext context) {
    final overlay = Overlay.of(context);
    OverlayEntry? overlayEntry; // Declare nullable

    overlayEntry = OverlayEntry(
      builder: (_) => Positioned(
        top: 40.h,
        right: 20.w,
        child: Material(
          color: Colors.transparent,
          child: UploadingDialog(
            title: "Shopping list is uploading...",
            subtitle: "Fruits",
            progress: 0.54,
            onCancel: () {
              overlayEntry?.remove(); // Use nullable-safe access
            },
            onClose: () {
              overlayEntry?.remove();
            },
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);
  }

}
