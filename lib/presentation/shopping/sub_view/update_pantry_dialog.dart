import 'package:mastercookai/presentation/cookbook/widgets/shopping_dialog.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/providers/shopping/pantry_provider.dart';

void showUpdatePantryDialog(BuildContext context, WidgetRef ref,{String? pantryId, String? pantryName}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => ShoppingDialog(
      dialogType: DialogType.pantry,
      title: "Edit Pantry",
      hintText: "Edit Pantry Name",
      successText: "Pantry Updated \nSuccessfully",
      buttonText: "Update",
      successButtonText: "Done",
      shoppingId: pantryId,
      shoppingName: pantryName,
      callFromUpdate: true,
    ),
  ).then((_) {
    // Refresh pantry lists when dialog closes
    ref.read(pantryListNotifierProvider.notifier).fetchPantryLists();
  });
}