import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/controllers/shopping_notifier.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:mastercookai/core/widgets/custom_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../../../core/widgets/custom_button.dart';
import '../../../../core/data/models/shopping.dart';
import '../../../core/providers/shopping/models/shopping_list_state.dart';
import '../../../core/providers/shopping/shopping_provider.dart';
// Removed unused import
import '../../../core/utils/Utils.dart';



class ShoppingCard extends ConsumerWidget {
  final Shopping shopping;
  final bool isHighRes;
  final VoidCallback onTap;

  const ShoppingCard({
    super.key,
    required this.shopping,
    required this.isHighRes,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;
    final shoppingListNotifier =
        ref.read(shoppingListNotifierProvider.notifier);
    final shoppingNotifier = ref.read(shoppingNotifierProvider.notifier);
    final shoppingListState = ref.watch(shoppingNotifierProvider);

    // Show loading indicator when deleting
    final isDeleting = shoppingListState.status == AppStatus.loading;

    return Card(
      color: context.theme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(10),
        onTap: () {
          if (!isDeleting) {
            context.go('/shopping/shoppingDetail');
          }
        },
        child: Padding(
          padding: EdgeInsets.only(
              left: screenSize.width * 0.005,
              right: screenSize.width * 0.005,
              top: screenSize.width * 0.005),
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [

                  Stack(
                    children: [
                      ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(15),
                            bottom: Radius.circular(15),
                          ),
                          child: CommonImage(imageSource: shopping.imageUrl,
                              width: screenSize.width,
                              fit: BoxFit.cover,
                              placeholder: AssetsManager.shoppingDummy,
                              height: ScreenSizer().calculateImageHeight(context))

                          ),
                      Align(
                        alignment: Alignment.topRight,
                        child: IconButton(
                            onPressed: () async {
                              shoppingNotifier.pickImage(context, ref, shopping.id.toString(), shopping.title);
                            },
                            icon: Icon(Icons.camera_alt_rounded)),
                      )
                    ],
                  ),
                  SizedBox(height: 16.h),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              shopping.title,
                              style:
                                  context.theme.textTheme.bodyMedium!.copyWith(
                                color: AppColors.primaryGreyColor,
                                fontSize: responsiveFont(28).sp,
                                fontWeight: FontWeight.w400,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Row(
                            children: [
                              // Share button with GestureDetector
                              GestureDetector(
                                onTap: onTap,
                                child: Padding(
                                  padding: EdgeInsets.all(
                                      0.w), // Adjust padding as needed
                                  child: SvgPicture.asset(
                                    AssetsManager.edit,
                                    height: 30.h,
                                    width: 30.w,
                                  ),
                                ),
                              ),
                               SizedBox(width: 8.w), // Reduced space between buttons

                              // More button with GestureDetector
                              GestureDetector(
                                onTap: () async {
                                  if (!isDeleting) {
                                    final currentContext = context; // Store context before async operation

                                    // Show confirmation dialog
                                    final bool? confirmed = await Utils().showCommonConfirmDialog(
                                      context: currentContext,
                                      title: 'Delete Shopping List',
                                      subtitle: 'Are you sure you want to delete "${shopping.title}" shopping list?',
                                      confirmText: 'Delete',
                                      cancelText: 'Cancel',
                                    );

                                    if (confirmed != true) {
                                      return; // User cancelled deletion
                                    }

                                    final result = await shoppingListNotifier
                                        .deleteShoppingList(
                                            shopping.id.toString());
                                    if (result.$1 ==
                                        ShoppingListStatus.success) {
                                      // Refresh the shopping list screen
                                      if (currentContext.mounted) {
                                        shoppingNotifier.fetchShoppingLists(context: currentContext);

                                        // ScaffoldMessenger.of(currentContext)
                                        //     .showSnackBar(
                                        //   SnackBar(
                                        //     content: Text(
                                        //         'Shopping list deleted successfully'),
                                        //     backgroundColor: Colors.green,
                                        //   ),
                                        // );
                                      }
                                    } else {
                                      if (currentContext.mounted) {
                                        // ScaffoldMessenger.of(currentContext)
                                        //     .showSnackBar(
                                        //   SnackBar(
                                        //     content: Text(result.$2 ??
                                        //         'Failed to delete shopping list'),
                                        //     backgroundColor: Colors.red,
                                        //   ),
                                        // );
                                      }
                                    }
                                  }
                                },
                                child: Padding(
                                  padding: EdgeInsets.all(
                                      0.w), // Adjust padding as needed
                                  child: SvgPicture.asset(
                                    AssetsManager.dlt_recipe,
                                    height: 30.h,
                                    width: 30.w,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('${shopping.recipeCount} products',
                              style:
                                  context.theme.textTheme.labelSmall!.copyWith(
                                color: AppColors.textGreyColor,
                                fontSize: 22.sp,
                                fontWeight: FontWeight.w400,
                              )),
                          Text(
                              'Created : ${timeago.format(DateTime.parse(shopping.createdDate), locale: 'en_short')}',
                              style:
                                  context.theme.textTheme.labelSmall!.copyWith(
                                color: AppColors.textGreyColor,
                                fontSize: 22.sp,
                                fontWeight: FontWeight.w400,
                              )),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      CustomButton(
                        text: 'Open Shopping List',
                        fontSize: 22.sp,
                        onPressed: () {
                          if (!isDeleting) {
                            context.go('/shopping/shoppingDetail');
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
              // if (isDeleting)
              //   Positioned.fill(
              //     child: Container(
              //       color: Colors.black.withOpacity(0.5),
              //       child: const Center(
              //         child: CircularProgressIndicator(),
              //       ),
              //     ),
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  double calculateImageHeight(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final aspectRatio = size.width / size.height;

    // Specifically handle 1920px width
    if (size.width == 1920) {
      if (size.width >= 1060) {
        return 240.h; // Works for 1920x1080 and similar
      } else {
        return 220.h; // Safer ratio for shorter height like 1006
      }
    }

    if (aspectRatio >= 2.0) {
      // Ultra-wide screens (e.g., 3440×1440, 3840×1600)
      return 240.h; // Works for 1920x1080 and similar
    } else if (aspectRatio >= 1.8) {
      // 16:9 and wider (e.g., 1920×1080, 2560×1440)
      //and even 1006
      return 200.h;
      //1920 1006
    } else if (aspectRatio >= 1.7) {
      return 200.h; // Covers 1920x1080
    } else if (aspectRatio >= 1.4) {
      // Retina/HiDPI screens (e.g., 2880×1800, 3584×2240)
      return 200.h;
    } else if (aspectRatio >= 1.2) {
      // Taller screens
      return 200.h;
    } else {
      // Very tall or square-ish screens
      return 200.h;
    }
  }
}
