import 'package:flutter/gestures.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/controllers/pantry_notifier.dart';
import 'package:mastercookai/core/providers/shopping/page_state_provider.dart';
import 'package:mastercookai/presentation/shopping/sub_view/pantry_card.dart';
import 'package:mastercookai/presentation/shopping/sub_view/update_pantry_dialog.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/pantry.dart';
import '../../../core/providers/pentry_provider.dart';
import '../../../core/providers/shopping/models/pantry_list_state.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/uploading_dialog.dart';
import '../../../core/widgets/no_data_widget.dart';
import '../../cookbook/widgets/create_cookbook_card.dart';
import '../../cookbook/widgets/shopping_dialog.dart';
import '../../shimer/Recipe_shimmer.dart';

class PantryView extends ConsumerStatefulWidget {
  final GetPantryListState pantryState;
  final Size screenSize;
  final bool isHighRes;
  final String? searchQuery; // Add search query parameter

  const PantryView({
    Key? key,
    required this.pantryState,
    required this.screenSize,
    required this.isHighRes,
    this.searchQuery,
  }) : super(key: key);

  @override
  ConsumerState<PantryView> createState() => _PantryViewState();
}

class _PantryViewState extends ConsumerState<PantryView> with AutomaticKeepAliveClientMixin {
  late final PageController _pageController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Initialize page controller with the current page from provider
    final currentPage = ref.read(pageStateProvider).pantryCurrentPage;
    _pageController = PageController(initialPage: currentPage);

    // Ensure the page controller navigates to the correct page after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients) {
        // Always jump to the current page to ensure sync
        _pageController.jumpToPage(currentPage);
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final pantryState = widget.pantryState;
    final screenSize = widget.screenSize;
    final isHighRes = widget.isHighRes;
    final pageState = ref.watch(pageStateProvider);
    final currentPage = pageState.pantryCurrentPage;

    // Ensure PageController is synchronized with the current page state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients && _pageController.page?.round() != currentPage) {
        _pageController.jumpToPage(currentPage);
      }
    });

    // Show shimmer when loading
    if (pantryState.status == PantryListStatus.loading &&
        pantryState.pantryLists.isEmpty) {
      final width = MediaQuery.of(context).size.width;
      final crossAxisCount = ScreenSizer().calculateCrossAxisCount(width);
      return RecipeShimmer(
        crossAxisCount: crossAxisCount,
        itemsPerPage: 8,
      );
    }

    if (pantryState.status == PantryListStatus.error &&
        pantryState.pantryLists.isEmpty) {
      return Center(child: Text('Error: ${pantryState.errorMessage}'));
    } else if (pantryState.pantryLists.isNotEmpty) {
      final width = MediaQuery.of(context).size.width;
      final crossAxisCount = ScreenSizer().calculateCrossAxisCount(width);

      final itemsPerPage = crossAxisCount * 2;
      final totalItems = pantryState.pantryLists.length + 1;
      final totalPages = (totalItems / itemsPerPage).ceil();

      return NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification) {
            final metrics = scrollNotification.metrics;
            if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
                pantryState.hasMore &&
                pantryState.status != PantryListStatus.loadingMore) {
              ref
                  .read(pantryListNotifierProvider.notifier)
                  .fetchPantryLists(loadMore: true);
            }
          }
          return false;
        },
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  child: Listener(
                    onPointerSignal: (pointerSignal) {
                      if (pointerSignal is PointerScrollEvent) {
                        if (pointerSignal.scrollDelta.dx != 0 || pointerSignal.scrollDelta.dy != 0) {
                          final delta = pointerSignal.scrollDelta.dx != 0
                              ? pointerSignal.scrollDelta.dx
                              : pointerSignal.scrollDelta.dy;
                          if (delta > 0 && currentPage < (pantryState.hasMore ? totalPages : totalPages - 1)) {
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          } else if (delta < 0 && currentPage > 0) {
                            _pageController.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        }
                      }
                    },
                    child: PageView.builder(
                      key: ValueKey('pantry_pageview_${pantryState.pantryLists.length}'),
                      controller: _pageController,
                      onPageChanged: (int page) {
                        // Update the page state in the provider
                        ref.read(pageStateProvider.notifier).updatePantryPage(page);
                        if (page == totalPages - 1 && pantryState.hasMore) {
                          ref
                              .read(pantryListNotifierProvider.notifier)
                              .fetchPantryLists(loadMore: true);
                        }
                      },
                    itemCount:
                        pantryState.hasMore ? totalPages + 1 : totalPages,
                    itemBuilder: (context, pageIndex) {
                      if (pageIndex >= totalPages && pantryState.hasMore) {
                        return Center(
                          child: Center(
                            child: LoadingAnimationWidget.fallingDot(
                              color: Colors.white,
                              size: 50.0,
                            ),
                          ),
                        );
                      }

                      final startIndex = pageIndex * itemsPerPage;
                      var endIndex = startIndex + itemsPerPage;
                      if (endIndex > totalItems) endIndex = totalItems;

                      return LayoutBuilder(
                        builder: (context, constraints) {
                          final size = MediaQuery.of(context).size;
                          final aspectRatio = ScreenSizer()
                              .calculateShoppingSize(size.width, size.height);
                          return GridView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount:
                                  ScreenSizer().calculateCrossAxisCount(width),
                              mainAxisSpacing: screenSize.height * 0.01,
                              crossAxisSpacing: screenSize.width * 0.01,
                              childAspectRatio: aspectRatio,
                            ),
                            itemCount: endIndex - startIndex,
                            itemBuilder: (context, index) {
                              final itemIndex = startIndex + index;

                              if (index == itemsPerPage - 1 &&
                                  pantryState.status ==
                                      PantryListStatus.loadingMore) {
                                return Center(
                                  child: Center(
                                    child: LoadingAnimationWidget.fallingDot(
                                      color: Colors.white,
                                      size: 50.0,
                                    ),
                                  ),
                                );
                              }

                              if (itemIndex == 0) {
                                return ImportCreateCard(
                                  importText: "Import Pantry",
                                  title: "Add New Pantry",
                                  isRecipe: false,
                                  onImport: () =>  showTopRightDialog(context),
                                  onCreate: () =>
                                      showPantryDialog(context, ref),
                                );
                              }

                              final pantryIndex = itemIndex - 1;
                              if (pantryIndex <
                                  pantryState.pantryLists.length) {
                                final pantryList =
                                    pantryState.pantryLists[pantryIndex];
                                final pantry = Pantry(
                                  id: pantryList.id,
                                  title: pantryList.name,
                                  imageUrl: pantryList.coverImageUrl ?? '',
                                  recipeCount: pantryList.pantryItemCount,
                                  createdDate:
                                      pantryList.dateCreated.toString(),
                                );
                                return PantryCard(
                                  pantry: pantry,
                                  isHighRes: isHighRes,
                                  onTap: () {
                                    ref
                                        .read(pantryNotifierProvider.notifier)
                                        .resetToIdle();
                                    showUpdatePantryDialog(
                                      context,
                                      ref,
                                      pantryId: pantryList.id.toString(),
                                      pantryName: pantryList.name,
                                    );
                                  },
                                );
                              }

                              return const SizedBox.shrink();
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
                ),
              ],
            ),
            if (totalPages > 1)
              Positioned(
                left: 0,
                right: 0,
                bottom: screenSize.height * 0.20,
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: List<Widget>.generate(
                      pantryState.hasMore ? totalPages + 1 : totalPages,
                      (int index) {
                        if (index >= totalPages) {
                          return const SizedBox.shrink();
                        }
                        return Container(
                          width: screenSize.width * 0.008,
                          height: screenSize.width * 0.008,
                          margin: EdgeInsets.symmetric(
                            horizontal: screenSize.width * 0.004,
                          ),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: currentPage == index
                                ? context.theme.scaffoldBackgroundColor
                                : context.theme.scaffoldBackgroundColor
                                    .withValues(alpha: .2),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    } else if (pantryState.status != PantryListStatus.idle &&
        pantryState.pantryLists.isEmpty) {
      // Check if this is a search result with no data
      if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) {
        // Show animated no data for search results
        return const NoDataWidget(
          title: "No Pantry Lists Found",
          subtitle: "Try adjusting your search terms or create a new pantry list",
          width: 250,
          height: 250,
        );
      } else {
        // Show create card at index 0 when list is empty (no search)
        final width = MediaQuery.of(context).size.width;
        final crossAxisCount = ScreenSizer().calculateCrossAxisCount(width);

        return LayoutBuilder(
          builder: (context, constraints) {
            final size = MediaQuery.of(context).size;
            final aspectRatio = ScreenSizer()
                .calculateShoppingSize(size.width, size.height);
            return GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: screenSize.height * 0.01,
                crossAxisSpacing: screenSize.width * 0.01,
                childAspectRatio: aspectRatio,
              ),
              itemCount: 1, // Only show the create card
              itemBuilder: (context, index) {
              return ImportCreateCard(
                importText: "Import Pantry",
                title: "Add New Pantry",
                isRecipe: false,
                onImport: () => showTopRightDialog(context),
                onCreate: () => showPantryDialog(context, ref),
              );
            },
          );
        },
      );
      }
    } else {
      return const SizedBox.shrink();
    }
  }

  void showPantryDialog(BuildContext context, WidgetRef ref) {
    // Reset the provider state before showing dialog
    ref.read(isShoppingListCreatedProvider.notifier).state = false;
    ref.read(shoppingItemProvider.notifier).state = '';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => ShoppingDialog(
        dialogType: DialogType.pantry,
        title: 'Create Pantry List',
        hintText: 'Enter Pantry name',
        successText: 'Pantry list created\nsuccessfully!',
        buttonText: 'Create now',
        successButtonText: 'Go to Pantry list',
      ),
    );
  }


  //Uploading PopUo open At Right
  void showTopRightDialog(BuildContext context) {
    final overlay = Overlay.of(context);
    OverlayEntry? overlayEntry; // Declare nullable

    overlayEntry = OverlayEntry(
      builder: (_) => Positioned(
        top: 40.h,
        right: 20.w,
        child: Material(
          color: Colors.transparent,
          child: UploadingDialog(
            title: "Pantry list is uploading...",
            subtitle: "Fruits",
            progress: 0.54,
            onCancel: () {
              overlayEntry?.remove(); // Use nullable-safe access
            },
            onClose: () {
              overlayEntry?.remove();
            },
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);
  }

}
