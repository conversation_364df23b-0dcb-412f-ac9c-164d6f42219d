import 'package:mastercookai/presentation/cookbook/widgets/shopping_dialog.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/providers/shopping/controllers/shopping_notifier.dart';

void showUpdateShoppingDialog(BuildContext context, WidgetRef ref,{String? shoppingId, String? shoppingName}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => ShoppingDialog(
      dialogType: DialogType.shopping,
      title: "Edit Shopping",
      hintText: "Edit Shopping Name",
      successText: "Shopping Updated \nSuccessfully",
      buttonText: "Update",
      successButtonText: "Done",
      shoppingId: shoppingId,
      shoppingName: shoppingName,
      callFromUpdate: true,
    ),
  ).then((_) {
    // Refresh shopping lists when dialog closes (without context to avoid async gap issues)
    ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists();
  });
}