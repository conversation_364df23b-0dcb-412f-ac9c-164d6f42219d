// import 'package:dio/dio.dart';
// import '../../../core/data/models/category_model.dart';
// import '../../../core/data/models/category_response_model.dart';
// import '../../../core/data/request_query/create_recipe_request.dart';
// import '../../../core/data/models/create_recipe_response.dart';
// import '../../../core/data/models/cuisines_response_model.dart';
// import '../../../core/data/models/recipe_delete_response.dart';
// import '../../../core/data/models/recipe_response.dart';
// import '../../../core/data/models/recipes.dart';
// import '../../../core/data/recipe/recipe_api.dart';
//
//
// class RecipeRepository {
//   final RecipeApi api;
//
//   RecipeRepository(this.api);
//
//   Future<List<Category>> getCategories() async {
//     final response = await api.fetchCategories();
//
//     if (response.statusCode == 200) {
//       final model = CategoryResponse.fromJson(response.data);
//       return model.data?.categories ?? [];
//     } else {
//       throw Exception('Failed to load categories');
//     }
//   }
//
//   Future<List<Category>> getCusines() async {
//     final response = await api.fetchCusines();
//
//     if (response.statusCode == 200) {
//       final model = CusinesResponse.fromJson(response.data);
//       return model.data?.cusines ?? [];
//     } else {
//       throw Exception('Failed to load cusines');
//     }
//   }
//
//   Future<CreateRecipeResponse> createRecipe(CreateRecipeRequest request, int cookbookId) {
//     return api.createRecipe(request, cookbookId);
//   }
//
//   Future<RecipeResponse> getRecipes({int page = 1, int pageSize = 10 , required int cookbookId}) {
//     return api.fetchRecipes(page: page, pageSize: pageSize , cookbookId: cookbookId);
//   }
//
//
//   Future<RecipesResponse> getRecipe({ required int cookbookId , required int recipeId}) {
//     return api.fetchRecipe( cookbookId: cookbookId , recipeId: recipeId);
//   }
//
//   Future<RecipeDeleteResponse> deleteRecipe({ required int cookbookId , required int recipeId}) {
//     return api.deleteRecipe( cookbookId: cookbookId , recipeId: recipeId);
//   }
// }
//
//
