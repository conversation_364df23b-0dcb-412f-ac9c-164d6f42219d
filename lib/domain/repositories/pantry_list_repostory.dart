
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/data/models/pantry.dart';
import '../../core/data/pantry_list_api.dart';

final pantryListRepositoryProvider = Provider<PantryListRepository>((ref) {
  final postApi = ref.watch(PostPantryListApiProvider);
  final getApi = ref.watch(GetPantryListApiProvider);
  final getPantryListItemsApi = ref.watch(GetPantryListItemsApiProvider);
  final deleteApi = ref.watch(DeletePantryListApiProvider);
  return PantryListRepository(postApi, getApi, getPantryListItemsApi, deleteApi);
});

class PantryListRepository {
  final PostPantryListApi postApi;
  final GetPantryListApi getApi;
  final GetPantryListItemsApi getPantryListItemsApi;
  final DeletePantryListApi deleteApi;

  PantryListRepository(this.postApi, this.getApi, this.getPantryListItemsApi, this.deleteApi);

  // POST
  Future<PostPantryListResponse> createPantryList(String name) async {
    final response = await postApi.PantryList(name);
    if (response.statusCode == 200) {
      return PostPantryListResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to create pantry list');
    }
  }

  // GET
  Future<GetPantryListResponse> fetchPantryLists({
    required int pageNumber,
    required int pageSize,
    bool includeShoppingItems = true,
    String itemSort = 'Newest',
    String? search,
  }) async {
    final response = await getApi.fetchPantryLists(
      pageNumber: pageNumber,
      pageSize: pageSize,
      includePantryItems: includeShoppingItems,
      itemSort: itemSort,
      search: search,
    );
    return response;
  }

  // GET SHOPPING LISTS ITEMS
  Future<PantryListItemResponse> fetchPantryListsItems({
    required int id,
    required int pageNumber,
    required int pageSize,
  }) async {
    final response = await getPantryListItemsApi.fetcPantryListsItems(
      id: id,
      pageNumber: pageNumber,
      pageSize: pageSize,
    );
    return response;
  }

// DELETE
  Future<DeletePantryListResponse> deletePantryList(String id) async {
    final response = await deleteApi.deletePantryList(id);
    if (response.statusCode == 200) {
      return DeletePantryListResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to delete pantry list');
    }
  }
}