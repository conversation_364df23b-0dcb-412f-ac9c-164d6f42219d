
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/data/models/shopping.dart';
import '../../core/data/shopping_list_api.dart';

final shoppingListRepositoryProvider = Provider<ShoppingListRepository>((ref) {
  final postApi = ref.watch(PostShoppingListApiProvider);
  final getApi = ref.watch(GetShoppingListApiProvider);
  final getShoppingListItemsApi = ref.watch(GetShoppingListItemsApiProvider);
  final deleteApi = ref.watch(DeleteShoppingListApiProvider);
  return ShoppingListRepository(postApi, getApi, getShoppingListItemsApi, deleteApi);
});

class ShoppingListRepository {
  final PostShoppingListApi postApi;
  final GetShoppingListApi getApi;
  final GetShoppingListItemsApi getShoppingListItemsApi;
  final DeleteShoppingListApi deleteApi;

  ShoppingListRepository(this.postApi, this.getApi, this.getShoppingListItemsApi, this.deleteApi);

  // POST
  Future<PostShoppingListResponse> createShoppingList(String name) async {
    final response = await postApi.ShoppingList(name);
    if (response.statusCode == 200) {
      return PostShoppingListResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to create shopping list');
    }
  }

  // GET
  Future<GetShoppingListResponse> fetchShoppingLists({
    required int pageNumber,
    required int pageSize,
    bool includeShoppingItems = true,
    String itemSort = 'Newest',
  }) async {
    final response = await getApi.fetchShoppingLists(
      pageNumber: pageNumber,
      pageSize: pageSize,
      includeShoppingItems: includeShoppingItems,
      itemSort: itemSort,
    );
    return response;
  }

  // GET SHOPPING LISTS ITEMS
  Future<ShoppingListItemResponse> fetchShoppingListsItems({
    required int id,
    required int pageNumber,
    required int pageSize,
  }) async {
    final response = await getShoppingListItemsApi.fetchShoppingListsItems(
      id: id,
      pageNumber: pageNumber,
      pageSize: pageSize,
    );
    return response;
  }

  // DELETE
  Future<DeleteShoppingListResponse> deleteShoppingList(String id) async {
    final response = await deleteApi.deleteShoppingList(id);
    if (response.statusCode == 200) {
      return DeleteShoppingListResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to delete shopping list');
    }
  }
}
