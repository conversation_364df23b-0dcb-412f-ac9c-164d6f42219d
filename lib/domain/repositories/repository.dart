import 'dart:io';

import 'package:mastercookai/core/data/models/ask_ai_response.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_response.dart';
import 'package:mastercookai/core/data/models/plans_response.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/data/request_query/ask_ai_request.dart';
import 'package:mastercookai/core/data/request_query/auth_request.dart';
import 'package:mastercookai/core/data/request_query/shopping_item_request.dart';
import 'package:mastercookai/core/data/request_query/pantry_item_request.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';

import '../../core/data/models/base_response.dart';
import '../../core/data/models/category_response.dart';
import '../../core/data/models/cookbook.dart';
import '../../core/data/models/create_recipe_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/data/models/forgot_password_response.dart';
import '../../core/data/models/get_ask_ai_thread_messages.dart';
import '../../core/data/models/login_model.dart';
import '../../core/data/models/recipe_delete_response.dart';
import '../../core/data/models/recipe_detail_response.dart';
import '../../core/data/models/recipe_response.dart';
import '../../core/data/models/search_items_response.dart';
import '../../core/data/models/shopping_response.dart';
import '../../core/data/request_query/create_recipe_request.dart';
import '../../core/data/request_query/fetch_cookbook_request.dart';
import '../../core/data/request_query/get_asl_ai_threads_request.dart';
import '../../core/data/request_query/paginantion_request.dart';

abstract class Repository {
  Future<bool> getPartner();

  Future<LoginModel> login(AuthQueryParam queryParam);

  Future<BaseResponse> logout();

  Future<ForgotPasswordResponse> forgotPassword(String email);

  Future<GetCookbooksResponse> fetchCookbooks(FetchCookbookRequest queryParam);

  Future<bool> addShoppingItems(int shoppingListId, ShoppingItemRequest body);

  Future<bool> addPantryItems(int pantryListId, PantryItemsRequest body);

  Future<BaseResponse> createCookbook(String cookbookName);

  Future<BaseResponse> deleteCookbook(String id);

  Future<BaseResponse> updateCookbook(String id, String name, File path);

  Future<RecipeResponse> getRecipes(String cookbookId, String itemSort, String cuisineId, String categoryId, String search,
      {required int pageNumber, required int pageSize});

  Future<ShoppingResponse> getShoppingList(PaginationQueryParam queryParam);

  Future<BaseResponse> createShoppingList(String name);

  Future<BaseResponse> deletePantryItems(int pantryListId,
      DeletePantryItemsRequest body);

  Future<BaseResponse> deleteShoppingItems(int shoppingListId,
      DeleteShoppingItemsRequest body);

  Future<RecipeDetailResponse> getRecipeDetail(
      {required int cookbookId, required int recipeId});

  // New methods from RecipeApi
  Future<CategoryResponse> fetchCategories();

  Future<CuisinesResponse> fetchCuisines();

  Future<CreateRecipeResponse> createRecipe(CreateRecipeRequest request,
      int cookbookId);

  Future<CreateRecipeResponse> updateRecipe(CreateRecipeRequest request,
      int cookbookId, int recipeId);

  Future<RecipeDeleteResponse> deleteRecipe(
      {required int cookbookId, required int recipeId});

  Future<BaseResponse> updateShopping(String id, String name, File path);

  Future<BaseResponse> updatePantry(String id, String name, File path);

  Future<SearchItemsResponse> searchItems(String searchQuery);

  Future<UserProfileResponse> userProfile();

  Future<AskAiResponse> askAi(AskAiQueryParam queryParam);

  Future<GetAskAiResponse> getAskAiThreads(GetAskAiThreadsRequest queryParam);

  Future<GetAskAiThreadMessagesResponse> getAskAiMessages(int id,
      {required int pageNumber, required int pageSize});

  Future<BaseResponse> resetPassword(String currentPassword, String newPassword, String newConfirmPassword);

  Future<BaseResponse> deleteAccount();

  Future<PlansResponse> plans();

  Future<BaseResponse> updateUserProfile(UpdateProfileRequest request);

  Future<UserTypesResponse> userTypes();

}
