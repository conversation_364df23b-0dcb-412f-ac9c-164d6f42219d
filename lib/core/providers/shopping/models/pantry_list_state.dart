// import '/core/models/pantry.dart'; 

// enum PantryListStatus { idle, loading, success, error }

// class PantryListState {
//   final PantryListStatus status;
//   final String? errorMessage;

//   const PantryListState({this.status = PantryListStatus.idle, this.errorMessage});

//   PantryListState copyWith({
//     PantryListStatus? status,
//     String? errorMessage,
//   }) {
//     return PantryListState(
//       status: status ?? this.status,
//       errorMessage: errorMessage,
//     );
//   }
// }


// class GetPantryListState {
//   final PantryListStatus status;
//   final String? errorMessage;
//   final List<PantryList> pantryLists;

//   const GetPantryListState({
//     this.status = PantryListStatus.idle,
//     this.errorMessage,
//     this.pantryLists = const [],
//   });

//   GetPantryListState copyWith({
//     PantryListStatus? status,
//     String? errorMessage,
//     List<PantryList>? pantryLists,
//   }) {
//     return GetPantryListState(
//       status: status ?? this.status,
//       errorMessage: errorMessage,
//       pantryLists: pantryLists ?? this.pantryLists,
//     );
//   }
// }



import '../../../data/models/pantry.dart';

enum PantryListStatus {
  idle,        // Initial state
  loading,     // Initial loading
  loadingMore, // Loading more items (pagination)
  success,     // Data loaded successfully
  error,       // Error state
  empty,       // No data available
}

class PantryListState {
  final PantryListStatus status;
  final String? errorMessage;

  const PantryListState({
    this.status = PantryListStatus.idle,
    this.errorMessage,
  });

  PantryListState copyWith({
    PantryListStatus? status,
    String? errorMessage,
  }) {
    return PantryListState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class GetPantryListState {
  final PantryListStatus status;
  final String? errorMessage;
  final List<PantryList> pantryLists;
  final int currentPage;
  final bool hasMore;
  final int totalItems;
  final int pageSize;

  const GetPantryListState({
    this.status = PantryListStatus.idle,
    this.errorMessage,
    this.pantryLists = const [],
    this.currentPage = 1,
    this.hasMore = true,
    this.totalItems = 0,
    this.pageSize = 20, // Default page size
  });

  // Helper getter to check if we're on the first page
  bool get isFirstPage => currentPage == 1;

  // Helper getter to check if we're loading for the first time
  bool get isLoadingFirstPage => status == PantryListStatus.loading && isFirstPage;

  // Helper getter to check if we're loading more items
  bool get isLoadingMore => status == PantryListStatus.loadingMore;

  GetPantryListState copyWith({
    PantryListStatus? status,
    String? errorMessage,
    List<PantryList>? pantryLists,
    int? currentPage,
    bool? hasMore,
    int? totalItems,
    int? pageSize,
  }) {
    return GetPantryListState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      pantryLists: pantryLists ?? this.pantryLists,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
      totalItems: totalItems ?? this.totalItems,
      pageSize: pageSize ?? this.pageSize,
    );
  }

  // Reset to initial state
  GetPantryListState reset() {
    return const GetPantryListState();
  }

  // Add new items to the list (for pagination)
  GetPantryListState appendItems(List<PantryList> newItems) {
    return copyWith(
      pantryLists: [...pantryLists, ...newItems],
      currentPage: currentPage + 1,
    );
  }
}

class GetPantryListItemState {
  final PantryListStatus status;
  final String? errorMessage;
  final List<PantryItem> pantryItems;
  final int currentPage;
  final bool hasMore;
  final int totalItems;

  const GetPantryListItemState({
    this.status = PantryListStatus.idle,
    this.errorMessage,
    this.pantryItems = const [],
    this.currentPage = 1,
    this.hasMore = true,
    this.totalItems = 0,
  });

  GetPantryListItemState copyWith({
    PantryListStatus? status,
    String? errorMessage,
    List<PantryItem>? pantryItems,
    int? currentPage,
    bool? hasMore,
    int? totalItems,
  }) {
    return GetPantryListItemState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      pantryItems: pantryItems ?? this.pantryItems,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
      totalItems: totalItems ?? this.totalItems,
    );
  }

  // Reset to initial state
  GetPantryListItemState reset() {
    return const GetPantryListItemState();
  }

  // Add new items to the list (for pagination)
  GetPantryListItemState appendItems(List<PantryItem> newItems) {
    return copyWith(
      pantryItems: [...pantryItems, ...newItems],
      currentPage: currentPage + 1,
    );
  }

  // Add new items to the list locally (without pagination)
  GetPantryListItemState addItems(List<PantryItem> newItems) {
    return copyWith(
      pantryItems: [...pantryItems, ...newItems],
      totalItems: totalItems + newItems.length,
    );
  }

  // Update existing items in the list
  GetPantryListItemState updateItems(List<PantryItem> updatedItems) {
    final updatedList = pantryItems.map((item) {
      final updatedItem = updatedItems.firstWhere(
        (updated) => updated.id == item.id,
        orElse: () => item,
      );
      return updatedItem;
    }).toList();

    return copyWith(
      pantryItems: updatedList,
    );
  }

  // Remove items from the list by IDs
  GetPantryListItemState removeItems(List<int> itemIds) {
    final filteredItems = pantryItems.where((item) => !itemIds.contains(item.id)).toList();

    return copyWith(
      pantryItems: filteredItems,
      totalItems: filteredItems.length,
    );
  }

  // Clear all items
  GetPantryListItemState clearItems() {
    return copyWith(
      pantryItems: [],
      totalItems: 0,
    );
  }
}