// import '/core/models/shopping.dart'; 

// enum ShoppingListStatus { idle, loading, success, error }

// class ShoppingListState {
//   final ShoppingListStatus status;
//   final String? errorMessage;

//   const ShoppingListState({this.status = ShoppingListStatus.idle, this.errorMessage});

//   ShoppingListState copyWith({
//     ShoppingListStatus? status,
//     String? errorMessage,
//   }) {
//     return ShoppingListState(
//       status: status ?? this.status,
//       errorMessage: errorMessage,
//     );
//   }
// }


// class GetShoppingListState {
//   final ShoppingListStatus status;
//   final String? errorMessage;
//   final List<ShoppingList> shoppingLists;
//   final int currentPage;
//   final bool hasMore;

//   const GetShoppingListState({
//     this.status = ShoppingListStatus.idle,
//     this.errorMessage,
//     this.shoppingLists = const [],
//      this.currentPage = 1,
//     this.hasMore = true,
//   });

//   GetShoppingListState copyWith({
//     ShoppingListStatus? status,
//     String? errorMessage,
//     List<ShoppingList>? shoppingLists,
//     int? currentPage,
//     bool? hasMore,
//   }) {
//     return GetShoppingListState(
//       status: status ?? this.status,
//       errorMessage: errorMessage,
//       shoppingLists: shoppingLists ?? this.shoppingLists,
//       currentPage: currentPage ?? this.currentPage,
//       hasMore: hasMore ?? this.hasMore,
//     );
//   }
// }

// class GetShoppingListItemState {
//   final ShoppingListStatus status;
//   final String? errorMessage;
//   final List<ShoppingItem> shoppingItems;

//   const GetShoppingListItemState({
//     this.status = ShoppingListStatus.idle,
//     this.errorMessage,
//     this.shoppingItems = const [],
//   });

//   GetShoppingListItemState copyWith({
//     ShoppingListStatus? status,
//     String? errorMessage,
//     List<ShoppingItem>? shoppingItems,
//   }) {
//     return GetShoppingListItemState(
//       status: status ?? this.status,
//       errorMessage: errorMessage,
//       shoppingItems: shoppingItems ?? this.shoppingItems,
//     );
//   }
// }


import '../../../../core/data/models/shopping.dart';

enum ShoppingListStatus {
  idle,       // Initial state
  loading,    // Initial loading
  loadingMore, // Loading more items (pagination)
  success,    // Data loaded successfully
  error,      // Error state
  empty,      // No data available
}

class ShoppingListState {
  final ShoppingListStatus status;
  final String? errorMessage;

  const ShoppingListState({
    this.status = ShoppingListStatus.idle,
    this.errorMessage,
  });

  ShoppingListState copyWith({
    ShoppingListStatus? status,
    String? errorMessage,
  }) {
    return ShoppingListState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class GetShoppingListState {
  final ShoppingListStatus status;
  final String? errorMessage;
  final List<ShoppingList> shoppingLists;
  final int currentPage;
  final bool hasMore;
  final int totalItems;
  final int pageSize;

  const GetShoppingListState({
    this.status = ShoppingListStatus.idle,
    this.errorMessage,
    this.shoppingLists = const [],
    this.currentPage = 1,
    this.hasMore = true,
    this.totalItems = 0,
    this.pageSize = 10, // Default page size
  });

  // Helper getter to check if we're on the first page
  bool get isFirstPage => currentPage == 1;

  // Helper getter to check if we're loading for the first time
  bool get isLoadingFirstPage => status == ShoppingListStatus.loading && isFirstPage;

  // Helper getter to check if we're loading more items
  bool get isLoadingMore => status == ShoppingListStatus.loadingMore;

  GetShoppingListState copyWith({
    ShoppingListStatus? status,
    String? errorMessage,
    List<ShoppingList>? shoppingLists,
    int? currentPage,
    bool? hasMore,
    int? totalItems,
    int? pageSize,
  }) {
    return GetShoppingListState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      shoppingLists: shoppingLists ?? this.shoppingLists,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
      totalItems: totalItems ?? this.totalItems,
      pageSize: pageSize ?? this.pageSize,
    );
  }

  // Reset to initial state
  GetShoppingListState reset() {
    return const GetShoppingListState();
  }

  // Add new items to the list (for pagination)
  GetShoppingListState appendItems(List<ShoppingList> newItems) {
    return copyWith(
      shoppingLists: [...shoppingLists, ...newItems],
      currentPage: currentPage + 1,
    );
  }
}

class GetShoppingListItemState {
  final ShoppingListStatus status;
  final String? errorMessage;
  final List<ShoppingItem> shoppingItems;
  final int currentPage;
  final bool hasMore;
  final int totalItems;
  final double totalCost;

  const GetShoppingListItemState({
    this.status = ShoppingListStatus.idle,
    this.errorMessage,
    this.shoppingItems = const [],
    this.currentPage = 1,
    this.hasMore = true,
    this.totalItems = 0,
    this.totalCost = 0.0,
  });

  GetShoppingListItemState copyWith({
    ShoppingListStatus? status,
    String? errorMessage,
    List<ShoppingItem>? shoppingItems,
    int? currentPage,
    bool? hasMore,
    int? totalItems,
    double? totalCost,
  }) {
    return GetShoppingListItemState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      shoppingItems: shoppingItems ?? this.shoppingItems,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
      totalItems: totalItems ?? this.totalItems,
      totalCost: totalCost ?? this.totalCost,
    );
  }

  // Reset to initial state
  GetShoppingListItemState reset() {
    return const GetShoppingListItemState();
  }

  // Add new items to the list (for pagination)
  GetShoppingListItemState appendItems(List<ShoppingItem> newItems, {double? newTotalCost}) {
    return copyWith(
      shoppingItems: [...shoppingItems, ...newItems],
      currentPage: currentPage + 1,
      totalCost: newTotalCost ?? totalCost,
    );
  }

  // Add new items to the list locally (without pagination)
  GetShoppingListItemState addItems(List<ShoppingItem> newItems, {double? additionalCost}) {
    return copyWith(
      shoppingItems: [...shoppingItems, ...newItems],
      totalItems: totalItems + newItems.length,
      totalCost: totalCost + (additionalCost ?? 0.0),
    );
  }

  // Update existing items in the list
  GetShoppingListItemState updateItems(List<ShoppingItem> updatedItems) {
    final updatedList = shoppingItems.map((item) {
      final updatedItem = updatedItems.firstWhere(
        (updated) => updated.id == item.id,
        orElse: () => item,
      );
      return updatedItem;
    }).toList();

    // Recalculate total cost
    final newTotalCost = updatedList.fold<double>(0.0, (sum, item) => sum + item.cost);

    return copyWith(
      shoppingItems: updatedList,
      totalCost: newTotalCost,
    );
  }

  // Remove items from the list by IDs
  GetShoppingListItemState removeItems(List<int> itemIds) {
    final filteredItems = shoppingItems.where((item) => !itemIds.contains(item.id)).toList();
    final newTotalCost = filteredItems.fold<double>(0.0, (sum, item) => sum + item.cost);

    return copyWith(
      shoppingItems: filteredItems,
      totalItems: filteredItems.length,
      totalCost: newTotalCost,
    );
  }

  // Clear all items
  GetShoppingListItemState clearItems() {
    return copyWith(
      shoppingItems: [],
      totalItems: 0,
      totalCost: 0.0,
    );
  }
}