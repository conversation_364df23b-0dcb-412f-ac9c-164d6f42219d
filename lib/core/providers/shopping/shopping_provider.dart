import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/shopping.dart';
import 'controllers/shopping_list_notifier.dart';
import 'models/shopping_list_state.dart';

final shoppingListNotifierProvider =
    NotifierProvider<ShoppinglistNotifier, GetShoppingListState>(
  ShoppinglistNotifier.new,
);

final shoppingProvider =
StateNotifierProvider<ShoppingNotifier, List<Shopping>>(
      (ref) => ShoppingNotifier(),
);

class ShoppingNotifier extends StateNotifier<List<Shopping>> {
  ShoppingNotifier() : super(initialCookbooks);


  static List<Shopping> get initialCookbooks => [
    Shopping(id: 1, title: 'Diary', imageUrl: '', recipeCount: "26 Products", createdDate: ""),
    Shopping(id: 2, title: 'Fruits', imageUrl: '', recipeCount: "20 Products", createdDate: ""),
    Shopping(id: 3, title: 'Vegetables', imageUrl: '', recipeCount: "15 Products", createdDate: ""),
    Shopping(id: 4, title: 'Meat & Eggs', imageUrl: '', recipeCount: "26 Products", createdDate: ""),
  ];



  void addCookbook(Shopping pantry) {
    state = [...state, pantry];
  }

  void importCookbook() {
    // Implement import functionality
  }
}
