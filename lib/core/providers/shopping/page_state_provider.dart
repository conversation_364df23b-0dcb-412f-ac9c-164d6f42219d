import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider to store the current page state for shopping and pantry views
/// This ensures page state is preserved when navigating back from detail screens
class PageState {
  final int shoppingCurrentPage;
  final int pantryCurrentPage;

  const PageState({
    this.shoppingCurrentPage = 0,
    this.pantryCurrentPage = 0,
  });

  PageState copyWith({
    int? shoppingCurrentPage,
    int? pantryCurrentPage,
  }) {
    return PageState(
      shoppingCurrentPage: shoppingCurrentPage ?? this.shoppingCurrentPage,
      pantryCurrentPage: pantryCurrentPage ?? this.pantryCurrentPage,
    );
  }
}

class PageStateNotifier extends StateNotifier<PageState> {
  PageStateNotifier() : super(const PageState());

  void updateShoppingPage(int page) {
    state = state.copyWith(shoppingCurrentPage: page);
  }

  void updatePantryPage(int page) {
    state = state.copyWith(pantryCurrentPage: page);
  }

  void resetShoppingPage() {
    state = state.copyWith(shoppingCurrentPage: 0);
  }

  void resetPantryPage() {
    state = state.copyWith(pantryCurrentPage: 0);
  }
}

final pageStateProvider = StateNotifierProvider<PageStateNotifier, PageState>((ref) {
  return PageStateNotifier();
});
