import 'package:dio/dio.dart';
 import '../../../../app/imports/packages_imports.dart';
import '../../../../domain/repositories/pantry_list_repostory.dart';
import '../../../../core/data/request_query/pantry_item_request.dart';
import '../../../../core/data/partner_api.dart';
import '../../../../core/data/models/pantry.dart' as pantry_models;
import '../models/pantry_list_state.dart';

final pantryProvider = NotifierProvider<PantrylistNotifier, GetPantryListState>(
  PantrylistNotifier.new,
);

class PantrylistNotifier extends Notifier<GetPantryListState> {
  late final PantryListRepository _repo;

  @override
  GetPantryListState build() {
    _repo = ref.watch(pantryListRepositoryProvider); // <-- updated to combined repo
    return const GetPantryListState();
  }

  void setLoggedIn(bool isLoggedIn) {
    state = state.copyWith(
      status: isLoggedIn ? PantryListStatus.success : PantryListStatus.idle,
    );
  }

  /// POST
  Future<(PantryListStatus, String?)> createPantryList(String name) async {
    state = state.copyWith(status: PantryListStatus.loading);

    try {
      final response = await _repo.createPantryList(name);

      if (response.success) {
        state = state.copyWith(status: PantryListStatus.success);
        await fetchPantryLists();
        return (PantryListStatus.success, null);
      } else {
        final error = response.message.error ?? [];
        final firstError = error.isNotEmpty ? error.first : 'Unknown error';
        state = state.copyWith(status: PantryListStatus.error, errorMessage: firstError);
        return (PantryListStatus.error, firstError);
      }

    } catch (e) {
      final error = _handleError(e);
      state = state.copyWith(status: PantryListStatus.error, errorMessage: error);
      return (PantryListStatus.error, error);
    }
  }

  /// GET
  Future<void> fetchPantryLists({bool loadMore = false, String? search}) async {
    if (!loadMore) {
      state = state.copyWith(
        status: PantryListStatus.loading,
        currentPage: 1,
        hasMore: true,
        pantryLists: [], // Clear existing items when refreshing
      );
    } else {
      if (!state.hasMore || state.status == PantryListStatus.loading) return;
      state = state.copyWith(status: PantryListStatus.loadingMore);
    }

    try {
      final result = await _repo.fetchPantryLists(
        pageNumber: state.currentPage,
        pageSize: 10, // Match this with your screen's itemsPerPage
        search: search,
      );

      state = state.copyWith(
        status: PantryListStatus.success,
        pantryLists: loadMore
            ? [...state.pantryLists, ...result.data.pantries]
            : result.data.pantries,
        currentPage: state.currentPage + 1,
        hasMore: state.currentPage < result.data.totalPageCount,
        totalItems: result.data.totalRecords,
      );
    } catch (e) {
      final error = _handleError(e);
      state = state.copyWith(
        status: PantryListStatus.error,
        errorMessage: error,
      );
    }
  }

  /// DELETE
  Future<(PantryListStatus, String?)> deletePantryList(String id) async {
    state = state.copyWith(status: PantryListStatus.loading);

    try {
      final response = await _repo.deletePantryList(id);

      if (response.success) {
        state = state.copyWith(status: PantryListStatus.success);
        await fetchPantryLists();
        return (PantryListStatus.success, null);
      } else {
        final error = response.message.error ?? [];
        final firstError = error.isNotEmpty ? error.first : 'Unknown error';
        state = state.copyWith(status: PantryListStatus.error, errorMessage: firstError);
        return (PantryListStatus.error, firstError);
      }

    } catch (e) {
      final error = _handleError(e);
      state = state.copyWith(status: PantryListStatus.error, errorMessage: error);
      return (PantryListStatus.error, error);
    }
  }

  String _handleError(Object e) {
    if (e is DioException) {
      final response = e.response;
      if (response != null && response.data is Map) {
        final data = response.data as Map;
        final errorList = (data['message']?['error'] as List?)?.cast<String>();
        if (errorList != null && errorList.isNotEmpty) {
          return errorList.first;
        } else {
          return 'failed (403)';
        }
      }
      return e.message ?? 'Dio error occurred';
    } else {
      return e.toString();
    }
  }

  void reset() {
    state = const GetPantryListState();
  }
}

/// GET Pantry List Items
final pantryListItemProvider =
NotifierProvider<PantrylistItemNotifier, GetPantryListItemState>(
  PantrylistItemNotifier.new,
);

class PantrylistItemNotifier extends Notifier<GetPantryListItemState> {
  late final PantryListRepository _repo;

  @override
  GetPantryListItemState build() {
    _repo = ref.watch(pantryListRepositoryProvider);
    return const GetPantryListItemState();
  }

  Future<void> fetchPantryListsItems({
    required int id,
    int pageNumber = 1,
    int pageSize = 20,
    String? search,
  }) async {
    state = state.copyWith(status: PantryListStatus.loading);

    try {
      final result = await _repo.fetchPantryListsItems(
        id: id,
        pageNumber: pageNumber,
        pageSize: pageSize,
        search: search,
      );

      state = state.copyWith(
        status: PantryListStatus.success,
        pantryItems: result.data.pantryItems,
      );
    } catch (e) {
      final error = _handleError(e);
      state = state.copyWith(
        status: PantryListStatus.error,
        errorMessage: error,
      );
    }
  }

  Future<(bool, String?)> addPantryItems({
    required int pantryListId,
    required PantryItemsRequest request,
  }) async {
    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final success = await repositoryImpl.addPantryItems(pantryListId, request);

      if (success) {
        // Update local state instead of refetching
        _updateLocalPantryItemsAfterAdd(request);
        return (true, null);
      } else {
        return (false, 'Failed to add pantry items');
      }
    } catch (e) {
      final error = _handleError(e);
      return (false, error);
    }
  }

  // Helper method to update local state after adding items
  void _updateLocalPantryItemsAfterAdd(PantryItemsRequest request) {
    if (request.pantryItems?.isNotEmpty == true) {
      // Check if this is an update (items have IDs) or add (no IDs)
      final hasIds = request.pantryItems!.any((item) => item.id != null);

      if (hasIds) {
        // This is an update operation - update existing items
        final updatedItems = request.pantryItems!.where((item) => item.id != null).map((item) {
          return pantry_models.PantryItem(
            id: item.id!,
            item: item.item ?? '',
            amount: item.amount ?? 0,
            unit: item.unit ?? '',
            purchasedDate: item.purchasedDate ?? DateTime.now(),
            useByDate: item.useByDate ?? DateTime.now().add(Duration(days: 7)),
          );
        }).toList();

        // Update existing items in the state
        state = state.updateItems(updatedItems);
      } else {
        // This is an add operation - add new items
        final newItems = request.pantryItems!.map((item) {
          return pantry_models.PantryItem(
            id: DateTime.now().millisecondsSinceEpoch + request.pantryItems!.indexOf(item), // Temporary ID
            item: item.item ?? '',
            amount: item.amount ?? 0,
            unit: item.unit ?? '',
            purchasedDate: item.purchasedDate ?? DateTime.now(),
            useByDate: item.useByDate ?? DateTime.now().add(Duration(days: 7)),
          );
        }).toList();

        // Update the state by creating a new state with added items
        state = state.addItems(newItems);
      }
    }
  }

  // Method to update local state after updating items (reusing addPantryItems API)
  void updateLocalPantryItems(List<pantry_models.PantryItem> updatedItems) {
    state = state.updateItems(updatedItems);
  }

  // Method to remove items from local state after deletion
  void removeLocalPantryItems(List<int> itemIds) {
    state = state.removeItems(itemIds);
  }

  // Method to clear all items from local state
  void clearLocalPantryItems() {
    state = state.clearItems();
  }

  String _handleError(Object e) {
    if (e is DioException) {
      final response = e.response;
      if (response != null && response.data is Map) {
        final data = response.data as Map;
        final errorList = (data['message']?['error'] as List?)?.cast<String>();
        if (errorList != null && errorList.isNotEmpty) {
          return errorList.first;
        } else {
          return 'failed (403)';
        }
      }
      return e.message ?? 'Dio error occurred';
    } else {
      return e.toString();
    }
  }

  void reset() {
    state = const GetPantryListItemState();
  }
}