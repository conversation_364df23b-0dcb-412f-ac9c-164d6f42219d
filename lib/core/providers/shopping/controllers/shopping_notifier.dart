import 'dart:io';

import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/shopping_response.dart';
import 'package:mastercookai/core/data/request_query/paginantion_request.dart';
import 'package:mastercookai/core/data/request_query/shopping_item_request.dart';
import 'package:mastercookai/core/data/partner_api.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/network/network_utils.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/services/file_picker_service.dart';

import '../../../network/app_status.dart';
import '../../../network/base_notifier.dart';

/// Provider for managing shopping operations (both lists and items)
final shoppingNotifierProvider =
    StateNotifierProvider<ShoppingNotifier, AppState<List<ShoppingLists>>>(
  (ref) => ShoppingNotifier(ref),
);

class ShoppingNotifier extends BaseNotifier<List<ShoppingLists>> {
  ShoppingNotifier(Ref ref) : super(ref, const AppState<List<ShoppingLists>>());

  /// [shoppingListId] - The ID of the shopping list to add items to
  /// [request] - The shopping items request containing the items to add

  Future<(bool, String?)> addShoppingItems({
    required int shoppingListId,
    required ShoppingItemRequest request,
  }) async {
    // Validate input
    if (request.shoppingItems?.isEmpty ?? true) {
      const errorMessage = 'No shopping items provided';
      return (false, errorMessage);
    }

    // Set loading state
    state = state.copyWith(status: AppStatus.loading);

    try {
      // Get repository instance
      final repositoryImpl = ref.read(partnerApiProvider);

      // Call the API
      final success =
          await repositoryImpl.addShoppingItems(shoppingListId, request);

      if (success) {
        // Success state
        state = state.copyWith(
          status: AppStatus.success,
          errorMessage: null,
        );

        return (true, null);
      } else {
        // API returned false
        const errorMessage = 'Failed to add shopping items';
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: errorMessage,
        );

        return (false, errorMessage);
      }
    } catch (e) {
      // Handle exceptions
      final errorMessage = _handleError(e);
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: errorMessage,
      );

      return (false, errorMessage);
    }
  }

  /// [shoppingListId] - The ID of the shopping list to delete items from
  /// [request] - The delete shopping items request containing the items to delete

  Future<(bool, String?)> deleteShoppingItems({
    required int shoppingListId,
    required DeleteShoppingItemsRequest request,
  }) async {
    // Validate input
    if (request.type == null) {
      const errorMessage = 'Delete type is required';
      return (false, errorMessage);
    }

    // Set loading state
    state = state.copyWith(status: AppStatus.loading);

    try {
      // Get repository instance
      final repositoryImpl = ref.read(partnerApiProvider);

      // Call the API
      final response =
          await repositoryImpl.deleteShoppingItems(shoppingListId, request);

      if (response.success == true) {
        // Success state
        state = state.copyWith(
          status: AppStatus.success,
          errorMessage: null,
        );

        return (true, null);
      } else {
        // API returned false
        const errorMessage = 'Failed to delete shopping items';
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: errorMessage,
        );

        return (false, errorMessage);
      }
    } catch (e) {
      // Handle exceptions
      final errorMessage = _handleError(e);
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: errorMessage,
      );

      return (false, errorMessage);
    }
  }

//Update Shopping
  Future<void> updateShopping(BuildContext context,
      {required String id,
      required String name,
      required File filePath,
      required bool callFromUpdate}) async {
    callDataService(
      repo.updateShopping(id, name, filePath),
      onStart: () {
        showLoader();
        state = state.copyWith(status: AppStatus.updating);
      },
      onSuccess: (response) async {
        hideLoader();
        state = state.copyWith(status: AppStatus.updateSuccess);
        if (callFromUpdate) {
          ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists();
        }
        // Note: List refresh will be handled by the calling screen after dialog closes
      },
      onError: (e) {
        hideLoader();
        state = state.copyWith(
          status: AppStatus.updateError,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: e.message, isError: true);
        }
      },
      onComplete: () => {}, // Remove print statement for production
    );
  }

  /// GET - Fetch cookbooks with pagination
  Future<void> fetchShoppingLists({
    BuildContext? context,
    bool loadMore = false,
    String? search,
  }) async {
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;

    // Create pagination param with search support
    final paginationParam = PaginationQueryParam(
      pageNumber: currentPage,
      pageSize: 10,
    );

    // Add search to the param if provided
    if (search != null && search.isNotEmpty) {
      paginationParam.search = search;
    }

    callDataService(
      repo.getShoppingList(paginationParam),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : [],
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) => _handleSuccessResponse(response, loadMore),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context?.mounted == true) {
          Utils().showFlushbar(context!, message: e.message, isError: true);
        }
      },
      onComplete: () => {}, // Remove print statement for production
    );
  }

  /// POST - Create new shopping list
  Future<void> createShoppingList(BuildContext context, String name) async {
    callDataService(
      repo.createShoppingList(name),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        state = state.copyWith(status: AppStatus.createSuccess);
        // Refresh shopping lists after creation
        await fetchShoppingLists(context: context);
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.createError,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: e.message, isError: true);
        }
      },
    );
  }

  /// Pick image for shopping list cover
  Future<void> pickImage(
      BuildContext context, WidgetRef ref, String id, String name) async {
    final pickFile = ref.read(filePickerServiceProvider.notifier);
    final file = await pickFile.pickFile(
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );

    if (file != null) {
      updateShopping(
        context,
        id: id,
        name: name,
        filePath: file,
        callFromUpdate: true,
      );
    }
  }

  /// Handle errors and return user-friendly error messages
  String _handleError(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  void _handleSuccessResponse(ShoppingResponse response, bool loadMore) {
    final result = response.data!;
    // Debug logging (remove in production)
    // print('Fetched ${result.shoppingLists!.length} shopping lists, loadMore: $loadMore');
    state = state.copyWith(
      status: result.shoppingLists!.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? [...state.data ?? [], ...result.shoppingLists!]
          : result.shoppingLists!,
      hasMore: result.shoppingLists!.length == 10,
      currentPage: state.currentPage,
      totalItems: result.totalPageCount!.toInt(),
      errorMessage: null,
    );
  }

  /// Reset the notifier state to initial state
  void reset() {
    state = const AppState<List<ShoppingLists>>();
  }

  /// Reset to idle state while preserving data
  void resetToIdle() {
    state = state.copyWith(
      status: AppStatus.idle,
      errorMessage: null,
    );
  }

  /// Check if the notifier is currently loading
  bool get isLoading => state.status == AppStatus.loading;

  /// Check if the last operation was successful
  bool get isSuccess => state.status == AppStatus.success;

  /// Check if there's an error
  bool get hasError => state.status == AppStatus.error;

  /// Get the current error message
  String? get errorMessage => state.errorMessage;
}
