import 'package:dio/dio.dart';
 import '../../../../app/imports/packages_imports.dart';
import '../../../../domain/repositories/shopping_list_repostory.dart';
import '../../../../core/data/request_query/shopping_item_request.dart';
import '../../../../core/data/partner_api.dart';
import '../../../../core/data/models/shopping.dart' as shopping_models;
import '../models/shopping_list_state.dart';

final shoppingProvider = NotifierProvider<ShoppinglistNotifier, GetShoppingListState>(
  ShoppinglistNotifier.new,
);

class ShoppinglistNotifier extends Notifier<GetShoppingListState> {
  late final ShoppingListRepository _repo;


  @override
  GetShoppingListState build() {
    _repo = ref.watch(shoppingListRepositoryProvider); // <-- updated to combined repo
    return const GetShoppingListState();
  }

  void setLoggedIn(bool isLoggedIn) {
    state = state.copyWith(
      status: isLoggedIn ? ShoppingListStatus.success : ShoppingListStatus.idle,
    );
  }

  /// POST
  Future<(ShoppingListStatus, String?)> createShoppingList(String name) async {
    state = state.copyWith(status: ShoppingListStatus.loading);

    try {
      final response = await _repo.createShoppingList(name);

      if (response.success) {
        state = state.copyWith(status: ShoppingListStatus.success);
        await fetchShoppingLists();
        return (ShoppingListStatus.success, null);
      } else {
        final error = response.message.error ?? [];
        final firstError = error.isNotEmpty ? error.first : 'Unknown error';
        state = state.copyWith(status: ShoppingListStatus.error, errorMessage: firstError);
        return (ShoppingListStatus.error, firstError);
      }

    } catch (e) {
      final error = _handleError(e);
      state = state.copyWith(status: ShoppingListStatus.error, errorMessage: error);
      return (ShoppingListStatus.error, error);
    }
  }

  /// GET
Future<void> fetchShoppingLists({bool loadMore = false}) async {
  if (!loadMore) {
    // Initial load
    state = state.copyWith(
      status: ShoppingListStatus.loading,
      currentPage: 1,
      hasMore: true,
      shoppingLists: [],
    );
  } else {
    // Loading more
    if (!state.hasMore || state.status == ShoppingListStatus.loadingMore) return;
    state = state.copyWith(status: ShoppingListStatus.loadingMore);
  }

  try {
    final result = await _repo.fetchShoppingLists(
      pageNumber: state.currentPage,
      pageSize: 10, // Should match your API's page size
    );

    state = state.copyWith(
      status: ShoppingListStatus.success,
      shoppingLists: loadMore
          ? [...state.shoppingLists, ...result.data.shoppingLists]
          : result.data.shoppingLists,
      currentPage: state.currentPage + 1,
      hasMore: state.currentPage < result.data.totalPageCount,
      totalItems: result.data.totalRecords,
    );
  } catch (e) {
    final error = _handleError(e);
    state = state.copyWith(
      status: ShoppingListStatus.error,
      errorMessage: error,
    );
  }
}

/// DELETE
  Future<(ShoppingListStatus, String?)> deleteShoppingList(String id) async {
    state = state.copyWith(status: ShoppingListStatus.loading);

    try {
      final response = await _repo.deleteShoppingList(id);

      if (response.success) {
        state = state.copyWith(status: ShoppingListStatus.success);
        await fetchShoppingLists();
        return (ShoppingListStatus.success, null);
      } else {
        final error = response.message.error ?? [];
        final firstError = error.isNotEmpty ? error.first : 'Unknown error';
        state = state.copyWith(status: ShoppingListStatus.error, errorMessage: firstError);
        return (ShoppingListStatus.error, firstError);
      }

    } catch (e) {
      final error = _handleError(e);
      state = state.copyWith(status: ShoppingListStatus.error, errorMessage: error);
      return (ShoppingListStatus.error, error);
    }
  }



  String _handleError(Object e) {
    if (e is DioException) {
      final response = e.response;
      if (response != null && response.data is Map) {
        final data = response.data as Map;
        final errorList = (data['message']?['error'] as List?)?.cast<String>();
        if (errorList != null && errorList.isNotEmpty) {
          return errorList.first;
        } else {
          return 'failed (403)';
        }
      }
      return e.message ?? 'Dio error occurred';
    } else {
      return e.toString();
    }
  }

  void reset() {
    state = const GetShoppingListState();
  }
}

/// GET SHOPPING LIST ITEMS
final shoppingListItemProvider =
    NotifierProvider<ShoppinglistItemNotifier, GetShoppingListItemState>(
  ShoppinglistItemNotifier.new,
);

class ShoppinglistItemNotifier extends Notifier<GetShoppingListItemState> {
  late final ShoppingListRepository _repo;

  @override
  GetShoppingListItemState build() {
    _repo = ref.watch(shoppingListRepositoryProvider);
    return const GetShoppingListItemState();
  }

  Future<void> fetchShoppingListsItems({
    required int id,
    int pageNumber = 1,
    int pageSize = 20,
  }) async {
    state = state.copyWith(status: ShoppingListStatus.loading);

    try {
      final result = await _repo.fetchShoppingListsItems(
        id: id,
        pageNumber: pageNumber,
        pageSize: pageSize,
      );

      state = state.copyWith(
        status: ShoppingListStatus.success,
        shoppingItems: result.data.shoppingItems,
        totalCost: result.data.totalCost,
      );
    } catch (e) {
      final error = _handleError(e);
      state = state.copyWith(
        status: ShoppingListStatus.error,
        errorMessage: error,
      );
    }
  }

  Future<(bool, String?)> addShoppingItems({
    required int shoppingListId,
    required ShoppingItemRequest request,
  }) async {
    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final success = await repositoryImpl.addShoppingItems(shoppingListId, request);

      if (success) {
        // Update local state instead of refetching
        _updateLocalShoppingItemsAfterAdd(request);
        return (true, null);
      } else {
        return (false, 'Failed to add shopping items');
      }
    } catch (e) {
      final error = _handleError(e);
      return (false, error);
    }
  }

  // Helper method to update local state after adding items
  void _updateLocalShoppingItemsAfterAdd(ShoppingItemRequest request) {
    if (request.shoppingItems?.isNotEmpty == true) {
      // Check if this is an update (items have IDs) or add (no IDs)
      final hasIds = request.shoppingItems!.any((item) => item.id != null);

      if (hasIds) {
        // This is an update operation - update existing items
        final updatedItems = request.shoppingItems!.where((item) => item.id != null).map((item) {
          return shopping_models.ShoppingItem(
            id: item.id!,
            item: item.item ?? '',
            amount: (item.amount ?? 0).toDouble(),
            unit: item.unit ?? '',
            storeLocation: item.storeLocation ?? '',
            recipe: item.recipe ?? '',
            cost: (item.cost ?? 0.0).toDouble(),
            purchased: false,
          );
        }).toList();

        // Update existing items in the state
        state = state.updateItems(updatedItems);
      } else {
        // This is an add operation - add new items
        final newItems = request.shoppingItems!.map((item) {
          return shopping_models.ShoppingItem(
            id: DateTime.now().millisecondsSinceEpoch + request.shoppingItems!.indexOf(item), // Temporary ID
            item: item.item ?? '',
            amount: (item.amount ?? 0).toDouble(),
            unit: item.unit ?? '',
            storeLocation: item.storeLocation ?? '',
            recipe: item.recipe ?? '',
            cost: (item.cost ?? 0.0).toDouble(),
            purchased: false, // New items are not purchased
          );
        }).toList();

        // Calculate additional cost
        final additionalCost = newItems.fold<double>(0.0, (sum, item) => sum + item.cost);

        // Update the state by creating a new state with added items
        state = state.addItems(newItems, additionalCost: additionalCost);
      }
    }
  }

  // Method to update local state after updating items (reusing addShoppingItems API)
  void updateLocalShoppingItems(List<shopping_models.ShoppingItem> updatedItems) {
    state = state.updateItems(updatedItems);
  }

  // Method to remove items from local state after deletion
  void removeLocalShoppingItems(List<int> itemIds) {
    state = state.removeItems(itemIds);
  }

  // Method to clear all items from local state
  void clearLocalShoppingItems() {
    state = state.clearItems();
  }

  // Method to delete shopping items
  Future<(bool, String?)> deleteShoppingItems({
    required int shoppingListId,
    required DeleteShoppingItemsRequest request,
  }) async {
    try {
      final repositoryImpl = ref.read(partnerApiProvider);
      final response = await repositoryImpl.deleteShoppingItems(shoppingListId, request);

      if (response.success == true) {
        // Update local state instead of refetching
        if (request.type == "All") {
          clearLocalShoppingItems();
        } else if (request.shoppingItemIds?.isNotEmpty == true) {
          removeLocalShoppingItems(request.shoppingItemIds!);
        }
        return (true, null);
      } else {
        final errorMessage = response.message?.general?.isNotEmpty == true
            ? response.message!.general!.first
            : 'Failed to delete shopping items';
        return (false, errorMessage);
      }
    } catch (e) {
      final error = _handleError(e);
      return (false, error);
    }
  }

  String _handleError(Object e) {
    if (e is DioException) {
      final response = e.response;
      if (response != null && response.data is Map) {
        final data = response.data as Map;
        final errorList = (data['message']?['error'] as List?)?.cast<String>();
        if (errorList != null && errorList.isNotEmpty) {
          return errorList.first;
        } else {
          return 'failed (403)';
        }
      }
      return e.message ?? 'Dio error occurred';
    } else {
      return e.toString();
    }
  }

  void reset() {
    state = const GetShoppingListItemState();
  }
}
