import 'dart:io';

import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/request_query/pantry_item_request.dart';
import 'package:mastercookai/core/data/partner_api.dart';
import 'package:mastercookai/core/network/network_utils.dart';
import 'package:mastercookai/core/services/file_picker_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';

import '../../../network/app_status.dart';
import '../../../network/base_notifier.dart';
import '../pantry_provider.dart';

/// Provider for managing pantry item operations
final pantryNotifierProvider =
    StateNotifierProvider<PantryNotifier, AppState<bool>>(
  (ref) => PantryNotifier(ref),
);

class PantryNotifier extends BaseNotifier<bool> {
  PantryNotifier(Ref ref) : super(ref, const AppState<bool>());

  /// [pantryListId] - The ID of the pantry list to add items to
  /// [request] - The pantry items request containing the items to add

  Future<(bool, String?)> addPantryItems({
    required int pantryListId,
    required PantryItemsRequest request,
  }) async {
    // Validate input
    if (request.pantryItems?.isEmpty ?? true) {
      const errorMessage = 'No pantry items provided';
      return (false, errorMessage);
    }

    // Set loading state
    state = state.copyWith(status: AppStatus.loading);

    try {
      // Get repository instance
      final repositoryImpl = ref.read(partnerApiProvider);
      
      // Call the API
      final success = await repositoryImpl.addPantryItems(pantryListId, request);
      
      if (success) {
        // Success state
        state = state.copyWith(
          status: AppStatus.success,
          data: true,
          errorMessage: null,
        );

        return (true, null);
      } else {
        // API returned false
        const errorMessage = 'Failed to add pantry items';
        state = state.copyWith(
          status: AppStatus.error,
          data: false,
          errorMessage: errorMessage,
        );

        return (false, errorMessage);
      }
    } catch (e) {
      // Handle exceptions
      final errorMessage = _handleError(e);
      state = state.copyWith(
        status: AppStatus.error,
        data: false,
        errorMessage: errorMessage,
      );

      return (false, errorMessage);
    }
  }

  /// Delete pantry items from a pantry list
  /// [pantryListId] - The ID of the pantry list to delete items from
  /// [request] - The delete pantry items request containing the item IDs to delete
  Future<(bool, String?)> deletePantryItems({
    required int pantryListId,
    required DeletePantryItemsRequest request,
  }) async {
    // Debug logging (remove in production)
    // print('Delete request - Type: ${request.type}, PantryItemIds: ${request.pantryItemIds}');

    // Validate input - allow null pantryItemIds when type is "All"
    if (request.type != "All" && (request.pantryItemIds?.isEmpty ?? true)) {
      const errorMessage = 'No pantry item IDs provided';
      return (false, errorMessage);
    }

    // Set loading state
    state = state.copyWith(status: AppStatus.loading);

    try {
      // Get repository instance
      final repositoryImpl = ref.read(partnerApiProvider);

      // Call the API
      final response = await repositoryImpl.deletePantryItems(pantryListId, request);

      if (response.success == true) {
        // Success state
        state = state.copyWith(
          status: AppStatus.success,
          data: true,
          errorMessage: null,
        );

        return (true, null);
      } else {
        // API returned false or error
        final errorMessage = response.message?.general?.isNotEmpty == true
            ? response.message!.general!.first
            : 'Failed to delete pantry items';
        state = state.copyWith(
          status: AppStatus.error,
          data: false,
          errorMessage: errorMessage,
        );

        return (false, errorMessage);
      }
    } catch (e) {
      // Handle exceptions
      final errorMessage = _handleError(e);
      state = state.copyWith(
        status: AppStatus.error,
        data: false,
        errorMessage: errorMessage,
      );

      return (false, errorMessage);
    }
  }

  //Update Pantry
   Future<void> updatePantry(BuildContext context, { required String id, required String name, required File filePath, required bool callFromUpdate}) async {
    callDataService(
      repo.updatePantry(id, name, filePath),
      onStart: () => state = state.copyWith(status: AppStatus.updating),
      onSuccess: (response) async {
        state = state.copyWith(status: AppStatus.updateSuccess);
        if(callFromUpdate){
          ref.read(pantryListNotifierProvider.notifier).fetchPantryLists();
        }

      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.updateError,
          errorMessage: e.message,
        );
        if (context.mounted) {
//Utils().showSnackBar(context, state.errorMessage!);
           Utils().showFlushbar(context, message: e.message, isError: true);
        }
      },
      onComplete: () => {}, // Remove print statement for production
    );
  }

  /// Handle errors and return user-friendly error messages
  String _handleError(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  /// Pick image for Pantry list cover
  Future<void> pickImage(
      BuildContext context, WidgetRef ref, String id, String name) async {
    final pickFile = ref.read(filePickerServiceProvider.notifier);
    final file = await pickFile.pickFile(
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );

    if (file != null) {
      updatePantry(
        context,
        id: id,
        name: name,
        filePath: file,
        callFromUpdate: true,
      );
    }
  }

  /// Reset the notifier state to initial state
  void reset() {
    state = const AppState<bool>();
  }

  /// Reset to idle state while preserving data
  void resetToIdle() {
    state = state.copyWith(
      status: AppStatus.idle, 
      errorMessage: null,
    );
  }

  /// Check if the notifier is currently loading
  bool get isLoading => state.status == AppStatus.loading;

  /// Check if the last operation was successful
  bool get isSuccess => state.status == AppStatus.success;

  /// Check if there's an error
  bool get hasError => state.status == AppStatus.error;

  /// Get the current error message
  String? get errorMessage => state.errorMessage;
}
