import 'dart:async';

import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/models/base_response.dart';
import 'package:mastercookai/core/data/models/plans_response.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/network/base_notifier.dart';
import 'package:mastercookai/core/network/network_utils.dart';
import 'package:mastercookai/core/services/file_picker_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';

/// Provider for managing user profile operations
final userProfileNotifierProvider =
    StateNotifierProvider<UserProfileNotifier, AppState<UserProfileData?>>(
  (ref) => UserProfileNotifier(ref),
);

/// Provider for managing plans operations
final plansNotifierProvider =
    StateNotifierProvider<PlansNotifier, AppState<PlansData?>>(
  (ref) => PlansNotifier(ref),
);

/// Provider for managing user types operations
final userTypesNotifierProvider =
    StateNotifierProvider<UserTypesNotifier, AppState<UserTypesData?>>(
  (ref) => UserTypesNotifier(ref),
);

class UserProfileNotifier extends BaseNotifier<UserProfileData?> {
  UserProfileNotifier(Ref ref) : super(ref, const AppState<UserProfileData?>());

  /// Fetch user profile data
  Future<void> fetchUserProfile() async {
    callDataService(
      repo.userProfile(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (UserProfileResponse response) {
        if (response.success && response.data != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!,
            errorMessage: null,
          );
        } else {
          // Handle case where API returns success but no user profile data
          state = state.copyWith(
            status: AppStatus.empty,
            data: null,
            errorMessage: response.message.general.isNotEmpty
                ? response.message.general.first
                : 'No user profile data found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {}, // Optional completion callback
    );
  }

  //Reset Password
  Future<void> resetPassword(BuildContext context, String currentPassword, String newPassword, String newConfirmPassword, {VoidCallback? onSuccess}) async {
    callDataService(
      repo.resetPassword(currentPassword, newPassword, newConfirmPassword),
      onStart: () {
        state = state.copyWith(status: AppStatus.updating);
      },
      onSuccess: (BaseResponse response) async {
        if (response.success == true) {
          state = state.copyWith(status: AppStatus.success);
          if (context.mounted) {
            // Extract message from API response
            String successMessage = 'Password reset successfully';
            if (response.message?.general != null && response.message!.general!.isNotEmpty) {
              successMessage = response.message!.general!.first;
            }
            Utils().showFlushbar(context, message: successMessage, isError: false);
            // Call the success callback if provided (for clearing fields)
            if (onSuccess != null) {
              onSuccess();
            }
          }
        } else {
          // Handle case where API returns success: false (this shouldn't happen now due to fixed error handling)
          String errorMessage = 'Failed to reset password';
          if (response.message?.error != null && response.message!.error!.isNotEmpty) {
            errorMessage = response.message!.error!.first;
          }
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: errorMessage,
          );
          if (context.mounted) {
            Utils().showFlushbar(context, message: errorMessage, isError: true);
          }
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: state.errorMessage!, isError: true);
        }
      },
      onComplete: () => print('Password reset completed'),
    );
  }

  //Delete Account
  Future<void> deleteAccount(BuildContext context, {VoidCallback? onSuccess}) async {
    callDataService(
      repo.deleteAccount(),
      onStart: () {
        state = state.copyWith(status: AppStatus.deleting);
      },
      onSuccess: (BaseResponse response) async {
        if (response.success == true) {
          state = state.copyWith(status: AppStatus.success);
          if (context.mounted) {
            // Extract message from API response
            String successMessage = 'Account deleted successfully';
            if (response.message?.general != null && response.message!.general!.isNotEmpty) {
              successMessage = response.message!.general!.first;
            }
            Utils().showFlushbar(context, message: successMessage, isError: false);

            // Call the success callback if provided (for navigation)
            if (onSuccess != null) {
              onSuccess();
            }
          }
        } else {
          // Handle case where API returns success: false
          String errorMessage = 'Failed to delete account';
          if (response.message?.error != null && response.message!.error!.isNotEmpty) {
            errorMessage = response.message!.error!.first;
          }
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: errorMessage,
          );
          if (context.mounted) {
            Utils().showFlushbar(context, message: errorMessage, isError: true);
          }
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: state.errorMessage!, isError: true);
        }
      },
      onComplete: () => print('Account deletion completed'),
    );
  }

  //update Profile
 Future<bool> updateProfile({
    required BuildContext context,
    required UpdateProfileRequest request,
  }) async {
    showLoader();
    final completer = Completer<bool>();
    callDataService(
      repo.updateUserProfile(request),
      onStart: () => state = state.copyWith(
        status: AppStatus.loading,
      ),
      onSuccess: (BaseResponse response) {
        hideLoader();
        if (response.success  == true) {
          state = state.copyWith(
            status: AppStatus.success,
            errorMessage: null,
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message!.general!.first,
              isError: false,
              onDismissed: () {
                //updateData(context);
              },
            );
          }
          completer.complete(true);
        } else {
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage:
                response.message!.error?.join(', ') ?? 'Failed toupdate profile',
          );
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: state.errorMessage!, isError: true);
          }
          print('profile update failed: ${response.message!.error}');
          completer.complete(false);
        }
      },

      onError: (e) {
        hideLoader();
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('profile updation error: $e');
        completer.complete(false);
      },
      onComplete: () => print('Profile updation completed'),
    );
    return completer.future;
  }


/// Pick image for Pantry list cover
  Future<void> pickImage(
      BuildContext context, WidgetRef ref, String id, String name) async {
    final pickFile = ref.read(filePickerServiceProvider.notifier);
    final file = await pickFile.pickFile(
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );

    // if (file != null) {
    //   updateProfile(
    //     context,
    //   );
    // }
  }

  
  /// Reset the notifier state to initial state
  void reset() {
    state = const AppState<UserProfileData?>();
  }

  /// Get current user profile data
  UserProfileData? get userProfileData => state.data;

  /// Get current user profile
  UserProfile? get userProfile => state.data?.userProfile;

  /// Get instruction words for units
  List<String> get instructionWords => state.data?.instructionWords ?? [];

  /// Check if currently loading
  bool get isLoading => state.status == AppStatus.loading;

  /// Check if user profile is loaded
  bool get hasProfile => state.data?.userProfile != null && state.status == AppStatus.success;

  /// Check if there's an error
  bool get hasError => state.status == AppStatus.error;

  /// Check if profile is empty
  bool get isEmpty => state.status == AppStatus.empty || state.data == null;

  /// Get error message
  String? get errorMessage => state.errorMessage;

  /// Get user name
  String get userName => state.data?.userProfile?.userName ?? '';

  /// Get user email
  String get userEmail => state.data?.userProfile?.email ?? '';

  /// Get user role
  String get userRole => state.data?.userProfile?.userType ?? '';

  /// Get storage quota in MB
  double get storageQuotaInMB => state.data?.userProfile?.storageQuotaInMB ?? 0.0;

  /// Get storage used in MB
  double get storageUsedInMB => state.data?.userProfile?.storageUsedInMB ?? 0.0;

  /// Get storage usage percentage
  double get storageUsagePercentage {
    if (storageQuotaInMB <= 0) return 0.0;
    return (storageUsedInMB / storageQuotaInMB) * 100;
  }

  /// Get profile picture URL
  String? get profilePicUrl => state.data?.userProfile?.profilePic;

  /// Check if user has profile picture
  bool get hasProfilePic => state.data?.userProfile?.profilePic?.isNotEmpty ?? false;
}

class UserTypesNotifier extends BaseNotifier<UserTypesData?> {
  UserTypesNotifier(Ref ref) : super(ref, const AppState<UserTypesData?>());

  /// Fetch user types
  Future<void> fetchUserTypes() async {
    callDataService(
      repo.userTypes(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (UserTypesResponse response) {
        if (response.success && response.data != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!,
            errorMessage: null,
          );
        } else {
          state = state.copyWith(
            status: AppStatus.empty,
            data: null,
            errorMessage: response.message.general.isNotEmpty
                ? response.message.general.first
                : 'No user types data found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {}, // Optional completion callback
    );
  }

  /// Reset the notifier state to initial state
  void reset() {
    state = const AppState<UserTypesData?>();
  }

  /// Get current user types data
  UserTypesData? get userTypesData => state.data;

  /// Get user types list
  List<UserType> get userTypes => state.data?.userTypes ?? [];

  /// Check if currently loading
  bool get isLoading => state.status == AppStatus.loading;

  /// Check if user types are loaded
  bool get hasUserTypes => state.data?.userTypes.isNotEmpty == true && state.status == AppStatus.success;

  /// Check if there's an error
  bool get hasError => state.status == AppStatus.error;

  /// Check if user types list is empty
  bool get isEmpty => state.status == AppStatus.empty || state.data == null;

  /// Get error message
  String? get errorMessage => state.errorMessage;
}

class PlansNotifier extends BaseNotifier<PlansData?> {
  PlansNotifier(Ref ref) : super(ref, const AppState<PlansData?>());

  /// Fetch plans data
  Future<void> fetchPlans() async {
    callDataService(
      repo.plans(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (PlansResponse response) {
        if (response.success == true && response.data != null) {
          state = state.copyWith(
            status: AppStatus.success,
            data: response.data!,
            errorMessage: null,
          );
        } else {
          // Handle case where API returns success but no plans data
          state = state.copyWith(
            status: AppStatus.empty,
            data: null,
            errorMessage: response.message?.general?.isNotEmpty == true
                ? response.message!.general!.first
                : 'No plans data found',
          );
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message,
          data: null,
        );
      },
      onComplete: () {}, // Optional completion callback
    );
  }

  /// Get loading status
  bool get isLoading => state.status == AppStatus.loading;

  /// Get success status
  bool get isSuccess => state.status == AppStatus.success;

  /// Get error status
  bool get hasError => state.status == AppStatus.error;

  /// Get empty status
  bool get isEmpty => state.status == AppStatus.empty;

  /// Get error message
  String? get errorMessage => state.errorMessage;

  /// Get plans list
  List<Plan> get plans => state.data?.plans ?? [];

  /// Check if plans are available
  bool get hasPlans => plans.isNotEmpty;
}
