
import 'dart:io';

import '../../../app/imports/packages_imports.dart';

final authorProvider = StateNotifierProvider<AuthorNotifier, AuthorState>((ref) {
  return AuthorNotifier();
});

class AuthorState {
  final String? authorName;
  final String? source;
  final String? copyright;
  final File? image;

  const AuthorState({
    this.authorName,
    this.source,
    this.copyright,
    this.image,
  });
}

class AuthorNotifier extends StateNotifier<AuthorState> {
  AuthorNotifier() : super(const AuthorState());

  void updateAuthorName(String value) {
    state = AuthorState(
      authorName: value,
      source: state.source,
      copyright: state.copyright,
      image: state.image,
    );
  }

  void updateSource(String value) {
    state = AuthorState(
      authorName: state.authorName,
      source: value,
      copyright: state.copyright,
      image: state.image,
    );
  }

  void updateCopyright(String value) {
    state = AuthorState(
      authorName: state.authorName,
      source: state.source,
      copyright: value,
      image: state.image,
    );
  }
  void updateImage(File file) {
    state = AuthorState(
      authorName: state.authorName,
      source: state.source,
      copyright: state.copyright,
      image: file,
    );
  }
}
