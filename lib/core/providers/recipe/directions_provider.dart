import 'dart:convert';
import 'dart:io';

import '../../../app/imports/packages_imports.dart';
import '../../data/models/recipe_detail_response.dart';
// model
class DirectionStep {
  final String title;
  final String description;
  final String imageUrl;
  final File? mediaFile;
  final String? mediaFileName;
  final int? mediaFileId;

  DirectionStep({
    required this.title,
    required this.description,
    this.mediaFile,
    this.imageUrl= '',
    this.mediaFileName,
    this.mediaFileId,
  });

  Map<String, dynamic> toJson() => {
    "Title": title,
    "Description": description,
    "MediaFileName": mediaFileName,
    "MediaFileId": mediaFileId,
  };
}

// provider
final directionStepsProvider = StateNotifierProvider<DirectionStepsNotifier, List<DirectionStep>>((ref) {
  return DirectionStepsNotifier();
});

class DirectionStepsNotifier extends StateNotifier<List<DirectionStep>> {
  DirectionStepsNotifier() : super([]);

  void addStep() {
    state = [...state, DirectionStep(title: "", description: "", mediaFile: null)];
  }

  void updateStep(
      int index, {
        String? title,
        String? description,
        File? mediaFile,
        bool removeMediaFile = false,
        String? mediaFileName,
        bool removeMediaFileName = false,
        int? mediaFileId,
      }) {
    final updated = state[index];

    state[index] = DirectionStep(
      title: title ?? updated.title,
      description: description ?? updated.description,
      mediaFile: removeMediaFile ? null : mediaFile ?? updated.mediaFile,
      mediaFileName: removeMediaFileName ? null : mediaFileName ?? updated.mediaFileName,
      mediaFileId: mediaFileId ?? updated.mediaFileId,
    );

    state = [...state];
  }


  void setDirections(List<Directions> backendDirections) {
    state = backendDirections.map((direction) {
      return DirectionStep(
        title: direction.title ?? "", // Map title if available, else empty
        description: direction.description ?? "",
        imageUrl: direction.mediaUrl??'',
        mediaFileName: direction.mediaFileId.toString(),
         mediaFileId: direction.mediaFileId, // Set mediaFileId if available in Directions
      );
    }).toList();
    if (state.isEmpty) {
      // Add a default step if backend data is empty
      state = [DirectionStep(title: "", description: "")];
    }
    print("Set directions: ${state.map((e) => e.description).toList()}");
  }

  void removeStep(int index) {
   // if (state.length <= 1) return;
    final list = [...state]..removeAt(index);
    state = list;
  }

  void resetWithDefaults({int defaultCount = 3}) {
    clearDirections();
    for (int i = 0; i < defaultCount; i++) {
      state = [...state, DirectionStep(title: "", description: "")];
    }
    print("Reset with $defaultCount empty steps. New state: ${state.map((e) => e.description).toList()}");
  }

  void clearDirections() {
    state = [];
    print("Cleared directions. New state: $state");
  }

  List<File> get mediaFiles => state.map((e) => e.mediaFile).whereType<File>().toList();

  String get directionsJson => jsonEncode(state.map((e) => e.toJson()).toList());
}




