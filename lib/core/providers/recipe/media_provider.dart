// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
//
// final mediaFilesProvider = StateNotifierProvider<MediaFilesNotifier, List<RecipeMedia>>((ref) {
//   return MediaFilesNotifier();
// });
//
// class MediaFilesNotifier extends StateNotifier<List<RecipeMedia>> {
//   MediaFilesNotifier() : super([]);
//
//   void updateMedia(List<RecipeMedia?> files) {
//     state = files.where((file) => file != null).cast<RecipeMedia>().toList();
//   }
//
//   void clear() {
//     state = [];
//   }
// }


import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';

final mediaFilesProvider = StateNotifierProvider<MediaFilesNotifier, List<RecipeMedia>>((ref) {
  return MediaFilesNotifier();
});

class MediaFilesNotifier extends StateNotifier<List<RecipeMedia>> {
  MediaFilesNotifier() : super([]);

  void updateMedia(List<RecipeMedia?> files) {
    final newState = files.where((file) => file != null).cast<RecipeMedia>().toList();
    print('Updating mediaFilesProvider with ${newState.length} items: ${newState.map((m) => m.toJson()).toList()}');
    state = newState;
  }

  void clear() {
    print('Clearing mediaFilesProvider');
    state = [];
  }
}