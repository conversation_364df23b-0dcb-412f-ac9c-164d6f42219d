import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/data/request_query/fetch_cookbook_request.dart';
import 'package:mastercookai/core/network/network_utils.dart';
import '../../app/imports/packages_imports.dart';
import '../network/app_status.dart';
import '../network/base_notifier.dart';
import '../services/file_picker_service.dart' show filePickerServiceProvider;

final cookbookNotifierProvider =
    StateNotifierProvider<CookbookNotifier, AppState<List<Cookbook>>>(
  (ref) => CookbookNotifier(ref),
);

class CookbookNotifier extends BaseNotifier<List<Cookbook>> {
  CookbookNotifier(Ref ref) : super(ref, const AppState<List<Cookbook>>()) {
    _initialize();
  }

  void _initialize() {
    // Initial fetch of cookbooks
    callDataService(
      repo.fetchCookbooks(FetchCookbookRequest(
        pageNumber: 1,
        pageSize: 10,
        includeRecipes: true,
        itemSort: 'Newest',
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) => _handleSuccessResponse(response, false),
      onError: (e) => state = state.copyWith(
        status: AppStatus.error,
        errorMessage: e.message ?? 'Failed to fetch cookbooks',
      ),
      onComplete: () => print('Cookbook fetch complete'),
    );
  }

  /// POST - Create new cookbook
  Future<void> createCookbook(BuildContext context, String name) async {
    callDataService(
      repo.createCookbook(name),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        if(response.success == false) {
          if (context.mounted) {
            Utils().showFlushbar(context,message:  response.message?.error?.first ?? 'Failed to create cookbook',isError: true);
          }
           // Close the dialog if open
          state = state.copyWith(status: AppStatus.createError, errorMessage:response.message?.error?.first );

          return;
        }
      state = state.copyWith(status: AppStatus.createSuccess);
      },
      onError: (e) {
         state = state.copyWith(status: AppStatus.createError, errorMessage: e.message );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
      },
    );
  }

  /// GET - Fetch cookbooks with pagination
  Future<void> fetchCookbooks({
    required BuildContext context,
    bool loadMore = false,
    String? search,
  }) async {
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;

    callDataService(
      repo.fetchCookbooks(FetchCookbookRequest(
        pageNumber: currentPage,
        pageSize: 10,
        includeRecipes: true,
        itemSort: 'Newest',
        search: search,
      )),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : [],
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) => _handleSuccessResponse(response, loadMore),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch cookbooks',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
      },
      onComplete: () => print('Cookbook fetch completed'),
    );
  }

  /// DELETE - Delete cookbook
  Future<void> deleteCookbook(BuildContext context, String id) async {
    final bool? confirmed = await Utils().showCommonConfirmDialog(
        context: context,
        title: 'Delete Cookbook',
        subtitle: 'Are you sure you want to delete this cookbook?',
        confirmText: 'Delete',
        cancelText: 'Cancel');
    if (confirmed != true) {
      print('Deletion cancelled by user');
      return; // User cancelled deletion
    }
    callDataService(
      repo.deleteCookbook(id),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) async {
        if (response.success!) {
          print('Deletion successful, refreshing cookbooks');
          state = state.copyWith(status: AppStatus.success);
          await fetchCookbooks(context: context); // Refresh cookbooks
          if (context.mounted) {
            Utils().showSnackBar(
                context,
                (response.success ?? 'Cookbook deleted successfully')
                    as String?);
          }
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to delete cookbook',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
      },
      onComplete: () => print('Deletion process completed'),
    );
  }

  Future<void> updateCookbook(BuildContext context, { required String id, required String name, required File filePath, required bool callFromUpdate}) async {
    callDataService(
      repo.updateCookbook(id, name, filePath),
      onStart: () => state = state.copyWith(status: AppStatus.updating),
      onSuccess: (response) async {
        state = state.copyWith(status: AppStatus.updateSuccess);
        if(callFromUpdate){
          await fetchCookbooks(context: context);
        }

      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.updateError,
          errorMessage: e.message ?? 'Failed to delete cookbook',
        );
        if (context.mounted) {
          Utils().showFlushbar(context,message:  state.errorMessage!,isError: true);
        }
      },
      onComplete: () => print('Deletion process completed'),
    );
  }

  void _handleSuccessResponse(GetCookbooksResponse response, bool loadMore) {
    final result = response.data;
     state = state.copyWith(
      status: result.cookbooks.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? [...state.data ?? [], ...result.cookbooks]
          : result.cookbooks,
      hasMore: result.cookbooks.length == 10,
      currentPage: state.currentPage,
      totalItems: result.totalRecords,
      errorMessage: null,
    );
  }

  Future<void> pickImage(
      BuildContext context, WidgetRef ref, String id, String name) async {
    final pickFile = ref.read(filePickerServiceProvider.notifier); // ✅ Fixed
    final file = await pickFile.pickFile(
      allowedExtensions: ['jpg', 'jpeg', 'png'],
    );

    if (file!=null) {
      updateCookbook(
        context,
        id: id,
        name: name,
        filePath: file,
        callFromUpdate: true,
      );    }
  }

  @override
  void reset() {
    state = const AppState<List<Cookbook>>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }
}
