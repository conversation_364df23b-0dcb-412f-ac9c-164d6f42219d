import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/assets_manager.dart';

import '../data/models/cookbook.dart';
import '../data/models/recipe.dart';


// Dummy data providers
final cookbookDetailProvider = Provider<List<Cookbook>>((_) => [
  Cookbook(
    id: 1,
    name: 'Desserts',
    user: null,
    recipeCount: 26,
    recipes: null,
    userDesktopMaps: null,
    system: false, // or true, depending on your logic
    description: 'Sweet and delicious desserts',
    dateAdded: DateTime.now().subtract(Duration(days: 5)),
    lastModified: DateTime.now(),
    coverImage: AssetsManager.meal,
    coverImageUrl: null,
    fromStore: false,
    sku: null,
    type: 'public', // or 'private', etc.
    groupShare: null,
    contentCollection: null,
    readOnly: false,
    permission: 'read_write',
  ),
  Cookbook(
    id: 2,
    name: 'Desserts',
    user: null,
    recipeCount: 26,
    recipes: null,
    userDesktopMaps: null,
    system: false, // or true, depending on your logic
    description: 'Sweet and delicious desserts',
    dateAdded: DateTime.now().subtract(Duration(days: 5)),
    lastModified: DateTime.now(),
    coverImage: AssetsManager.meal,
    coverImageUrl: null,
    fromStore: false,
    sku: null,
    type: 'public', // or 'private', etc.
    groupShare: null,
    contentCollection: null,
    readOnly: false,
    permission: 'read_write',
  ),
  Cookbook(
    id: 3,
    name: 'Desserts',
    user: null,
    recipeCount: 26,
    recipes: null,
    userDesktopMaps: null,
    system: false, // or true, depending on your logic
    description: 'Sweet and delicious desserts',
    dateAdded: DateTime.now().subtract(Duration(days: 5)),
    lastModified: DateTime.now(),
    coverImage: AssetsManager.meal,
    coverImageUrl: null,
    fromStore: false,
    sku: null,
    type: 'public', // or 'private', etc.
    groupShare: null,
    contentCollection: null,
    readOnly: false,
    permission: 'read_write',
  ),
  Cookbook(
    id: 4,
    name: 'Desserts',
    user: null,
    recipeCount: 26,
    recipes: null,
    userDesktopMaps: null,
    system: false, // or true, depending on your logic
    description: 'Sweet and delicious desserts',
    dateAdded: DateTime.now().subtract(Duration(days: 5)),
    lastModified: DateTime.now(),
    coverImage: AssetsManager.meal,
    coverImageUrl: null,
    fromStore: false,
    sku: null,
    type: 'public', // or 'private', etc.
    groupShare: null,
    contentCollection: null,
    readOnly: false,
    permission: 'read_write',
  ),
  Cookbook(
    id: 5,
    name: 'Desserts',
    user: null,
    recipeCount: 26,
    recipes: null,
    userDesktopMaps: null,
    system: false, // or true, depending on your logic
    description: 'Sweet and delicious desserts',
    dateAdded: DateTime.now().subtract(Duration(days: 5)),
    lastModified: DateTime.now(),
    coverImage: AssetsManager.meal,
    coverImageUrl: null,
    fromStore: false,
    sku: null,
    type: 'public', // or 'private', etc.
    groupShare: null,
    contentCollection: null,
    readOnly: false,
    permission: 'read_write',
  ),
  Cookbook(
    id: 6,
    name: 'Desserts',
    user: null,
    recipeCount: 26,
    recipes: null,
    userDesktopMaps: null,
    system: false, // or true, depending on your logic
    description: 'Sweet and delicious desserts',
    dateAdded: DateTime.now().subtract(Duration(days: 5)),
    lastModified: DateTime.now(),
    coverImage: AssetsManager.meal,
    coverImageUrl: null,
    fromStore: false,
    sku: null,
    type: 'public', // or 'private', etc.
    groupShare: null,
    contentCollection: null,
    readOnly: false,
    permission: 'read_write',
  ),
  // add more cookbooks...
]);

final recipeProvider = Provider<List<Recipe>>((_) => [
  Recipe(
    recipeId : 1,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 2,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 3,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 4,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 5,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 6,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 7,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 8,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 9,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 10,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 11,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 12,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  Recipe(
    recipeId : 13,
    title: 'Grilled Chicken Caesar',
    imageUrl: AssetsManager.meal,
    duration: Duration(minutes: 15),
    servings: 4,
    rating: 4.7,
    reviews: 23,
    createdDate: DateTime.now().subtract(Duration(days: 5)),
  ),
  // add more recipes...
]);