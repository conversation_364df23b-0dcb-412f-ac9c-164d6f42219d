
import 'package:mastercookai/core/providers/shopping/controllers/pantry_list_notifier.dart';
import 'package:mastercookai/core/providers/shopping/models/pantry_list_state.dart';

import '../../../app/imports/packages_imports.dart';
import '../data/models/pantry.dart';
final pantryListNotifierProvider =
NotifierProvider<PantrylistNotifier, GetPantryListState>(
  PantrylistNotifier.new,
);

final pantryProvider =
StateNotifierProvider<PantryNotifier, List<Pantry>>(
      (ref) => PantryNotifier(),
);

class PantryNotifier extends StateNotifier<List<Pantry>> {
  PantryNotifier() : super(initialCookbooks);


  static List<Pantry> get initialCookbooks => [
    Pantry(id: 1, title: 'Diary', imageUrl: '', recipeCount: 26, createdDate: ""),
    Pantry(id: 2, title: 'Fruits', imageUrl: '', recipeCount: 26, createdDate: ""),
    Pantry(id: 3, title: 'Vegetables', imageUrl: '', recipeCount: 26, createdDate: ""),
    Pantry(id: 4, title: 'Meat & Eggs', imageUrl: '', recipeCount: 26, createdDate: ""),
  ];



  void addCookbook(Pantry pantry) {
    state = [...state, pantry];
  }

  void importCookbook() {
    // Implement import functionality
  }
}