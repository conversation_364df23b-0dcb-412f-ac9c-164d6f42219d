import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../../data/models/forgot_password_response.dart';
import '../../../network/app_status.dart';
import '../../../network/base_notifier.dart';
import '../../../network/network_utils.dart';
import '../../../utils/Utils.dart';

// PasswordResetNotifier provider
final passwordResetProvider =
    StateNotifierProvider<PasswordResetNotifier, AppState<void>>((ref) {
  return PasswordResetNotifier(ref);
});

class PasswordResetNotifier extends BaseNotifier<void> {
  PasswordResetNotifier(Ref ref) : super(ref, const AppState<void>());

  String _email = '';

  String get email => _email;

  void setEmail(String email) {
    _email = email;
  }

  Future<void> sendResetLink(BuildContext context) async {
    state = state.copyWith(status: AppStatus.loading);
    await callDataService(
      repo.forgotPassword(_email),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) => _handleSuccessResponse(context, response),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to send reset link',
        );
        Utils()
            .showFlushbar(context, message: state.errorMessage!, isError: true);
      },
    );
  }

  Future<void> _handleSuccessResponse(
      BuildContext context, ForgotPasswordResponse response) async {
    if (response.success == true) {
      state = state.copyWith(status: AppStatus.success);
    } else {
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: response.message?.error ?? 'Failed to send reset link',
      );
      Utils()
          .showFlushbar(context, message: state.errorMessage!, isError: true);
    }
  }

  void reset() {
    _email = '';
    state = const AppState<void>(status: AppStatus.idle);
  }
}
