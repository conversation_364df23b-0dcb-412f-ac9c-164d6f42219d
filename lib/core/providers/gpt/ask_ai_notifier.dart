
import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_thread_messages.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/network/network_utils.dart';

import '../../../app/imports/packages_imports.dart';
import '../../data/models/get_ask_ai_response.dart';
//import '../../data/models/get_ask_ai_thread_messages.dart' hide Message;
import '../../data/request_query/ask_ai_request.dart';
import '../../data/request_query/get_asl_ai_threads_request.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';

final askAiNotifierProvider =
StateNotifierProvider<AskAiNotifier, AppState<List<Thread>>>(
      (ref) => AskAiNotifier(ref),
);



class AskAiNotifier extends BaseNotifier<List<Thread>> {
  AskAiNotifier(Ref ref) : super(ref, const AppState<List<Thread>>()) {
    _initialize();
  }

  void _initialize() {
  // Initial fetch of AskAiThreads
    callDataService(
      repo.getAskAiThreads(GetAskAiThreadsRequest(
        pageNumber: 1,
        pageSize: 10,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) => _handleSuccessResponse(response, false),
      onError: (e) => state = state.copyWith(
        status: AppStatus.error,
        errorMessage: e.message ?? 'Failed to fetch cookbooks',
      ),
      onComplete: () => print('Cookbook fetch complete'),
    );
  }


  // Post - Add AskAi
  Future<void> askAi(BuildContext context,int threadId, String prompt) async {
    callDataService(
      repo.askAi(AskAiQueryParam(
        threadId: threadId,
        prompt: prompt,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        if(response.success == false) {
          if (context.mounted) {
            Utils().showFlushbar(context,message:  response.message.error?? 'Failed to send message',isError: true);
          }
          // Close the dialog if open
          state = state.copyWith(status: AppStatus.createError, errorMessage:response.message.error);

          return;
        }
        state = state.copyWith(status: AppStatus.createSuccess);
      },
      onError: (e) {
        state = state.copyWith(status: AppStatus.createError, errorMessage: e.message );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
      },
    );
  }



  /// GET - Fetch threads with pagination
  Future<void> getAskAiThreads({
    required BuildContext context,
    bool loadMore = false,
  }) async {
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;

    callDataService(
      repo.getAskAiThreads(GetAskAiThreadsRequest(
        pageNumber: currentPage,
        pageSize: 10,
      )),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : [],
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) => _handleSuccessResponse(response, loadMore),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch cookbooks',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
      },
      onComplete: () => print('Threads fetch completed'),
    );
  }

/// GET - Fetch Threads messages with pagination
  Future<void> fetchThreadsMessages({
    required BuildContext context,
    required int id,
    bool reset = false,
    int pageSize = 8,
  }) async {
    if (!reset &&
        (state.hasMore == false || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = reset ? 1 : state.currentPage + 1;

    callDataService(
      repo.getAskAiMessages(
        id,
        pageNumber: currentPage,
        pageSize: pageSize,
      ),
      onStart: () => state = state.copyWith(status: AppStatus.loading),

      onSuccess: (GetAskAiThreadMessagesResponse response) {
        final newMessage = response.data.messages ?? [];
        final totalPages = response.data.totalPageCount ?? 1;
        final hasMore = newMessage.length == pageSize;

        state = state.copyWith(
          status:
          newMessage.isEmpty && reset ? AppStatus.empty : AppStatus.success,
          data: reset
              ? newMessage
              : [...(state.data as List<UserMessage>?) ?? [], ...newMessage],
          hasMore: hasMore,
          currentPage: currentPage,
          totalItems: response.data.totalRecords,
          errorMessage: null,
        );
        print(
            'Fetched ${newMessage.length} messages for ai $id, page: $currentPage, hasMore: $hasMore');
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch recipes',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Messages fetch error: $e');
      },
      onComplete: () => print('Messages fetch completed'),
    );
  }

  void _handleSuccessResponse(GetAskAiResponse response, bool loadMore) {
    final result = response.data;
    state = state.copyWith(
      status: result.threads.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? [...state.data ?? [], ...result.threads]
          : result.threads,
      hasMore: result.threads.length == 10,
      currentPage: state.currentPage,
      totalItems: result.totalRecords,
      errorMessage: null,
    );
  }


  @override
  void reset() {
    state = const AppState<List<Thread>>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }
}