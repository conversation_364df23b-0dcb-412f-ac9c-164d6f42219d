import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../app/imports/packages_imports.dart';
import '../data/models/cookbook.dart';
import '../data/models/create_recipe_response.dart';
import '../data/models/recipe_delete_response.dart';
import '../data/models/recipes.dart';
import '../data/request_query/create_recipe_request.dart';
import '../network/base_notifier.dart';
import '../network/network_utils.dart';
import 'cookboor_notifier.dart';

// Use dynamic to allow data to hold List<Recipe> or RecipeDetails
final recipeNotifierProvider =
    StateNotifierProvider<RecipeNotifier, AppState<List<Recipe>>>(
  (ref) => RecipeNotifier(ref),
);

class RecipeNotifier extends BaseNotifier<List<Recipe>> {
  RecipeNotifier(Ref ref) : super(ref, const AppState<List<Recipe>>());

  Future<void> fetchRecipes({
    required BuildContext context,
    required int cookbookId,
    String? cookbookName,
    bool reset = false,
    int pageSize = 8,
  }) async {
    if (!reset &&
        (state.hasMore == false || state.status == AppStatus.loadingMore)) {
      return;
    }

    final currentPage = reset ? 1 : state.currentPage + 1;

    callDataService(
      repo.getRecipes(
        cookbookId.toString(),
        cookbookName ?? '',
        pageNumber: currentPage,
        pageSize: pageSize,
      ),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      // onStart: () => state = state.copyWith(
      //   status: reset ? AppStatus.loading : AppStatus.loadingMore,
      //   data: reset ? null : state.data,
      //   hasMore: reset ? true : state.hasMore,
      //   currentPage: currentPage,
      // ),
      onSuccess: (RecipeResponse response) {
        final newRecipes = response.recipes ?? [];
        final totalPages = response.totalPageCount ?? 1;
        final hasMore = newRecipes.length == pageSize;

        state = state.copyWith(
          status:
              newRecipes.isEmpty && reset ? AppStatus.empty : AppStatus.success,
          data: reset
              ? newRecipes
              : [...(state.data as List<Recipe>?) ?? [], ...newRecipes],
          hasMore: hasMore,
          currentPage: currentPage,
          totalItems: response.totalRecords ?? newRecipes.length,
          errorMessage: null,
        );
        print(
            'Fetched ${newRecipes.length} recipes for cookbook $cookbookId, page: $currentPage, hasMore: $hasMore');
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch recipes',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Recipe fetch error: $e');
      },
      onComplete: () => print('Recipe fetch completed'),
    );
  }

  // Future<bool> createRecipe({
  //   required BuildContext context,
  //   required CreateRecipeRequest request,
  //   required int cookbookId,
  // }) async {
  //   callDataService(
  //     repo.createRecipe(request, cookbookId),
  //     onStart: () => state = state.copyWith(
  //       status: AppStatus.loading,
  //     ),
  //     onSuccess: (CreateRecipeResponse response) {
  //       hideLoader();
  //       if (response.success) {
  //         state = state.copyWith(
  //           status: AppStatus.success,
  //           errorMessage: null,
  //         );
  //         Navigator.pop(context);
  //         if (context.mounted) {
  //           Utils().showFlushbar(context,
  //               message:
  //                   response.message.general!.first ?? 'Added successfully',
  //               isError: false,
  //           );
  //         }
  //         return true;
  //       } else {
  //
  //         state = state.copyWith(
  //           status: AppStatus.error,
  //           errorMessage:
  //               response.message.error?.join(', ') ?? 'Failed to create recipe',
  //         );
  //         if (context.mounted) {
  //           Utils().showFlushbar(context,
  //               message: state.errorMessage!, isError: true);
  //         }
  //
  //         print('Recipe creation failed: ${response.message.error}');
  //         return false;
  //       }
  //     },
  //     onError: (e) {
  //       hideLoader();
  //       state = state.copyWith(
  //         status: AppStatus.error,
  //         errorMessage: e.message ?? 'Failed to create recipe',
  //       );
  //       if (context.mounted) {
  //         Utils().showFlushbar(context, message: state.errorMessage!,isError: true);
  //       }
  //       print('Recipe creation error: $e');
  //       return false;
  //     },
  //     onComplete: () => print('Recipe creation completed'),
  //   );
  //
  // }

  Future<bool> createRecipe({
    required BuildContext context,
    required CreateRecipeRequest request,
    required int cookbookId,
  }) async {
    bool result = false;
    await callDataService(
      repo.createRecipe(request, cookbookId),
      onStart: () => state = state.copyWith(
        status: AppStatus.loading,
      ),
      onSuccess: (CreateRecipeResponse response) {
        hideLoader();
        if (response.success) {
          state = state.copyWith(
            status: AppStatus.success,
            errorMessage: null,
          );
          Navigator.pop(context);
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message.general!.first ?? 'Added successfully',
              isError: false,
            );
          }
          result = true;
        } else {
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage:
                response.message.error?.join(', ') ?? 'Failed to create recipe',
          );
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: state.errorMessage!, isError: true);
          }
          print('Recipe creation failed: ${response.message.error}');
          result = false;
        }
      },
      onError: (e) {
        hideLoader();
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to create recipe',
        );
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: state.errorMessage!, isError: true);
        }
        print('Recipe creation error: $e');
        result = false;
      },
      onComplete: () => print('Recipe creation completed'),
    );
    return result;
  }

  Future<bool> updateRecipe({
    required BuildContext context,
    required CreateRecipeRequest request,
    required int cookbookId,
    required int recipeId,
  }) async {
    showLoader();
    final completer = Completer<bool>();
    callDataService(
      repo.updateRecipe(request, cookbookId, recipeId),
      onStart: () => state = state.copyWith(
        status: AppStatus.loading,
      ),
      onSuccess: (CreateRecipeResponse response) {
        hideLoader();
        if (response.success) {
          state = state.copyWith(
            status: AppStatus.success,
            errorMessage: null,
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message.general!.first ?? 'Added successfully',
              isError: false,
              onDismissed: () {
                updateData(context, cookbookId: cookbookId);
              },
            );
          }
          completer.complete(true);
        } else {
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage:
                response.message.error?.join(', ') ?? 'Failed to create recipe',
          );
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: state.errorMessage!, isError: true);
          }
          print('Recipe creation failed: ${response.message.error}');
          completer.complete(false);
        }
      },

      onError: (e) {
        hideLoader();
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to create recipe',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Recipe creation error: $e');
        completer.complete(false);
      },
      onComplete: () => print('Recipe creation completed'),
    );
    return completer.future;
  }

  Future<void> deleteRecipe({
    required BuildContext context,
    required int cookbookId,
    required int recipeId,
  }) async {
    callDataService(
      repo.deleteRecipe(cookbookId: cookbookId, recipeId: recipeId),
      onStart: () => state = state.copyWith(
        status: AppStatus.loading,
      ),
      onSuccess: (RecipeDeleteResponse response) {
        if (response.success) {
          state = state.copyWith(
            status: AppStatus.success,
            errorMessage: null,
          );
          Utils().showFlushbar(context,
              message: 'Deleted successfully', isError: false);

          updateData(context, cookbookId: cookbookId);
        } else {
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: response.message ?? 'Failed to delete recipe',
          );
          if (context.mounted) {
            Utils().showSnackBar(context, state.errorMessage!);
          }
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.toString() ?? 'Failed to delete recipe',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Recipe deletion error: $e');
      },
      onComplete: () => print('Recipe deletion completed'),
    );
  }

  void updateData(BuildContext context, {int? cookbookId}) {
    state = state.copyWith(status: AppStatus.loading);

    ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
          context: context,
          loadMore: false,
        );
    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
          context: context,
          cookbookId: cookbookId ?? 0,
          reset: true,
        );

   }
}
