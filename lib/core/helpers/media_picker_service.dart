import 'dart:io';
import 'package:file_picker/file_picker.dart';


class MediaPickerService {

  static Future<List<File>> pickImages({
    bool allowMultiple = true,
  }) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: allowMultiple,
    );

    if (result != null && result.files.isNotEmpty) {
      return result.paths.map((path) => File(path!)).toList();
    }
    return [];
  }

  static Future<File?> pickSingleImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );
      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
    } catch (e, stack) {
      print('File picker error: $e\n$stack');
    }
    return null;
  }
}
