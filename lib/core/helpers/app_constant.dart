import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import '../../app/imports/core_imports.dart';

String baseURL = '';

showLoader() {
  SmartDialog.show(builder: (context) {
    return Center(
      child: LoadingAnimationWidget.fallingDot(
        color: Colors.white,
        size: 50.0,
      ),
    );
  });
}

hideLoader() {
  SmartDialog.dismiss();
}
