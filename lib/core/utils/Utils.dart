import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:url_launcher/url_launcher.dart';
import '../widgets/common_confirm_dialog.dart';

class Utils {
  Future<void> launchURL({required String url}) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  void showToast({
    required String message,
    Toast toastLength = Toast.LENGTH_SHORT,
    ToastGravity gravity = ToastGravity.CENTER,
    int timeInSecForIosWeb = 1,
    Color backgroundColor = Colors.red,
    Color textColor = Colors.white,
    double fontSize = 16.0,
  }) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: toastLength,
      gravity: gravity,
      timeInSecForIosWeb: timeInSecForIosWeb,
      backgroundColor: backgroundColor,
      textColor: textColor,
      fontSize: fontSize,
    );
  }

  showSnackBar(BuildContext context, String? message) {

      // Try to use the context first
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message ?? 'Unknown error',
            style: context.theme.textTheme.labelMedium!.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w400,
            ),
          ),
          backgroundColor: Theme.of(context).primaryColor,
          behavior: SnackBarBehavior.fixed, // Changed from floating to fixed
          duration: const Duration(seconds: 3),
        ),
      );

  }





  void showFlushbar(
      BuildContext context, {
        required String message,
        required bool isError,
        VoidCallback? onDismissed, // Optional callback for dismissal
      }) {
    Flushbar(
      message: message,
      margin: const EdgeInsets.only(top: 40, right: 16, left: 100),
      borderRadius: BorderRadius.circular(8),
      duration: const Duration(seconds: 3),
      backgroundColor: isError ? Colors.red : Colors.green,
      flushbarPosition: FlushbarPosition.TOP,
      flushbarStyle: FlushbarStyle.FLOATING,
      forwardAnimationCurve: Curves.easeOut,
      boxShadows: const [
        BoxShadow(
          color: Colors.black26,
          offset: Offset(0, 2),
          blurRadius: 6,
        ),
      ],
      maxWidth: 300,
      padding: const EdgeInsets.all(16),
    ).show(context).then((value) {
      // Handle dismissal
      if (onDismissed != null) {
        onDismissed(); // Call the optional callback
      }
      // Log or handle the dismissal status
    });
  }

  // Alternative safer method for showing SnackBar
  static void showSafeSnackBar(String? message) {
    if (message == null || message.isEmpty) return;

    try {
      // This will be safer as it doesn't depend on widget context
      print('Info: $message'); // For now, just print to console
      // In the future, this could use a global overlay or notification system
    } catch (e) {
      print('Failed to show message: $message');
    }
  }

  Future<bool?> showCommonConfirmDialog(
      {required BuildContext context,
      required String title,
      required String subtitle,
      required String confirmText,
      required String cancelText}) {
    return showDialog<bool>(
      context: context,
      builder: (context) {
        return CommonConfirmDialog(
          title: title,
          subtitle: subtitle,
          confirmText: confirmText,
          cancelText: cancelText,
        );
      },
    );
  }


  String capitalizeFirstLetter(String input) {
    if (input.isEmpty) return input;
    return input[0].toUpperCase() + input.substring(1);
  }


  void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }



}
