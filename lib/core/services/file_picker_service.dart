import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'file_picker_service.g.dart';

@riverpod
class FilePickerService extends _$FilePickerService {
  @override
  void build() {
    // No state to initialize
  }

  Future<File?> pickFile({List<String>? allowedExtensions}) async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        return await _pickFileMobile();
      } else {
        return await _pickFileDesktop(allowedExtensions: allowedExtensions);
      }
    } catch (e, stackTrace) {
      debugPrint('File picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<File?> _pickFileMobile() async {
    try {
      final source = await _showMobileSourceDialog();
      if (source == null) return null;

      final picker = ImagePicker();
      final XFile? xfile = await picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (xfile == null) return null;

      return File(xfile.path); // Return as a File
    } catch (e, stackTrace) {
      debugPrint('Mobile file picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<File?> _pickFileDesktop({List<String>? allowedExtensions}) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: allowedExtensions != null ? FileType.custom : FileType.any,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final filePath = result.files.single.path;
      if (filePath == null) return null;

      return File(filePath);
    } catch (e, stackTrace) {
      debugPrint('Desktop file picking error: $e\n$stackTrace');
      return null;
    }
  }

  Future<ImageSource?> _showMobileSourceDialog() async {
    final context = ref.read(navigatorKeyProvider).currentContext;
    if (context == null) return null;

    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Source'),
        content: const Text('Choose where to pick the file from:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, ImageSource.camera),
            child: const Text('Camera'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, ImageSource.gallery),
            child: const Text('Gallery'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, null),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

// Global navigator key provider for showing dialogs
final navigatorKeyProvider = Provider<GlobalKey<NavigatorState>>((ref) {
  return GlobalKey<NavigatorState>();
});
