// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_picker_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$filePickerServiceHash() => r'8ef4cdba5272155f1ff9d154c9c51fc1060dceaf';

/// See also [FilePickerService].
@ProviderFor(FilePickerService)
final filePickerServiceProvider =
    AutoDisposeNotifierProvider<FilePickerService, void>.internal(
  FilePickerService.new,
  name: r'filePickerServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$filePickerServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FilePickerService = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
