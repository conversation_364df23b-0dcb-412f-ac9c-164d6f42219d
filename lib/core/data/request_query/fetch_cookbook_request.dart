class FetchCookbookRequest {
  int? pageNumber;
  int? pageSize;
  bool? includeRecipes;
  String? itemSort;

  FetchCookbookRequest({
    this.pageNumber,
    this.pageSize,
    this.includeRecipes,
    this.itemSort,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (pageNumber != null) data['pageNumber'] = pageNumber;
    if (pageSize != null) data['pageSize'] = pageSize;
    if (includeRecipes != null) data['includeRecipes'] = includeRecipes;
    if (itemSort != null) data['itemSort'] = itemSort;

    return data;
  }
}