import '../models/recipe_detail_response.dart';
import '../models/recipe_response.dart';

class RecipeMetadata {
  final int? recipeId;
  final int? cookbookId;
  final List<Recipe>? recipesList;
  final String? name;
  final String? description;
  final List<String>? ingredients;
  final List<NutritionFacts>? nutritionFacts;
  final String? author;
  final String? authorMediaUrl;
  final String? copyright;
  final String? source;
  final List<Directions>? directions;
  final String? servingIdeas;
  final String? wine;
  final List<RecipeMedia>? recipeMedia;
  final int? categoryId;
  final int? cuisineId;
  final String? yieldValue;
  final int? servings;
  final String? prepTime;
  final String? cookTime;
  final String? totalTime;
  final String? wineDesc;

  RecipeMetadata({
    this.recipeId,
    this.cookbookId,
    this.recipesList,
    this.name,
    this.description,
    this.ingredients,
    this.nutritionFacts,
    this.author,
    this.authorMediaUrl,
    this.copyright,
    this.source,
    this.directions,
    this.servingIdeas,
    this.wine,
    this.recipeMedia,
    this.categoryId,
    this.cuisineId,
    this.yieldValue,
    this.servings,
    this.prepTime,
    this.cookTime,
    this.totalTime,
    this.wineDesc,
  });

  RecipeMetadata copyWith({
    int? recipeId,
    int? cookbookId,
    List<Recipe>? recipesList,
    String? name,
    String? description,
    List<String>? ingredients,
    List<NutritionFacts>? nutritionFacts,
    String? author,
    String? authorMediaUrl,
    String? copyright,
    String? source,
    List<Directions>? directions,
    String? servingIdeas,
    String? wine,
    List<RecipeMedia>? recipeMedia,
    int? categoryId,
    int? cuisineId,
    String? yieldValue,
    int? servings,
    String? prepTime,
    String? cookTime,
    String? totalTime,
    String? wineDesc,
  }) {
    return RecipeMetadata(
      recipeId: recipeId ?? this.recipeId,
      cookbookId: cookbookId ?? this.cookbookId,
      recipesList: recipesList ?? this.recipesList,
      name: name ?? this.name,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      nutritionFacts: nutritionFacts ?? this.nutritionFacts,
      author: author ?? this.author,
      authorMediaUrl: authorMediaUrl ?? this.authorMediaUrl,
      copyright: copyright ?? this.copyright,
      source: source ?? this.source,
      directions: directions ?? this.directions,
      servingIdeas: servingIdeas ?? this.servingIdeas,
      wine: wine ?? this.wine,
      recipeMedia: recipeMedia ?? this.recipeMedia,
      categoryId: categoryId ?? this.categoryId,
      cuisineId: cuisineId ?? this.cuisineId,
      yieldValue: yieldValue ?? this.yieldValue,
      servings: servings ?? this.servings,
      prepTime: prepTime ?? this.prepTime,
      cookTime: cookTime ?? this.cookTime,
      totalTime: totalTime ?? this.totalTime,
      wineDesc: wineDesc ?? this.wineDesc,
    );
  }
}