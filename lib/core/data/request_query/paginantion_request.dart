class PaginationQueryParam {
  int? pageNumber;
  int? pageSize;
  int? id;
  String? search;

  PaginationQueryParam({
    this.pageNumber,
    this.pageSize,
    this.id,
    this.search,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pageNumber'] = pageNumber;
    data['pageSize'] = pageSize;
    if (search != null && search!.isNotEmpty) {
      data['search'] = search;
    }
    return data;
  }
}
