class ShoppingItemRequest {
  List<ShoppingItem>? shoppingItems;

  ShoppingItemRequest({
    this.shoppingItems,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (shoppingItems != null) {
      data['shoppingItems'] = shoppingItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ShoppingItem {
  int? id;
  String? item;
  int? amount;
  String? unit;
  String? storeLocation;
  String? recipe;
  double? cost;

  ShoppingItem({
    this.id,
    this.item,
    this.amount,
    this.unit,
    this.storeLocation,
    this.recipe,
    this.cost,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['item'] = item;
    data['amount'] = amount;
    data['unit'] = unit;
    data['storeLocation'] = storeLocation;
    data['recipe'] = recipe;
    data['cost'] = cost;
    return data;
  }
}

class DeleteShoppingItemsRequest {
  String? type;
  List<int>? shoppingItemIds;
  String? status;

  DeleteShoppingItemsRequest({
    this.type,
    this.shoppingItemIds,
    this.status,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (type != null) {
      data['type'] = type;
    }
    if (shoppingItemIds != null) {
      data['shoppingItemIds'] = shoppingItemIds;
    }
    if (status != null) {
      data['status'] = status;
    }
    return data;
  }
}