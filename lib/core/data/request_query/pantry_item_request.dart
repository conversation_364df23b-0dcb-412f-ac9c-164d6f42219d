class PantryItemsRequest {
  List<PantryItem>? pantryItems;

  PantryItemsRequest({
    this.pantryItems,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (pantryItems != null) {
      data['pantryItems'] = pantryItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PantryItem {
  int? id;
  String? item;
  int? amount;
  String? unit;
  DateTime? purchasedDate;
  DateTime? useByDate;

  PantryItem({
    this.id,
    this.item,
    this.amount,
    this.unit,
    this.purchasedDate,
    this.useByDate,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['item'] = item;
    data['amount'] = amount;
    data['unit'] = unit;
    data['purchasedDate'] = purchasedDate?.toIso8601String().substring(0, 10);
    data['useByDate'] = useByDate?.toIso8601String().substring(0, 10);
    return data;
  }
}

class DeletePantryItemsRequest {
  String? type;
  List<int>? pantryItemIds;

  DeletePantryItemsRequest({
    this.type, 
    this.pantryItemIds,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (type != null) {
      data['type'] = type;
    }
    if (pantryItemIds != null) {
      data['pantryItemIds'] = pantryItemIds;
    }
    return data;
  }
}