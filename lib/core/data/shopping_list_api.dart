import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'models/shopping.dart';
import '/core/helpers/local_storage_service.dart';
import '/core/network/dio_provider.dart';
import '/core/utils/HeaderBuilder.dart';

final PostShoppingListApiProvider = Provider<PostShoppingListApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return PostShoppingListApi(dio, storage);
});

class PostShoppingListApi {
  final Dio dio;
  final LocalStorageService localStorage;
  PostShoppingListApi(this.dio , this.localStorage);

  Future<Response> ShoppingList(String name) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key' : encodedKey,
      'Authorization': 'Bearer $token',
    });

    return await dio.post('api/shopping-list', data: {
      'name': name,
    },
        options: Options(
          headers: headers
        )
    );
  }

}


final GetShoppingListApiProvider = Provider<GetShoppingListApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return GetShoppingListApi(dio, storage);
});

class GetShoppingListApi {
  final Dio dio;
  final LocalStorageService localStorage;
  
  GetShoppingListApi(this.dio, this.localStorage);

  Future<GetShoppingListResponse> fetchShoppingLists({
    required int pageNumber,
    required int pageSize,
    bool includeShoppingItems = true,
    String itemSort = 'Newest',
    String? search,
  }) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key': encodedKey,
      'Authorization': 'Bearer $token',
    });

    // Build query parameters, only include search if it's not null or empty
    final queryParams = {
      'pageNumber': pageNumber,
      'pageSize': pageSize,
      'includeShoppingItems': includeShoppingItems,
      'itemSort': itemSort,
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    final response = await dio.get(
      'api/shopping-lists',
      queryParameters: queryParams,
      options: Options(headers: headers),
    );

    if (response.statusCode == 200) {
      return GetShoppingListResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to load shopping lists: ${response.statusCode}');
    }
  }
}


final GetShoppingListItemsApiProvider = Provider<GetShoppingListItemsApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return GetShoppingListItemsApi(dio, storage);
});

class GetShoppingListItemsApi {
  final Dio dio;
  final LocalStorageService localStorage;
  
  GetShoppingListItemsApi(this.dio, this.localStorage);

  Future<ShoppingListItemResponse> fetchShoppingListsItems({
    required int id,
    required int pageNumber,
    required int pageSize,
    String? search,

  }) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key': encodedKey,
      'Authorization': 'Bearer $token',
    });

    final queryParams = <String, dynamic>{
      'pageNumber': pageNumber,
      'pageSize': pageSize,
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    final response = await dio.get(
      'api/shopping-list/$id/items',
      queryParameters: queryParams,
      options: Options(headers: headers),
    );

    if (response.statusCode == 200) {
      return ShoppingListItemResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to load shopping lists: ${response.statusCode}');
    }
  }
}

final DeleteShoppingListApiProvider = Provider<DeleteShoppingListApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return DeleteShoppingListApi(dio, storage);
});

class DeleteShoppingListApi {
  final Dio dio;
  final LocalStorageService localStorage;
  DeleteShoppingListApi(this.dio , this.localStorage);

  Future<Response> deleteShoppingList(String id) async {
    final apiKey = await localStorage.getApiKey();
    final token = await localStorage.getToken();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();
    final headers = headerBuilder.build(extra: {
      'api-key' : encodedKey,
      'Authorization': 'Bearer $token',
    });

    return await dio.delete('api/shopping-list/$id',
        options: Options(
          headers: headers
        )
    );
  }

}