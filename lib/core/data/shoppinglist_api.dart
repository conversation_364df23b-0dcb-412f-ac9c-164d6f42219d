import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../helpers/device_info.dart';
import '../helpers/local_storage_service.dart';
import '../network/dio_provider.dart';
import '../utils/HeaderBuilder.dart';

final PostShoppingListApiProvider = Provider<PostShoppingListApi>((ref) {
  final dio = ref.watch(dioProvider);
  final storage = LocalStorageService();
  return PostShoppingListApi(dio, storage);
});

class PostShoppingListApi {
  final Dio dio;
  final LocalStorageService localStorage;
  PostShoppingListApi(this.dio , this.localStorage);

  Future<Response> login(String name) async {
    final deviceInfo = await DeviceInfoService.getDeviceInfo();
    final ip = await DeviceInfoService.getIpAddress();
    final apiKey = await localStorage.getApiKey();

    if (apiKey == null) {
      throw Exception("No access key found for current platform");
    }

    final encodedKey = base64Encode(utf8.encode(apiKey));

    final headerBuilder = await HeaderBuilder.initialize();

    final headers = headerBuilder.build(extra: {
      'api-key' : encodedKey,
    });

    return await dio.post('api/shopping-list', data: {
      'name': name
    },
        options: Options(
          headers: headers
        )
    );
  }

//   Future<Response> deleteShoppingList(String id) async {
//     final deviceInfo = await DeviceInfoService.getDeviceInfo();
//     final ip = await DeviceInfoService.getIpAddress();
//     final apiKey = await localStorage.getApiKey();

//     if (apiKey == null) {
//       throw Exception("No access key found for current platform");
//     }

//     final encodedKey = base64Encode(utf8.encode(apiKey));

//     final headerBuilder = await HeaderBuilder.initialize();

//     final headers = headerBuilder.build(extra: {
//       'api-key' : encodedKey,
//     });

//     return await dio.delete('api/shopping-list/$id',
//         options: Options(
//           headers: headers
//         )
//     );
//   }
 }
