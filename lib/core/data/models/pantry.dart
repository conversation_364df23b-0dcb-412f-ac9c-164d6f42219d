class Pantry {
  final int id;
  final String title;
  final String imageUrl;
  final int recipeCount;
  final String createdDate;

  Pantry({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.recipeCount,
    required this.createdDate,
  });
}

class PostPantryListResponse {
  final bool success;
  final Message message;
  final PostPantryListResponseData data;
  final int status;

  PostPantryListResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory PostPantryListResponse.fromJson(Map<String, dynamic> json) {
    return PostPantryListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      data: PostPantryListResponseData.fromJson(json['data']),
      status: json['status'] as int,
    );
  }
}

class Message {
  final List<String>? error;
  final List<String>? general;

  Message({this.error, this.general});

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: (json['error'] as List?)?.map((e) => e.toString()).toList(),
      general: (json['general'] as List?)?.map((e) => e.toString()).toList(),
    );
  }
}

class PostPantryListResponseData {
  final int pantryId;

  PostPantryListResponseData({
    required this.pantryId,
  });

  factory PostPantryListResponseData.fromJson(Map<String, dynamic> json) {
    return PostPantryListResponseData(
      pantryId: json['pantryId'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pantryId': pantryId,
    };
  }
}

class GetPantryListResponse {
  final bool success;
  final Message message;
  final PantryData data;
  final int status;

  GetPantryListResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory GetPantryListResponse.fromJson(Map<String, dynamic> json) {
    return GetPantryListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      data: PantryData.fromJson(json['data']),
      status: json['status'] as int,
    );
  }
}

class PantryData {
  final List<PantryList> pantries;
  final int totalRecords;
  final int totalPageCount;

  PantryData({
    required this.pantries,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory PantryData.fromJson(Map<String, dynamic> json) {
    return PantryData(
      pantries: (json['pantries'] as List)
          .map((item) => PantryList.fromJson(item))
          .toList(),
      totalRecords: json['totalRecords'],
      totalPageCount: json['totalPageCount'],
    );
  }
}

class PantryList {
  final int id;
  final String name;
  final String? coverImageUrl;
  final int pantryItemCount;
  final DateTime dateCreated;

  PantryList({
    required this.id,
    required this.name,
    this.coverImageUrl,
    required this.pantryItemCount,
    required this.dateCreated,
    //this.items = const [],
  });

  factory PantryList.fromJson(Map<String, dynamic> json) {
    return PantryList(
      id: json['id'],
      name: json['name'],
      coverImageUrl: json['coverImageUrl'],
      pantryItemCount: json['pantryItemCount'],
      dateCreated: DateTime.parse(json['dateCreated']),
    );
  }
}

class DeletePantryListResponse {
  final bool success;
  final Message message;
  final int status;

  DeletePantryListResponse({
    required this.success,
    required this.message,
    required this.status,
  });

  factory DeletePantryListResponse.fromJson(Map<String, dynamic> json) {
    return DeletePantryListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      status: json['status'] as int,
    );
  }
}

class PantryListItemResponse{
  final bool success;
  final Message message;
  final PantryListItemData data;
  final int status;

  PantryListItemResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory PantryListItemResponse.fromJson(Map<String, dynamic> json) {
    return PantryListItemResponse(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: PantryListItemData.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class PantryListItemData {
  final List<PantryItem> pantryItems;
  final int totalRecords;
  final int totalPageCount;

  PantryListItemData({
    required this.pantryItems,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory PantryListItemData.fromJson(Map<String, dynamic> json) {
    return PantryListItemData(
      pantryItems: (json['pantryItems'] as List)
          .map((e) => PantryItem.fromJson(e))
          .toList(),
      totalRecords: json['totalRecords'],
      totalPageCount: json['totalPageCount'],
    );
  }

  Map<String, dynamic> toJson() => {
    'pantryItems': pantryItems.map((e) => e.toJson()).toList(),
    'totalRecords': totalRecords,
    'totalPageCount': totalPageCount,
  };
}

class PantryItem {
  final int id;
  final String item;
  final int amount;
  final String unit;
  final DateTime purchasedDate;
  final DateTime useByDate;

  PantryItem({
    required this.id,
    required this.item,
    required this.amount,
    required this.unit,
    required this.purchasedDate,
    required this.useByDate,
  });

  factory PantryItem.fromJson(Map<String, dynamic> json) {
    return PantryItem(
      id: json['id'],
      item: json['item'],
      amount: json['amount'],
      unit: json['unit'],
      purchasedDate: DateTime.parse(json['purchasedDate']),
      useByDate: DateTime.parse(json['useByDate']),
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'item': item,
    'amount': amount,
    'unit': unit,
    'purchasedDate': purchasedDate.toIso8601String(),
    'useByDate': useByDate.toIso8601String(),
  };
}