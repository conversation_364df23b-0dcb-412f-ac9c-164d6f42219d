import 'dart:io';

/// success : true
/// message : {"error":null,"general":["Fetched successfully"]}
/// data : {"recipeDetails":{"id":7,"name":"sdfsd","description":"sdfsd","categoryId":2,"category":"Beverage","cuisineId":2,"cuisine":"American","yield":"1","yieldUnit":"service","servings":2,"prepTime":"2","totalTime":"4","cookTime":"2","author":null,"authorMediaFileId":null,"authorMediaUrl":null,"recipeThumbnailFileId":null,"recipeThumbnailFileUrl":null,"directionThumbnailFileId":null,"directionThumbnailFileUrl":null,"source":null,"copyright":null,"notes":null,"servingIdeas":null,"reviewsCount":10,"rating":4.2,"wine":null,"nutritionFacts":[{"label":"Energy","value":"1404 kJ"},{"label":"Protein","value":"45.6 g"},{"label":"Total lipid (fat)","value":"9.52 g"},{"label":"Carbohydrate, by difference","value":"0.56 g"},{"label":"Fiber, total dietary","value":"0 g"}],"directions":[{"id":11,"title":"","description":"test","mediaFileId":"15","mediaType":"","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/25/recipes/7/instruction/others/a044064b-c9bc-4042-937f-c5b07486fe47.jpeg"},{"id":12,"title":"","description":"test","mediaFileId":"16","mediaType":"","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/25/recipes/7/instruction/others/a044064b-c9bc-4042-937f-c5b07486fe47.jpeg"},{"id":13,"title":"","description":"","mediaFileId":null,"mediaType":null,"mediaUrl":null}],"recipeMedia":[{"mediaFileId":14,"mediaType":"","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/25/recipes/7/recipe/others/41bd2333-84ce-41c9-80d9-c268a71e9ae7.jpeg"}],"ingredients":["test","test","test","test"]}}
/// status : 200

class RecipeDetailResponse {
  RecipeDetailResponse({
      bool? success, 
      Message? message, 
      Data? data, 
      int? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  RecipeDetailResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  Data? _data;
  int? _status;
RecipeDetailResponse copyWith({  bool? success,
  Message? message,
  Data? data,
  int? status,
}) => RecipeDetailResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  Data? get data => _data;
  int? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

/// recipeDetails : {"id":7,"name":"sdfsd","description":"sdfsd","categoryId":2,"category":"Beverage","cuisineId":2,"cuisine":"American","yield":"1","yieldUnit":"service","servings":2,"prepTime":"2","totalTime":"4","cookTime":"2","author":null,"authorMediaFileId":null,"authorMediaUrl":null,"recipeThumbnailFileId":null,"recipeThumbnailFileUrl":null,"directionThumbnailFileId":null,"directionThumbnailFileUrl":null,"source":null,"copyright":null,"notes":null,"servingIdeas":null,"reviewsCount":10,"rating":4.2,"wine":null,"nutritionFacts":[{"label":"Energy","value":"1404 kJ"},{"label":"Protein","value":"45.6 g"},{"label":"Total lipid (fat)","value":"9.52 g"},{"label":"Carbohydrate, by difference","value":"0.56 g"},{"label":"Fiber, total dietary","value":"0 g"}],"directions":[{"id":11,"title":"","description":"test","mediaFileId":"15","mediaType":"","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/25/recipes/7/instruction/others/a044064b-c9bc-4042-937f-c5b07486fe47.jpeg"},{"id":12,"title":"","description":"test","mediaFileId":"16","mediaType":"","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/25/recipes/7/instruction/others/a044064b-c9bc-4042-937f-c5b07486fe47.jpeg"},{"id":13,"title":"","description":"","mediaFileId":null,"mediaType":null,"mediaUrl":null}],"recipeMedia":[{"mediaFileId":14,"mediaType":"","mediaUrl":"https://api.mastercook.ai/uploads/users/3/cookbooks/25/recipes/7/recipe/others/41bd2333-84ce-41c9-80d9-c268a71e9ae7.jpeg"}],"ingredients":["test","test","test","test"]}

class Data {
  Data({
      RecipeDetails? recipeDetails,}){
    _recipeDetails = recipeDetails;
}

  Data.fromJson(dynamic json) {
    _recipeDetails = json['recipeDetails'] != null ? RecipeDetails.fromJson(json['recipeDetails']) : null;
  }
  RecipeDetails? _recipeDetails;
Data copyWith({  RecipeDetails? recipeDetails,
}) => Data(  recipeDetails: recipeDetails ?? _recipeDetails,
);
  RecipeDetails? get recipeDetails => _recipeDetails;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_recipeDetails != null) {
      map['recipeDetails'] = _recipeDetails?.toJson();
    }
    return map;
  }

}

class RecipeDetails {
  RecipeDetails({
      int? id, 
      String? name, 
      String? description, 
      int? categoryId, 
      String? category, 
      int? cuisineId, 
      String? cuisine, 
      String? yield, 
      String? yieldUnit, 
      int? servings, 
      String? prepTime, 
      String? totalTime, 
      String? cookTime, 
      dynamic author, 
      dynamic authorMediaFileId, 
      dynamic authorMediaUrl, 
      dynamic recipeThumbnailFileId, 
      dynamic recipeThumbnailFileUrl, 
      dynamic directionThumbnailFileId, 
      dynamic directionThumbnailFileUrl, 
      dynamic source, 
      dynamic copyright, 
      dynamic notes, 
      dynamic servingIdeas, 
      int? reviewsCount, 
      double? rating, 
      dynamic wine, 
      List<NutritionFacts>? nutritionFacts, 
      List<Directions>? directions, 
      List<RecipeMedia>? recipeMedia, 
      List<String>? ingredients,}){
    _id = id;
    _name = name;
    _description = description;
    _categoryId = categoryId;
    _category = category;
    _cuisineId = cuisineId;
    _cuisine = cuisine;
    _yield = yield;
    _yieldUnit = yieldUnit;
    _servings = servings;
    _prepTime = prepTime;
    _totalTime = totalTime;
    _cookTime = cookTime;
    _author = author;
    _authorMediaFileId = authorMediaFileId;
    _authorMediaUrl = authorMediaUrl;
    _recipeThumbnailFileId = recipeThumbnailFileId;
    _recipeThumbnailFileUrl = recipeThumbnailFileUrl;
    _directionThumbnailFileId = directionThumbnailFileId;
    _directionThumbnailFileUrl = directionThumbnailFileUrl;
    _source = source;
    _copyright = copyright;
    _notes = notes;
    _servingIdeas = servingIdeas;
    _reviewsCount = reviewsCount;
    _rating = rating;
    _wine = wine;
    _nutritionFacts = nutritionFacts;
    _directions = directions;
    _recipeMedia = recipeMedia;
    _ingredients = ingredients;
}

  RecipeDetails.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
    _description = json['description'];
    _categoryId = json['categoryId'];
    _category = json['category'];
    _cuisineId = json['cuisineId'];
    _cuisine = json['cuisine'];
    _yield = json['yield'];
    _yieldUnit = json['yieldUnit'];
    _servings = json['servings'];
    _prepTime = json['prepTime'];
    _totalTime = json['totalTime'];
    _cookTime = json['cookTime'];
    _author = json['author'];
    _authorMediaFileId = json['authorMediaFileId'];
    _authorMediaUrl = json['authorMediaUrl'];
    _recipeThumbnailFileId = json['recipeThumbnailFileId'];
    _recipeThumbnailFileUrl = json['recipeThumbnailFileUrl'];
    _directionThumbnailFileId = json['directionThumbnailFileId'];
    _directionThumbnailFileUrl = json['directionThumbnailFileUrl'];
    _source = json['source'];
    _copyright = json['copyright'];
    _notes = json['notes'];
    _servingIdeas = json['servingIdeas'];
    _reviewsCount = json['reviewsCount'];
    _rating = json['rating'];
    _wine = json['wine'];
    if (json['nutritionFacts'] != null) {
      _nutritionFacts = [];
      json['nutritionFacts'].forEach((v) {
        _nutritionFacts?.add(NutritionFacts.fromJson(v));
      });
    }
    if (json['directions'] != null) {
      _directions = [];
      json['directions'].forEach((v) {
        _directions?.add(Directions.fromJson(v));
      });
    }
    if (json['recipeMedia'] != null) {
      _recipeMedia = [];
      json['recipeMedia'].forEach((v) {
        _recipeMedia?.add(RecipeMedia.fromJson(v));
      });
    }
    _ingredients = json['ingredients'] != null ? json['ingredients'].cast<String>() : [];
  }
  int? _id;
  String? _name;
  String? _description;
  int? _categoryId;
  String? _category;
  int? _cuisineId;
  String? _cuisine;
  String? _yield;
  String? _yieldUnit;
  int? _servings;
  String? _prepTime;
  String? _totalTime;
  String? _cookTime;
  dynamic _author;
  dynamic _authorMediaFileId;
  dynamic _authorMediaUrl;
  dynamic _recipeThumbnailFileId;
  dynamic _recipeThumbnailFileUrl;
  dynamic _directionThumbnailFileId;
  dynamic _directionThumbnailFileUrl;
  dynamic _source;
  dynamic _copyright;
  dynamic _notes;
  dynamic _servingIdeas;
  int? _reviewsCount;
  double? _rating;
  dynamic _wine;
  List<NutritionFacts>? _nutritionFacts;
  List<Directions>? _directions;
  List<RecipeMedia>? _recipeMedia;
  List<String>? _ingredients;
RecipeDetails copyWith({  int? id,
  String? name,
  String? description,
  int? categoryId,
  String? category,
  int? cuisineId,
  String? cuisine,
  String? yield,
  String? yieldUnit,
  int? servings,
  String? prepTime,
  String? totalTime,
  String? cookTime,
  dynamic author,
  dynamic authorMediaFileId,
  dynamic authorMediaUrl,
  dynamic recipeThumbnailFileId,
  dynamic recipeThumbnailFileUrl,
  dynamic directionThumbnailFileId,
  dynamic directionThumbnailFileUrl,
  dynamic source,
  dynamic copyright,
  dynamic notes,
  dynamic servingIdeas,
  int? reviewsCount,
  double? rating,
  dynamic wine,
  List<NutritionFacts>? nutritionFacts,
  List<Directions>? directions,
  List<RecipeMedia>? recipeMedia,
  List<String>? ingredients,
}) => RecipeDetails(  id: id ?? _id,
  name: name ?? _name,
  description: description ?? _description,
  categoryId: categoryId ?? _categoryId,
  category: category ?? _category,
  cuisineId: cuisineId ?? _cuisineId,
  cuisine: cuisine ?? _cuisine,
  yield: yield ?? _yield,
  yieldUnit: yieldUnit ?? _yieldUnit,
  servings: servings ?? _servings,
  prepTime: prepTime ?? _prepTime,
  totalTime: totalTime ?? _totalTime,
  cookTime: cookTime ?? _cookTime,
  author: author ?? _author,
  authorMediaFileId: authorMediaFileId ?? _authorMediaFileId,
  authorMediaUrl: authorMediaUrl ?? _authorMediaUrl,
  recipeThumbnailFileId: recipeThumbnailFileId ?? _recipeThumbnailFileId,
  recipeThumbnailFileUrl: recipeThumbnailFileUrl ?? _recipeThumbnailFileUrl,
  directionThumbnailFileId: directionThumbnailFileId ?? _directionThumbnailFileId,
  directionThumbnailFileUrl: directionThumbnailFileUrl ?? _directionThumbnailFileUrl,
  source: source ?? _source,
  copyright: copyright ?? _copyright,
  notes: notes ?? _notes,
  servingIdeas: servingIdeas ?? _servingIdeas,
  reviewsCount: reviewsCount ?? _reviewsCount,
  rating: rating ?? _rating,
  wine: wine ?? _wine,
  nutritionFacts: nutritionFacts ?? _nutritionFacts,
  directions: directions ?? _directions,
  recipeMedia: recipeMedia ?? _recipeMedia,
  ingredients: ingredients ?? _ingredients,
);
  int? get id => _id;
  String? get name => _name;
  String? get description => _description;
  int? get categoryId => _categoryId;
  String? get category => _category;
  int? get cuisineId => _cuisineId;
  String? get cuisine => _cuisine;
  String? get yield => _yield;
  String? get yieldUnit => _yieldUnit;
  int? get servings => _servings;
  String? get prepTime => _prepTime;
  String? get totalTime => _totalTime;
  String? get cookTime => _cookTime;
  dynamic get author => _author;
  dynamic get authorMediaFileId => _authorMediaFileId;
  dynamic get authorMediaUrl => _authorMediaUrl;
  dynamic get recipeThumbnailFileId => _recipeThumbnailFileId;
  dynamic get recipeThumbnailFileUrl => _recipeThumbnailFileUrl;
  dynamic get directionThumbnailFileId => _directionThumbnailFileId;
  dynamic get directionThumbnailFileUrl => _directionThumbnailFileUrl;
  dynamic get source => _source;
  dynamic get copyright => _copyright;
  dynamic get notes => _notes;
  dynamic get servingIdeas => _servingIdeas;
  int? get reviewsCount => _reviewsCount;
  double? get rating => _rating;
  dynamic get wine => _wine;
  List<NutritionFacts>? get nutritionFacts => _nutritionFacts;
  List<Directions>? get directions => _directions;
  List<RecipeMedia>? get recipeMedia => _recipeMedia;
  List<String>? get ingredients => _ingredients;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    map['description'] = _description;
    map['categoryId'] = _categoryId;
    map['category'] = _category;
    map['cuisineId'] = _cuisineId;
    map['cuisine'] = _cuisine;
    map['yield'] = _yield;
    map['yieldUnit'] = _yieldUnit;
    map['servings'] = _servings;
    map['prepTime'] = _prepTime;
    map['totalTime'] = _totalTime;
    map['cookTime'] = _cookTime;
    map['author'] = _author;
    map['authorMediaFileId'] = _authorMediaFileId;
    map['authorMediaUrl'] = _authorMediaUrl;
    map['recipeThumbnailFileId'] = _recipeThumbnailFileId;
    map['recipeThumbnailFileUrl'] = _recipeThumbnailFileUrl;
    map['directionThumbnailFileId'] = _directionThumbnailFileId;
    map['directionThumbnailFileUrl'] = _directionThumbnailFileUrl;
    map['source'] = _source;
    map['copyright'] = _copyright;
    map['notes'] = _notes;
    map['servingIdeas'] = _servingIdeas;
    map['reviewsCount'] = _reviewsCount;
    map['rating'] = _rating;
    map['wine'] = _wine;
    if (_nutritionFacts != null) {
      map['nutritionFacts'] = _nutritionFacts?.map((v) => v.toJson()).toList();
    }
    if (_directions != null) {
      map['directions'] = _directions?.map((v) => v.toJson()).toList();
    }
    if (_recipeMedia != null) {
      map['recipeMedia'] = _recipeMedia?.map((v) => v.toJson()).toList();
    }
    map['ingredients'] = _ingredients;
    return map;
  }

}

/// mediaFileId : 14
/// mediaType : ""
/// mediaUrl : "https://api.mastercook.ai/uploads/users/3/cookbooks/25/recipes/7/recipe/others/41bd2333-84ce-41c9-80d9-c268a71e9ae7.jpeg"

class RecipeMedia {
  RecipeMedia({
      int? mediaFileId,
      String? mediaType,
      String? mediaUrl,
       File? mediaFile,
        File? thumbnailFile,
        File? videoFile,

  }){
    _mediaFileId = mediaFileId;
    _mediaType = mediaType;
    _mediaUrl = mediaUrl;
    _mediaFile = mediaFile;
    _thumbnailFile = thumbnailFile;
    _videoFile = videoFile;
}

  RecipeMedia.fromJson(dynamic json) {
    _mediaFileId = json['mediaFileId'];
    _mediaType = json['mediaType'];
    _mediaUrl = json['mediaUrl'];
  }
  int? _mediaFileId;
  String? _mediaType;
  String? _mediaUrl;
  File? _mediaFile;
  File? _thumbnailFile;
  File? _videoFile;

RecipeMedia copyWith({  int? mediaFileId,
  String? mediaType,
  String? mediaUrl,
}) => RecipeMedia(  mediaFileId: mediaFileId ?? _mediaFileId,
  mediaType: mediaType ?? _mediaType,
  mediaUrl: mediaUrl ?? _mediaUrl,
  mediaFile: mediaFile ?? _mediaFile,
  videoFile: videoFile ?? _videoFile,
  thumbnailFile: thumbnailFile ?? _thumbnailFile,
);
  int? get mediaFileId => _mediaFileId;
  String? get mediaType => _mediaType;
  File? get mediaFile => _mediaFile;
  String? get mediaUrl => _mediaUrl;
  File? get thumbnailFile => _thumbnailFile;
  File? get videoFile => _videoFile;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['mediaFileId'] = _mediaFileId;
    map['mediaType'] = _mediaType;
    map['mediaUrl'] = _mediaUrl;
    map['mediaFile'] = _mediaFile;

    return map;
  }

}


class Directions {
  Directions({
      int? id, 
      String? title, 
      String? description, 
      int? mediaFileId,
      String? mediaType, 
      String? mediaUrl,}){
    _id = id;
    _title = title;
    _description = description;
    _mediaFileId = mediaFileId;
    _mediaType = mediaType;
    _mediaUrl = mediaUrl;
}

  Directions.fromJson(dynamic json) {
    _id = json['id'];
    _title = json['title'];
    _description = json['description'];
    _mediaFileId = json['mediaFileId'];
    _mediaType = json['mediaType'];
    _mediaUrl = json['mediaUrl'];
  }
  int? _id;
  String? _title;
  String? _description;
  int? _mediaFileId;
  String? _mediaType;
  String? _mediaUrl;
Directions copyWith({  int? id,
  String? title,
  String? description,
  int? mediaFileId,
  String? mediaType,
  String? mediaUrl,
}) => Directions(  id: id ?? _id,
  title: title ?? _title,
  description: description ?? _description,
  mediaFileId: mediaFileId ?? _mediaFileId,
  mediaType: mediaType ?? _mediaType,
  mediaUrl: mediaUrl ?? _mediaUrl,
);
  int? get id => _id;
  String? get title => _title;
  String? get description => _description;
  int? get mediaFileId => _mediaFileId;
  String? get mediaType => _mediaType;
  String? get mediaUrl => _mediaUrl;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['title'] = _title;
    map['description'] = _description;
    map['mediaFileId'] = _mediaFileId;
    map['mediaType'] = _mediaType;
    map['mediaUrl'] = _mediaUrl;
    return map;
  }

}

/// label : "Energy"
/// value : "1404 kJ"

class NutritionFacts {
  NutritionFacts({
      String? label, 
      String? value,}){
    _label = label;
    _value = value;
}

  NutritionFacts.fromJson(dynamic json) {
    _label = json['label'];
    _value = json['value'];
  }
  String? _label;
  String? _value;
NutritionFacts copyWith({  String? label,
  String? value,
}) => NutritionFacts(  label: label ?? _label,
  value: value ?? _value,
);
  String? get label => _label;
  String? get value => _value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['label'] = _label;
    map['value'] = _value;
    return map;
  }

}

/// error : null
/// general : ["Fetched successfully"]

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}