/// success : true
/// message : {"error":null,"general":["Fetched successfully"]}
/// data : {"shoppingLists":[{"id":3,"name":"test","user":null,"shoppingItemsCount":1,"coverImageUrl":null,"shoppingItems":[],"dateCreated":"2025-06-26T07:43:05.410Z","lastUpdated":"2025-06-26T07:43:05.410Z"}],"totalRecords":1,"totalPageCount":1}
/// status : 200

class ShoppingResponse {
  ShoppingResponse({
      bool? success, 
      Message? message, 
      Data? data, 
      num? status,}){
    _success = success;
    _message = message;
    _data = data;
    _status = status;
}

  ShoppingResponse.fromJson(dynamic json) {
    _success = json['success'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _status = json['status'];
  }
  bool? _success;
  Message? _message;
  Data? _data;
  num? _status;
ShoppingResponse copyWith({  bool? success,
  Message? message,
  Data? data,
  num? status,
}) => ShoppingResponse(  success: success ?? _success,
  message: message ?? _message,
  data: data ?? _data,
  status: status ?? _status,
);
  bool? get success => _success;
  Message? get message => _message;
  Data? get data => _data;
  num? get status => _status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['status'] = _status;
    return map;
  }

}

/// shoppingLists : [{"id":3,"name":"test","user":null,"shoppingItemsCount":1,"coverImageUrl":null,"shoppingItems":[],"dateCreated":"2025-06-26T07:43:05.410Z","lastUpdated":"2025-06-26T07:43:05.410Z"}]
/// totalRecords : 1
/// totalPageCount : 1

class Data {
  Data({
      List<ShoppingLists>? shoppingLists, 
      num? totalRecords, 
      num? totalPageCount,}){
    _shoppingLists = shoppingLists;
    _totalRecords = totalRecords;
    _totalPageCount = totalPageCount;
}

  Data.fromJson(dynamic json) {
    if (json['shoppingLists'] != null) {
      _shoppingLists = [];
      json['shoppingLists'].forEach((v) {
        _shoppingLists?.add(ShoppingLists.fromJson(v));
      });
    }
    _totalRecords = json['totalRecords'];
    _totalPageCount = json['totalPageCount'];
  }
  List<ShoppingLists>? _shoppingLists;
  num? _totalRecords;
  num? _totalPageCount;
Data copyWith({  List<ShoppingLists>? shoppingLists,
  num? totalRecords,
  num? totalPageCount,
}) => Data(  shoppingLists: shoppingLists ?? _shoppingLists,
  totalRecords: totalRecords ?? _totalRecords,
  totalPageCount: totalPageCount ?? _totalPageCount,
);
  List<ShoppingLists>? get shoppingLists => _shoppingLists;
  num? get totalRecords => _totalRecords;
  num? get totalPageCount => _totalPageCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_shoppingLists != null) {
      map['shoppingLists'] = _shoppingLists?.map((v) => v.toJson()).toList();
    }
    map['totalRecords'] = _totalRecords;
    map['totalPageCount'] = _totalPageCount;
    return map;
  }

}

/// id : 3
/// name : "test"
/// user : null
/// shoppingItemsCount : 1
/// coverImageUrl : null
/// shoppingItems : []
/// dateCreated : "2025-06-26T07:43:05.410Z"
/// lastUpdated : "2025-06-26T07:43:05.410Z"

class ShoppingLists {
  ShoppingLists({
      num? id, 
      String? name, 
      dynamic user, 
      num? shoppingItemsCount, 
      dynamic coverImageUrl, 
      List<dynamic>? shoppingItems, 
      String? dateCreated, 
      String? lastUpdated,}){
    _id = id;
    _name = name;
    _user = user;
    _shoppingItemsCount = shoppingItemsCount;
    _coverImageUrl = coverImageUrl;
    _shoppingItems = shoppingItems;
    _dateCreated = dateCreated;
    _lastUpdated = lastUpdated;
}

  ShoppingLists.fromJson(dynamic json) {
    _id = json['id'];
    _name = json['name'];
    _user = json['user'];
    _shoppingItemsCount = json['shoppingItemsCount'];
    _coverImageUrl = json['coverImageUrl'];
    if (json['shoppingItems'] != null) {
      _shoppingItems = [];
      json['shoppingItems'].forEach((v) {
        _shoppingItems?.add(v);
      });
    }
    _dateCreated = json['dateCreated'];
    _lastUpdated = json['lastUpdated'];
  }
  num? _id;
  String? _name;
  dynamic _user;
  num? _shoppingItemsCount;
  dynamic _coverImageUrl;
  List<dynamic>? _shoppingItems;
  String? _dateCreated;
  String? _lastUpdated;
ShoppingLists copyWith({  num? id,
  String? name,
  dynamic user,
  num? shoppingItemsCount,
  dynamic coverImageUrl,
  List<dynamic>? shoppingItems,
  String? dateCreated,
  String? lastUpdated,
}) => ShoppingLists(  id: id ?? _id,
  name: name ?? _name,
  user: user ?? _user,
  shoppingItemsCount: shoppingItemsCount ?? _shoppingItemsCount,
  coverImageUrl: coverImageUrl ?? _coverImageUrl,
  shoppingItems: shoppingItems ?? _shoppingItems,
  dateCreated: dateCreated ?? _dateCreated,
  lastUpdated: lastUpdated ?? _lastUpdated,
);
  num? get id => _id;
  String? get name => _name;
  dynamic get user => _user;
  num? get shoppingItemsCount => _shoppingItemsCount;
  dynamic get coverImageUrl => _coverImageUrl;
  List<dynamic>? get shoppingItems => _shoppingItems;
  String? get dateCreated => _dateCreated;
  String? get lastUpdated => _lastUpdated;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['name'] = _name;
    map['user'] = _user;
    map['shoppingItemsCount'] = _shoppingItemsCount;
    map['coverImageUrl'] = _coverImageUrl;
    if (_shoppingItems != null) {
      map['shoppingItems'] = _shoppingItems?.map((v) => v.toJson()).toList();
    }
    map['dateCreated'] = _dateCreated;
    map['lastUpdated'] = _lastUpdated;
    return map;
  }

}

/// error : null
/// general : ["Fetched successfully"]

class Message {
  Message({
      dynamic error, 
      List<String>? general,}){
    _error = error;
    _general = general;
}

  Message.fromJson(dynamic json) {
    _error = json['error'];
    _general = json['general'] != null ? json['general'].cast<String>() : [];
  }
  dynamic _error;
  List<String>? _general;
Message copyWith({  dynamic error,
  List<String>? general,
}) => Message(  error: error ?? _error,
  general: general ?? _general,
);
  dynamic get error => _error;
  List<String>? get general => _general;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['error'] = _error;
    map['general'] = _general;
    return map;
  }

}