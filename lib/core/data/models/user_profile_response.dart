import 'dart:convert';

UserProfileResponse userProfileResponseFromJson(String str) => 
    UserProfileResponse.fromJson(json.decode(str));

String userProfileResponseToJson(UserProfileResponse data) => 
    json.encode(data.toJson());

class UserProfileResponse {
  final bool success;
  final Message message;
  final UserProfileData? data;
  final int status;

  UserProfileResponse({
    required this.success,
    required this.message,
    this.data,
    required this.status,
  });

  factory UserProfileResponse.fromJson(Map<String, dynamic> json) => UserProfileResponse(
    success: json["success"] ?? false,
    message: Message.fromJson(json["message"] ?? {}),
    data: json["data"] != null ? UserProfileData.fromJson(json["data"]) : null,
    status: json["status"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "message": message.toJson(),
    "data": data?.toJson(),
    "status": status,
  };
}

class UserProfileData {
  final UserProfile? userProfile;
  final List<String> instructionWords;

  UserProfileData({
    this.userProfile,
    required this.instructionWords,
  });

  factory UserProfileData.fromJson(Map<String, dynamic> json) => UserProfileData(
    userProfile: json["userProfile"] != null 
        ? UserProfile.fromJson(json["userProfile"])
        : null,
    instructionWords: List<String>.from(json["instructionWords"] ?? []),
  );

  Map<String, dynamic> toJson() => {
    "userProfile": userProfile?.toJson(),
    "instructionWords": List<dynamic>.from(instructionWords.map((x) => x)),
  };
}

class UserProfile {
  final int userId;
  final String name;
  final String userName;
  final String companyName;
  final String phone;
  final String userType; 
  final int userTypeId; 
  final String? postalCode;
  final List<dynamic> userActivations;
  final dynamic userSubscription;
  final String dob;
  final String email;
  final String gender;
  final String? profilePic;
  final double storageQuotaInMB;
  final double storageUsedInMB;
  final CurrentPlan? currentPlan;

  UserProfile({
    required this.userId,
    required this.name,
    required this.userName,
    required this.companyName,
    required this.phone,
    required this.userType,
    required this.userTypeId,
    this.postalCode,
    required this.userActivations,
    this.userSubscription,
    required this.dob,
    required this.email,
    required this.gender,
    this.profilePic,
    required this.storageQuotaInMB,
    required this.storageUsedInMB,
    this.currentPlan,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
    userId: json["userId"] ?? 0,
    name: json["name"] ?? '',
    userName: json["userName"] ?? '',
    companyName: json["companyName"] ?? '',
    phone: json["phone"] ?? '',
    userType: json["userType"] ?? '',  // Changed from role to userType
    userTypeId: json["userTypeId"] ?? 0,  // Added new field
    postalCode: json["postalCode"],
    userActivations: List<dynamic>.from(json["userActivations"] ?? []),
    userSubscription: json["userSubscription"],
    dob: json["dob"] ?? '',
    email: json["email"] ?? '',
    gender: json["gender"] ?? '',
    profilePic: json["profilePic"],
    storageQuotaInMB: (json["storageQuotaInMB"] as num?)?.toDouble() ?? 0.0,
    storageUsedInMB: (json["storageUsedInMB"] as num?)?.toDouble() ?? 0.0,
    currentPlan: json["currentPlan"] != null 
        ? CurrentPlan.fromJson(json["currentPlan"])
        : null,
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
    "name": name,
    "userName": userName,
    "companyName": companyName,
    "phone": phone,
    "userType": userType,  // Changed from role to userType
    "userTypeId": userTypeId,  // Added new field
    "postalCode": postalCode,
    "userActivations": List<dynamic>.from(userActivations.map((x) => x)),
    "userSubscription": userSubscription,
    "dob": dob,
    "email": email,
    "gender": gender,
    "profilePic": profilePic,
    "storageQuotaInMB": storageQuotaInMB,
    "storageUsedInMB": storageUsedInMB,
    "currentPlan": currentPlan?.toJson(),
  };
}

class CurrentPlan {
  final String planName;
  final int planAmount;
  final String nextPaymentDate;
  final String billingInterval;

  CurrentPlan({
    required this.planName,
    required this.planAmount,
    required this.nextPaymentDate,
    required this.billingInterval,
  });

  factory CurrentPlan.fromJson(Map<String, dynamic> json) => CurrentPlan(
    planName: json["planName"] ?? '',
    planAmount: json["planAmount"] ?? 0,
    nextPaymentDate: json["nextPaymentDate"] ?? '',
    billingInterval: json["billingInterval"] ?? '',
  );

  Map<String, dynamic> toJson() => {
    "planName": planName,
    "planAmount": planAmount,
    "nextPaymentDate": nextPaymentDate,
    "billingInterval": billingInterval,
  };
}

class Message {
  final dynamic error;
  final List<String> general;

  Message({
    this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
    error: json["error"],
    general: List<String>.from(json["general"] ?? []),
  );

  Map<String, dynamic> toJson() => {
    "error": error,
    "general": general,
  };
}