// recipe_model.dart

class RecipeResponse {
  final List<Recipe> recipes;
  final int totalRecords;
  final int totalPageCount;

  RecipeResponse({
    required this.recipes,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory RecipeResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? {};
    final recipesJson = data['recipes'] as List<dynamic>? ?? [];

    return RecipeResponse(
      recipes: recipesJson.map((e) => Recipe.fromJson(e)).toList(),
      totalRecords: data['totalRecords'] ?? 0,
      totalPageCount: data['totalPageCount'] ?? 0,
    );
  }
}
class Recipe {
  final int id;
  final String name;
  final String? description;
  final int servings;
  final String? prepTime;
  final String? cookTime;
  final String? totalTime;
  final int reviewsCount;
  final double rating;
  final String? mediaUrl;
  final DateTime? dateAdded;
  final String permission;
  final bool isPublic;

  Recipe({
    required this.id,
    required this.name,
    this.description,
    required this.servings,
    this.prepTime,
    this.cookTime,
    this.totalTime,
    required this.reviewsCount,
    required this.rating,
    this.mediaUrl,
    this.dateAdded,
    required this.permission,
    required this.isPublic,
  });

  factory Recipe.fromJson(Map<String, dynamic> json) {
    return Recipe(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      servings: json['servings'] ?? 0,
      prepTime: json['prepTime'],
      cookTime: json['cookTime'],
      totalTime: json['totalTime'],
      reviewsCount: json['reviewsCount'] ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      mediaUrl: json['mediaUrl'] ?? '',
      dateAdded: json['dateAdded'] != null
          ? DateTime.tryParse(json['dateAdded'])
          : null,
      permission: json['permission'] ?? '',
      isPublic: json['public'] ?? false,
    );
  }
}



