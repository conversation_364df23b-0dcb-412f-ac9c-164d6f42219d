class AskAiResponse {
  final bool success;
  final Message message;
  final ThreadData data;
  final int status;

  AskAiResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory AskAiResponse.fromJson(Map<String, dynamic> json) {
    return AskAiResponse(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: ThreadData.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class Message {
  final String? error;
  final List<String> general;

  Message({
    required this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: json['error'],
      general: List<String>.from(json['general']),
    );
  }
}

class ThreadData {
  final int threadId;
  final String response;

  ThreadData({
    required this.threadId,
    required this.response,
  });

  factory ThreadData.fromJson(Map<String, dynamic> json) {
    return ThreadData(
      threadId: json['threadId'],
      response: json['response'],
    );
  }
}
