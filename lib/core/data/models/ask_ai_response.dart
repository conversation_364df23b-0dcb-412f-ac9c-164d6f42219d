class AskAiResponse {
  final bool success;
  final Message message;
  final ThreadData data;
  final int status;

  AskAiResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory AskAiResponse.fromJson(Map<String, dynamic> json) {
    return AskAiResponse(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: ThreadData.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class Message {
  final String? error;
  final List<String> general;

  Message({
    required this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: json['error'],
      general: List<String>.from(json['general']),
    );
  }
}

class ThreadData {
  final int id;
  final String prompt;
  final String response;
  final DateTime askedAt;

  ThreadData({
    required this.id,
    required this.prompt,
    required this.response,
    required this.askedAt,
  });

  factory ThreadData.fromJson(Map<String, dynamic> json) {
    return ThreadData(
      id: json['id'],
      prompt: json['prompt'],
      response: json['response'],
      askedAt: DateTime.parse(json['askedAt']),
    );
  }
}