import 'ask_ai_response.dart';

class GetAskAiThreadMessagesResponse {
  final bool success;
  final Message message;
  final MessageData data;
  final int status;

  GetAskAiThreadMessagesResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory GetAskAiThreadMessagesResponse.fromJson(Map<String, dynamic> json) {
    return GetAskAiThreadMessagesResponse(
      success: json['success'],
      message: Message.fromJson(json['message']),
      data: MessageData.fromJson(json['data']),
      status: json['status'],
    );
  }
}

class Message {
  final String? error;
  final List<String> general;

  Message({
    required this.error,
    required this.general,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: json['error'],
      general: List<String>.from(json['general']),
    );
  }
}

class MessageData {
  final List<UserMessage> messages;
  final int totalRecords;
  final int totalPageCount;

  MessageData({
    required this.messages,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory MessageData.fromJson(Map<String, dynamic> json) {
    return MessageData(
      messages: (json['messages'] as List)
          .map((e) => UserMessage.fromJson(e))
          .toList(),
      totalRecords: json['totalRecords'],
      totalPageCount: json['totalPageCount'],
    );
  }
}

class UserMessage {
  final int id;
  final String prompt;
  final String response;
  final DateTime askedAt;
  final String? imageUrl; 

  UserMessage({
    required this.id,
    required this.prompt,
    required this.response,
    required this.askedAt,
    this.imageUrl,
  });

  factory UserMessage.fromJson(Map<String, dynamic> json) {
    return UserMessage(
      id: json['id'],
      prompt: json['prompt'],
      response: json['response'],
      askedAt: DateTime.parse(json['askedAt']),
      imageUrl: json['imageUrl'], // Parse image field from API response
    );
  }

  // Helper method to create UserMessage from ThreadData
  factory UserMessage.fromThreadData(ThreadData threadData, {String? image}) {
    return UserMessage(
      id: threadData.id,
      prompt: threadData.prompt,
      response: threadData.response,
      askedAt: threadData.askedAt,
      imageUrl: image,
    );
  }
}
