// import 'dart:convert';
// import 'package:dio/dio.dart';
// import '../request_query/create_recipe_request.dart';
// import '../../../core/data/models/create_recipe_response.dart';
// import '../../../core/data/models/recipe_delete_response.dart';
// import '../../../core/data/models/recipe_response.dart';
// import '../../../core/data/models/recipes.dart';
// import '../../../core/helpers/local_storage_service.dart';
// import '../../../core/utils/HeaderBuilder.dart';
//
// class RecipeApi {
//   final Dio dio;
//   final LocalStorageService localStorage;
//
//   RecipeApi(this.dio, this.localStorage);
//
//   Future<Response> fetchCategories() async {
//     final apiKey = await localStorage.getApiKey();
//     final authToken = await localStorage.getToken();
//     final encodedKey = base64Encode(utf8.encode(apiKey!));
//
//     final headerBuilder = await HeaderBuilder.initialize();
//
//     final headers = headerBuilder.build(
//         extra: {'api-key': encodedKey, 'Authorization': 'Bearer $authToken'});
//     return dio.get('api/categories',
//         options: Options(headers: headers)); // ← Replace with actual endpoint
//   }
//
//   Future<Response> fetchCusines() async {
//     final apiKey = await localStorage.getApiKey();
//     final authToken = await localStorage.getToken();
//     final encodedKey = base64Encode(utf8.encode(apiKey!));
//
//     final headerBuilder = await HeaderBuilder.initialize();
//
//     final headers = headerBuilder.build(
//         extra: {'api-key': encodedKey, 'Authorization': 'Bearer $authToken'});
//     return dio.get('api/cuisines',
//         options: Options(headers: headers)); // ← Replace with actual endpoint
//   }
//
//   Future<CreateRecipeResponse> createRecipe(
//       CreateRecipeRequest request, int cookbookId) async {
//     try {
//       final apiKey = await localStorage.getApiKey();
//       final authToken = await localStorage.getToken();
//       final encodedKey = base64Encode(utf8.encode(apiKey!));
//
//       final headerBuilder = await HeaderBuilder.initialize();
//
//       final headers = headerBuilder.build(extra: {
//         'id': cookbookId,
//         'api-key': encodedKey,
//         'Authorization': 'Bearer $authToken'
//       });
//
//       final response = await dio.post('api/cookbook/${cookbookId}/recipe',
//           data: await request.toFormData(),
//           options: Options(
//             headers: headers,
//             validateStatus: (status) {
//               return status != null && status < 500;
//             },
//           ));
//       if (response.data != null) {
//         return CreateRecipeResponse.fromJson(response.data);
//       } else {
//         return CreateRecipeResponse(
//           success: false,
//           message:
//               MessageResponse(error: ['Unknown server error'], general: null),
//           data: null,
//           status: response.statusCode ?? 500,
//         );
//       }
//     } catch (e) {
//       rethrow;
//     }
//   }
//
//   Future<RecipeResponse> fetchRecipes(
//       {int page = 1,
//       int pageSize = 10,
//       required int cookbookId,
//       String? itemSort}) async {
//     try {
//       final apiKey = await localStorage.getApiKey();
//       final authToken = await localStorage.getToken();
//       final encodedKey = base64Encode(utf8.encode(apiKey!));
//
//       final headerBuilder = await HeaderBuilder.initialize();
//
//       final headers = headerBuilder.build(extra: {
//         'id': cookbookId,
//         'api-key': encodedKey,
//         'Authorization': 'Bearer $authToken',
//         'pageNumber': page,
//         'pageSize': pageSize,
//         'itemSort': itemSort ?? ''
//       });
//       final response = await dio.get(
//         'api/cookbook/${cookbookId}/recipes',
//         options: Options(headers: headers),
//       );
//
//       if (response.statusCode == 200 && response.data != null) {
//         return RecipeResponse.fromJson(response.data);
//       } else {
//         throw Exception('Failed to load recipes');
//       }
//     } catch (e) {
//       rethrow;
//     }
//   }
//
//   Future<RecipesResponse> fetchRecipe(
//       {required int cookbookId, required int recipeId}) async {
//     try {
//       final apiKey = await localStorage.getApiKey();
//       final authToken = await localStorage.getToken();
//       final encodedKey = base64Encode(utf8.encode(apiKey!));
//
//       final headerBuilder = await HeaderBuilder.initialize();
//
//       final headers = headerBuilder.build(extra: {
//         'cookbookId': cookbookId,
//         'recipeId': recipeId,
//         'api-key': encodedKey,
//         'Authorization': 'Bearer $authToken',
//       });
//       final response = await dio.get(
//         'api/cookbook/${cookbookId}/recipe/${recipeId}',
//         options: Options(headers: headers),
//       );
//
//       if (response.statusCode == 200 && response.data != null) {
//         return RecipesResponse.fromJson(response.data);
//       } else {
//         throw Exception('Failed to load recipes');
//       }
//     } catch (e) {
//       rethrow;
//     }
//   }
//
//   Future<RecipeDeleteResponse> deleteRecipe(
//       {required int cookbookId, required int recipeId}) async {
//     final apiKey = await localStorage.getApiKey();
//     final authToken = await localStorage.getToken();
//     final encodedKey = base64Encode(utf8.encode(apiKey!));
//
//     final headerBuilder = await HeaderBuilder.initialize();
//
//     final headers = headerBuilder.build(extra: {
//       'id': cookbookId,
//       'api-key': encodedKey,
//       'Authorization': 'Bearer $authToken'
//     });
//     final response = await dio.delete(
//         'api/cookbook/${cookbookId}/recipe/${recipeId}',
//         options: Options(headers: headers)); // ← Replace with actual endpoint
//
//     return RecipeDeleteResponse.fromJson(response.data);
//   }
// }
