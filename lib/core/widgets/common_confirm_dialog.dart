import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import 'custom_button.dart';

class CommonConfirmDialog extends StatelessWidget {
  final String title;
  final String subtitle;
  final String confirmText;
  final String cancelText;
  final Color? confirmTextColor;

  const CommonConfirmDialog({
    Key? key,
    required this.title,
    required this.subtitle,
    this.confirmText = 'Delete',
    this.cancelText = 'Cancel',
    this.confirmTextColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Center(
        child: Text(
          title,
          style: context.theme.textTheme.labelLarge!.copyWith(
            fontWeight: FontWeight.w700,
            fontSize: 40.sp,
          ),
          textAlign: TextAlign.center,
        ),
      ),
      content: Text(
        subtitle,
        style: context.theme.textTheme.labelLarge!.copyWith(
          fontWeight: FontWeight.w400,
          fontSize: 25.sp,
        ),
        textAlign: TextAlign.center,
      ),
      actionsPadding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 30),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: Text(
            cancelText,
            style: context.theme.textTheme.labelLarge!.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor, // Adjust as needed
            ),
          ),
        ),
        SizedBox(width: 56), // space between buttons
        CustomButton(
          width: 200.w,
          fontSize: 18.sp,
          text: confirmText,
          onPressed: () => Navigator.pop(context, true),
        ),
      ],
    );
  }
}

