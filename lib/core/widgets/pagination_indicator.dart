import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

class PaginationIndicator extends StatelessWidget {
  final int totalPages;
  final int currentPage;
  final bool hasMore;
  final Size screenSize;
  final BuildContext context;

  const PaginationIndicator({
    super.key,
    required this.totalPages,
    required this.currentPage,
    required this.hasMore,
    required this.screenSize,
    required this.context,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: screenSize.height * 0.20,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: List<Widget>.generate(
            hasMore ? totalPages + 1 : totalPages,
            (int index) {
              if (index >= totalPages) return const SizedBox.shrink();
              return Container(
                width: screenSize.width * 0.008,
                height: screenSize.width * 0.008,
                margin: EdgeInsets.symmetric(horizontal: screenSize.width * 0.004),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: currentPage == index
                      ? context.theme.scaffoldBackgroundColor
                      : context.theme.scaffoldBackgroundColor.withValues(alpha: 0.2),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}