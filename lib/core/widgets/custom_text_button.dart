import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../app/imports/core_imports.dart';
import '../utils/device_utils.dart';

class CustomTextButton extends StatelessWidget {
  final String text;
  final Color color;
  final double? size;
  final VoidCallback onPressed;

  const CustomTextButton({
    required this.text,
    required this.onPressed,
    required this.color,
    this.size,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Text(
        text,
        style: context.theme.textTheme.labelMedium!.copyWith(
          fontSize: size ??  responsiveFont(24).sp,
          color: color,
          letterSpacing: 1,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
