import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../app/assets_manager.dart';
import '../../app/theme/colors.dart';
import '../../presentation/cookbook/widgets/custom_desc_text.dart';


class CustomPopupMenu extends StatelessWidget {
  final Function(String) onSelected;
  final Color backgroundColor;
  final Color textColor;
  final Color? deleteTextColor;

  const CustomPopupMenu({
    Key? key,
    required this.onSelected,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black,
    this.deleteTextColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert,
          color: AppColors.backgroudInActiveColor,
          size: 40.h),
      color: backgroundColor,
      onSelected: onSelected,
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        _buildPopupMenuItem(
          value: 'Delete',
          iconPath: AssetsManager.dlt_recipe,
          label: 'Delete',
          textColor: deleteTextColor ?? AppColors.primaryColor,
        ),
      ],
    );
  }

  PopupMenuItem<String> _buildPopupMenuItem({
    required String value,
    required String iconPath,
    required String label,
    required Color textColor,
  }) {
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          SvgPicture.asset(
            iconPath,
            height: 40.h,
            width: 40.w,
          ),
          SizedBox(width: 15.w),
          CustomDescText(
            desc: label,
            textColor: textColor,
            size: 24.sp,
          ),
        ],
      ),
    );
  }
}