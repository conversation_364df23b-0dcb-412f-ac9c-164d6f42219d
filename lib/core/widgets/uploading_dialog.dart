import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../app/imports/packages_imports.dart';
import '../utils/device_utils.dart';

class UploadingDialog extends StatelessWidget {
  final String title;
  final String subtitle;
  final double progress;
  final VoidCallback onCancel;
  final VoidCallback onClose;

  const UploadingDialog({
    super.key,
    required this.title,
    required this.subtitle,
    required this.progress,
    required this.onCancel,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 500.w,
      padding: EdgeInsets.only(top:20.0,bottom: 20.0,right: 10.0,left: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
          )
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          /// Header: Image + Title/Sub + Close Button
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// Circular Image
              ClipRRect(
                borderRadius: BorderRadius.circular(30.r),
                child: Image.asset(
                  AssetsManager.food_1,
                  width: 80.w,
                  height: 80.h,
                  fit: BoxFit.cover,
                ),
              ),

              SizedBox(width: 10.w),

              /// Title & Subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style:   context.theme.textTheme.labelMedium!.copyWith(
                        color: AppColors.blackTextColor,
                        fontSize: responsiveFont(24).sp,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 5.h),
                    Text(
                      subtitle,
                      style: context.theme.textTheme.labelSmall!.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 20.sp,
                        color: AppColors.blackTextColor,
                      ),
                    ),
                  ],
                ),
              ),

              /// Close Button
              GestureDetector(
                onTap: onClose,
                child:Icon(IconsaxPlusBold.close_circle, color: Colors.red),
              )
            ],
          ),

          SizedBox(height: 26.h),

          /// Uploading text + percent
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Uploading...",
                style: context.theme.textTheme.labelSmall!.copyWith(
                  color: AppColors.textGreyColor,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,)
              ),
              Text(
                "${(progress * 100).toInt()}%",
                style: context.theme.textTheme.labelSmall!.copyWith(
                  color: AppColors.textGreyColor,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,)
              ),
            ],
          ),

          SizedBox(height: 10.h),

          /// Progress Bar
          ClipRRect(
            borderRadius: BorderRadius.circular(4.r),
            child: LinearProgressIndicator(
              value: progress,
              color: AppColors.primaryColor,
              backgroundColor: Colors.grey.withOpacity(0.2),
              minHeight: 10.h,
            ),
          ),

          SizedBox(height: 25.h),

          /// Cancel Uploading Button
          GestureDetector(
            onTap: onCancel,
            child: Text(
              "Cancel uploading",
              style: context.theme.textTheme.labelSmall!.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 20.sp,
                color: AppColors.blackTextColor,
              ),
            ),
          )
        ],
      ),
    );
  }
}
