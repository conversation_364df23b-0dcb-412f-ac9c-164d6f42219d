import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../app/imports/packages_imports.dart';

class CustomSearchBar extends StatelessWidget {
  final VoidCallback? onTap; // In case you want to handle tap on search bar
  final double? height;
  final double? width;
  final TextEditingController controller;
  final String? hintText;
  final bool autoFocus;
  final int maxLines;
  final TextInputType keyboardType;
  final Function(String)? onChanged;
  final List<TextInputFormatter> formats;

  const CustomSearchBar({super.key, this.onTap, this.height, this.width , required this.controller,
    this.hintText,
    this.maxLines = 1,
    this.autoFocus = false,
    this.keyboardType = TextInputType.text,
    this.onChanged,
    this.formats = const [],});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: width ?? 500.w,
          height: height ?? 50.h,
          margin: EdgeInsets.only(left: 20.w, right: 20.w ),
          // padding: EdgeInsets.only(top: 5, bottom: 5),
          decoration: BoxDecoration(
            color: AppColors.lightestGreyColor.withValues(alpha: .2),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child:   TextField(
            maxLines: maxLines,
            controller: controller,
            keyboardType: keyboardType,
            autofocus: autoFocus,
            inputFormatters: formats,
            style: context.theme.textTheme.labelMedium!.copyWith(fontWeight: FontWeight.w400 ,
                fontSize: headingSixFontSize ),
            decoration: InputDecoration(
                contentPadding: EdgeInsets.symmetric(vertical: 15.h), // <-- Add this line

                filled: context.theme.inputDecorationTheme.filled,
                fillColor:AppColors.lightestGreyColor.withOpacity(.6),
                hintText: "Search",
                hintStyle: context.theme.inputDecorationTheme.hintStyle!.copyWith(
                    color: Colors.black.withAlpha(70),
                    fontSize: headingSixFontSize,
                    fontWeight: FontWeight.w400
                ),
                errorStyle: context.theme.inputDecorationTheme.errorStyle,
                //border: context.theme.inputDecorationTheme.border,
                //border: InputBorder.none,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.r),
                  borderSide: BorderSide(color: Colors.transparent),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.r),
                  borderSide: BorderSide(color: AppColors.lightestGreyColor.withOpacity(.2),),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.r),
                  borderSide: BorderSide(color: Colors.transparent),
                ),
                prefixIcon: Icon(Icons.search, color: AppColors.textGreyColor.withOpacity(.3),)
              // suffixIcon: controller.text.isNotEmpty
              //     ? IconButton(
              //   icon: const Icon(Icons.clear, size: 18),
              //   onPressed: () {
              //     controller.clear();
              //   },
              // )
              //     : null,
            ),
            onChanged: (value) {
              if (onChanged != null) {
                onChanged!(value);
              }
            },
          ),
        ),
      ),
    );
  }
}