import 'package:flutter/material.dart';
import 'package:flutter_remix/flutter_remix.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../app/imports/core_imports.dart';
import '../../app/imports/packages_imports.dart';
import '../utils/device_utils.dart';

class CustomTextField extends HookWidget {
  const CustomTextField({
    required this.hintText,
    required this.controller,
    required this.keyboardType,
    this.prefixIcon,
    super.key,
    this.isPassword = false,
    this.autoFocus = true,
    this.isSearchField = false,
    this.capitalize = TextCapitalization.sentences,
    this.formats,
    this.validator,
    this.onChanged,
    this.onClear,
    this.onSubmit,
    this.readOnly = false,
    this.onTap,
    this.fontSize,
    this.isDense = true,
  });

  final double? fontSize;
  final String hintText;
  final TextEditingController controller;
  final IconData? prefixIcon;
  final bool isPassword;
  final bool autoFocus;
  final bool isSearchField;
  final bool readOnly;
  final bool isDense;
  final TextCapitalization capitalize;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? formats;
  final String? Function(String?)? validator;
  final VoidCallback? onClear;
  final Function(String)? onChanged;
  final Function(String)? onSubmit;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final focusNode = useFocusNode();
    final isPasswordVisible = useState(false);
    final showClearIcon = useListenable(focusNode);
    final bool textIsEmpty =
    useListenableSelector(controller, () => controller.text.isEmpty);

    return TextFormField(
      inputFormatters: formats,
      controller: controller,
      readOnly: readOnly,
      focusNode: focusNode,
      obscureText: isPassword ? !isPasswordVisible.value : false, // Simplified
      textCapitalization: capitalize,
      keyboardType: isPassword ? TextInputType.visiblePassword : keyboardType,
      onTapOutside: (_) => focusNode.unfocus(),
      style: context.theme.textTheme.labelMedium!.copyWith(
        color: AppColors.primaryGreyColor,
        fontWeight: FontWeight.w400,
        fontSize: fontSize == 0.0
            ? context.theme.textTheme.displaySmall!.fontSize
            : fontSize,
      ),
      cursorColor: AppColors.blackColor,
      cursorWidth: 2.sp,
      textInputAction:
      isSearchField ? TextInputAction.search : TextInputAction.done,
      textAlignVertical:
      isSearchField ? TextAlignVertical.bottom : TextAlignVertical.center,
      autofocus: autoFocus,
      onTap: onTap,
      decoration: InputDecoration(
        filled: context.theme.inputDecorationTheme.filled,
        fillColor: context.theme.inputDecorationTheme.fillColor,
        hintText: hintText,
        hintStyle: context.theme.inputDecorationTheme.hintStyle!.copyWith(
          fontSize: 20.sp,
        ),
        errorStyle: context.theme.inputDecorationTheme.errorStyle?.copyWith(
          height: 1,
          fontSize: 14.sp,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.r),
          borderSide: const BorderSide(
            color: Colors.grey, // Default border color
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.r),
          borderSide: const BorderSide(
            color: AppColors.primaryGreyColor, // Unfocused border color
            width: 1,
          ),
        ),
        focusedBorder: readOnly
            ? OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.r),
          borderSide: const BorderSide(
            color: Colors.transparent,
          ),
        )
            : OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.r),
          borderSide: const BorderSide(
            color: AppColors.primaryColor, // Focused border color
            width: 2,
          ),
        ),
        focusedErrorBorder:
        context.theme.inputDecorationTheme.focusedErrorBorder ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(16.r),
              borderSide: const BorderSide(
                color: Colors.red, // Error border color
                width: 2,
              ),
            ),
        suffixIconConstraints: const BoxConstraints(
          minHeight: 0,
          minWidth: 20,
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 10.0,
          horizontal: 10.0,
        ),
        prefixIcon: prefixIcon != null
            ? IconTheme(
          data: context.theme.iconTheme
              .copyWith(color: context.theme.iconTheme.color),
          child: Icon(prefixIcon),
        )
            : null,
        suffixIcon: IconTheme(
          data: context.theme.iconTheme
              .copyWith(color: AppColors.primaryHintColor),
          child: Padding(
            padding: const EdgeInsets.only(right: 12),
            child: isPassword
                ? InkWell(
              borderRadius: BorderRadius.circular(20.r),
              onTap: () =>
              isPasswordVisible.value = !isPasswordVisible.value,
              child: isPasswordVisible.value
                  ? Icon(
                FlutterRemix.eye_fill,
                color: context.theme.disabledColor,
                size: 25.sp,
              )
                  : Icon(
                FlutterRemix.eye_off_fill,
                size: 25.sp,
                color: context.theme.disabledColor,
              ),
            )
                : showClearIcon.hasPrimaryFocus && !textIsEmpty
                ? InkWell(
              onTap: () {
                controller.clear();
                if (onClear != null) {
                  onClear!();
                }
              },
              child: Icon(
                FlutterRemix.close_circle_fill,
                color: context.theme.disabledColor,
                size: 25.sp,
              ),
            )
                : const SizedBox.shrink(),
          ),
        ),
        isDense: isDense,
      ),
      validator: validator,
      onChanged: (value) {
        if (onChanged != null) {
          onChanged!(value);
        }
      },
      onFieldSubmitted: onSubmit,
    );
  }
}