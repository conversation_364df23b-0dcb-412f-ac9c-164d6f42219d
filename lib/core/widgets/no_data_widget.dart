import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import '../../app/theme/colors.dart';

class NoDataWidget extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final double? width;
  final double? height;
  final bool showAnimation;

  const NoDataWidget({
    super.key,
    this.title,
    this.subtitle,
    this.width,
    this.height,
    this.showAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showAnimation)
              Lottie.asset(
                'assets/NoData.json',
                width: width ?? 300.w,
                height: height ?? 300.h,
                fit: BoxFit.contain,
                repeat: true,
                animate: true,
              ),
            if (title != null) ...[
              SizedBox(height: 10.h),
              Text(
                title!,
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color.fromARGB(255, 228, 223, 223),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (subtitle != null) ...[
              SizedBox(height: 8.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Text(
                  subtitle!,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromARGB(255, 196, 191, 191),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
