import '../../app/imports/core_imports.dart';
import '../../app/imports/packages_imports.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onPressed;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      centerTitle: false,
      elevation: 2,
      titleSpacing: 0,
      automaticallyImplyLeading: !kIsWeb,
      leading: kIsWeb
          ? null
          : IconButton(
              icon: const Icon(Icons.arrow_back_ios_sharp),
        onPressed: onPressed ??
                () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                // You can fallback to GoRouter navigation if needed
                context.go(Routes.home);
              }
            },
      ),
      title: Padding(
        padding: EdgeInsets.only(left: kIsWeb ? 16.0 : 0.0),
        child: Text(
          title,
          style: context.theme.textTheme.bodyMedium!.copyWith(
            color: AppColors.texGreyColor,
            fontWeight: FontWeight.w500,
            fontFamily: 'Inter',
            fontSize: 30.sp,
          ),
        ),
      ),
      actions: actions ?? [

      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
