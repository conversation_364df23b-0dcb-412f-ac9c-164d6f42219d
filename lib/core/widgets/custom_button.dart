import '../../app/imports/core_imports.dart';
import '../../app/imports/packages_imports.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final Color? color;
  final IconData? icon;
  final Color? iconColor;
  final Color? textColor;
  final VoidCallback onPressed;
  final double? width;
  final double? height;
  final double? fontSize;
  final double? iconSize;
  final double? borderRadius;
  final bool? hasBorder;
  final bool? isLoading;
  final bool? isDisabled;
  final bool? isUnderline;
  final String fontFamily;

  const CustomButton({
    super.key,
    required this.text,
    this.color,
    this.icon,
    this.iconColor,
    this.textColor,
    required this.onPressed,
    this.width,
    this.height,
    this.fontSize,
    this.iconSize,
    this.borderRadius,
    this.hasBorder,
    this.isLoading,
    this.isDisabled,
    this.isUnderline = false,
    this.fontFamily = 'Inter',
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: hasBorder == true
            ? context.theme.scaffoldBackgroundColor
            : color ?? context.theme.primaryColor,
        shape: hasBorder == true
            ? RoundedRectangleBorder(
                side: BorderSide(
                  color: color ?? context.theme.iconTheme.color!,
                ),
                borderRadius: BorderRadius.circular(
                  borderRadius ?? 10.r,
                ),
              )
            : RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? 10.r,
                ),
              ),
        fixedSize: Size(
          width ?? context.width,
          height ?? 70.h,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 0.0,
        ),
        elevation: 0,
        shadowColor: Colors.transparent,
      ),
      onPressed: isDisabled == true || isLoading == true ? () {} : onPressed,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        child: isLoading == true
            ? const LoadingIndicator(
                key: ValueKey('loading'),
                size: 16,
                strokeWidth: 3,
                color: Colors.white,
              )
            : Row(
                key: const ValueKey('content'),
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null)
                    Icon(
                      icon,
                      color: iconColor ?? Colors.white,
                      size: iconSize ?? 24,
                    ),
                  if (icon != null) SizedBox(width: 15.w),
                  Text(
                    text,
                    style: context.textTheme.titleMedium?.copyWith(
                      color: textColor ?? AppColors.primaryHintColor,
                      fontSize: fontSize ?? headingFourFontSize,
                      fontWeight: FontWeight.w300,
                      fontFamily: fontFamily,
                      decoration: isUnderline == true
                          ? TextDecoration.underline
                          : TextDecoration.none,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
