import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mastercookai/app/routes/routes.dart';
import 'package:mastercookai/app/theme/light_theme.dart';
import 'package:mastercookai/core/helpers/CustomShortEnglishMessages.dart';
import 'package:mastercookai/presentation/start_up_error_screen.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'app/imports/packages_imports.dart';
import 'core/helpers/app_constant.dart';
import 'core/network/app_repository.dart';
import 'package:flutter/foundation.dart';

//<EMAIL>/test0$007
//http://127.0.0.1:57894/#/login
final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

final providerContainer = ProviderContainer(
  observers: [],
);

void main() async {
  timeago.setLocaleMessages('en_short', CustomShortEnglishMessages());
  WidgetsFlutterBinding.ensureInitialized();
  // await dotenv.load(fileName: "..env");
  baseURL = "https://api.mastercook.ai/"; //dotenv.env['BASE_URL']??'';
  await ScreenUtil.ensureScreenSize();
  // Check internet connection before proceeding
  final hasConnection = await providerContainer
      .read(appRepositoryProvider)
      .hasInternetConnection();

  if (!hasConnection) {
    runApp(
      UncontrolledProviderScope(
        container: providerContainer,
        child: const StartUpErrorScreen(),
      ),
    );
    return;
  }

  runApp(ProviderScope(child: MyApp()));
}

Size getFixedDesktopDesignSize(BuildContext context) {
  final width = MediaQuery.of(context).size.width;
  if (width > 2560) return const Size(2560, 1440);
  if (width > 1920) return const Size(1920, 1080);
  return const Size(1440, 900);
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(goRouterProvider);
    ScreenUtil.init(context, minTextAdapt: true, splitScreenMode: true);
    // Get dynamic design size
    final designSize = getFixedDesktopDesignSize(context);
    return ScreenUtilInit(
      designSize: Size(2500, 1600),
      minTextAdapt: true, // Enable text adaptation
      splitScreenMode: true,
      child: MaterialApp.router(
        key: navigatorKey,
        builder: FlutterSmartDialog.init(),
        scaffoldMessengerKey: scaffoldMessengerKey,
        title: 'MasterCook AI',
        debugShowCheckedModeBanner: false,
        supportedLocales: const [
          Locale('en'), // English, no country code
        ],
        theme: lightTheme,
        routerConfig: router,
      ),
    );
  }
}
